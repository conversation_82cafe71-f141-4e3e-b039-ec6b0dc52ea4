
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import PersianDatePicker from "@/components/common/PersianDatePicker";
import MoneyInput from "@/components/ui/money-input";
import { Plus } from "lucide-react";
import { FacilityData } from "@/types/facility";

interface FacilityFormFieldsProps {
  formData: FacilityData;
  onUpdateFormData: (data: Partial<FacilityData>) => void;
  onAddInstallments: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

const facilityTypes = [
  "مسکن",
  "خودرو", 
  "کارگشایی",
  "ودیعه مسکن",
  "جاری",
  "تولیدی",
  "خرد",
  "تحصیلی",
  "سایر"
];

const banks = [
  "بانک ملی ایران",
  "بانک صادرات ایران",
  "بانک تجارت",
  "بانک کشاورزی",
  "بانک صنعت و معدن",
  "بانک رفاه کارگران",
  "بانک توسعه تعاون",
  "بانک پست بانک",
  "بانک دی",
  "بانک پارسیان",
  "بانک پاسارگاد",
  "بانک کارآفرین",
  "سایر"
];

const FacilityFormFields = ({ formData, onUpdateFormData, onAddInstallments }: FacilityFormFieldsProps) => {
  // Filter out any empty or invalid facility types and banks
  const validFacilityTypes = facilityTypes.filter(type => type && type.trim() !== "");
  const validBanks = banks.filter(bank => bank && bank.trim() !== "");


  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* نوع تسهیلات */}
      <div className="space-y-2">
        <Label htmlFor="facilityType">نوع تسهیلات *</Label>
        <Select 
          value={formData.facilityType || ""} 
          onValueChange={(value) => onUpdateFormData({facilityType: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="انتخاب کنید" />
          </SelectTrigger>
          <SelectContent>
            {validFacilityTypes.map((type, index) => {
              const key = `facility_type_${index}`;
              return (
                <SelectItem key={key} value={type}>
                  {type}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>

      {/* مبلغ تسهیلات با فرمت مبلغ */}
      <div className="space-y-2">
        <Label htmlFor="amount">مبلغ تسهیلات (ریال) *</Label>
        <MoneyInput
          id="amount"
          value={formData.amount}
          onChange={(value) => onUpdateFormData({amount: value})}
          placeholder="مثال: *********"
        />
      </div>

      {/* تاریخ اخذ */}
      <PersianDatePicker
        label="تاریخ اخذ"
        value={formData.receivedDate}
        onChange={(value) => onUpdateFormData({ receivedDate: value })}
        required
      />

      {/* نام بانک */}
      <div className="space-y-2">
        <Label htmlFor="bankName">نام بانک *</Label>
        <Select 
          value={formData.bankName || ""} 
          onValueChange={(value) => onUpdateFormData({bankName: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="انتخاب کنید" />
          </SelectTrigger>
          <SelectContent>
            {validBanks.map((bank, index) => {
              const key = `bank_${index}`;
              return (
                <SelectItem key={key} value={bank}>
                  {bank}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>

      {/* شماره تسهیلات */}
      <div className="space-y-2">
        <Label htmlFor="facilityNumber">شماره تسهیلات *</Label>
        <Input
          id="facilityNumber"
          placeholder="شماره قرارداد تسهیلات"
          value={formData.facilityNumber}
          onChange={(e) => onUpdateFormData({facilityNumber: e.target.value})}
        />
      </div>

      {/* تعداد اقساط */}
      <div className="space-y-2">
        <Label htmlFor="installmentCount">تعداد اقساط *</Label>
        <div className="flex gap-2">
          <Input
            id="installmentCount"
            type="number"
            placeholder="مثال: 60"
            value={formData.installmentCount}
            onChange={(e) => onUpdateFormData({installmentCount: e.target.value})}
            className="flex-1"
          />
          {formData.installmentCount && (
            <Button
              type="button"
              variant="outline"
              onClick={onAddInstallments}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              افزودن اقساط
            </Button>
          )}
        </div>
      </div>

      {/* نرخ سود */}
      <div className="space-y-2">
        <Label htmlFor="interestRate">نرخ سود (درصد) *</Label>
        <Input
          id="interestRate"
          type="number"
          step="0.1"
          placeholder="مثال: 18"
          value={formData.interestRate}
          onChange={(e) => onUpdateFormData({interestRate: e.target.value})}
        />
      </div>

      {/* نرخ جریمه */}
      <div className="space-y-2">
        <Label htmlFor="penaltyRate">نرخ جریمه (درصد)</Label>
        <Input
          id="penaltyRate"
          type="number"
          step="0.1"
          placeholder="مثال: 6"
          value={formData.penaltyRate}
          onChange={(e) => onUpdateFormData({penaltyRate: e.target.value})}
        />
      </div>

      {/* مبلغ هر قسط با فرمت مبلغ */}
      <div className="space-y-2">
        <Label htmlFor="installmentAmount">مبلغ هر قسط (ریال)</Label>
        <MoneyInput
          id="installmentAmount"
          value={formData.installmentAmount}
          onChange={(value) => onUpdateFormData({installmentAmount: value})}
          placeholder="مثال: 15000000"
        />
      </div>

      {/* تاریخ اولین قسط */}
      <PersianDatePicker
        label="تاریخ اولین قسط"
        value={formData.firstInstallmentDate}
        onChange={(value) => onUpdateFormData({ firstInstallmentDate: value })}
      />
    </div>
  );
};

export default FacilityFormFields;
