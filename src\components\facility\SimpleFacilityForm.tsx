import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import SearchableSelect from "@/components/common/SearchableSelect";
import ManageListDialog from "@/components/common/ManageListDialog";
import SimpleInput from "@/components/common/SimpleInput";
import PersianDatePicker from "@/components/common/PersianDatePicker";
import { Input } from "@/components/ui/input";
import CurrencyInput from "@/components/common/CurrencyInput";
import InstallmentTable from "@/components/facility/InstallmentTable";
import { Installment } from "@/types/facility";
import {
  addFacility,
  updateFacility,
  getBankNames,
  saveBankNames,
  getFacilityTypes,
  saveFacilityTypes,
} from "@/utils/storage";
import { FacilityFormData, Facility } from "@/types/types";
import {
  Building2,
  <PERSON>R<PERSON>,
  Plus,
  Save,
  Edit3,
  <PERSON>Sign,
  Calendar,
  FileText,
  Settings,
  Sparkles
} from "lucide-react";

interface SimpleFacilityFormProps {
  onBack: () => void;
  onSuccess?: () => void;
  facility?: Facility;
}

const SimpleFacilityForm = ({
  onBack,
  onSuccess,
  facility,
}: SimpleFacilityFormProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FacilityFormData>(() => ({
    type: facility?.type || "",
    amount: facility?.amount?.toString() || "",
    bankName: facility?.bankName || "",
    contractNumber: facility?.contractNumber || "",
    installmentCount: facility?.installmentCount?.toString() || "",
    interestRate: facility?.interestRate?.toString() || "",
    penaltyRate: facility?.penaltyRate?.toString() || "",
    installmentAmount: facility?.installmentAmount?.toString() || "",
    receivedDate: facility?.receivedDate || "",
    firstInstallmentDate: facility?.firstInstallmentDate || "",
    commissionAmount: facility?.commissionAmount?.toString() || "",
    otherAmount: facility?.otherAmount?.toString() || "",
    description: facility?.description || "",
  }));

  const [installments, setInstallments] = useState<Installment[]>(() => facility?.installments || []);
  const [showInstallments, setShowInstallments] = useState(() => (facility?.installments?.length ?? 0) > 0);
  const [bankOptions, setBankOptions] = useState<string[]>(getBankNames());
  const [typeOptions, setTypeOptions] = useState<string[]>(getFacilityTypes());
  const [bankDialog, setBankDialog] = useState(false);
  const [typeDialog, setTypeDialog] = useState(false);

  const updateField = (field: keyof FacilityFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddInstallments = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const count = parseInt(formData.installmentCount);
    if (count > 0) {
      const list: Installment[] = Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        dueDate: "",
        principalAmount: "",
        interestAmount: "",
      }));
      setInstallments(list);
      setShowInstallments(true);
    }
  };

  const updateInstallment = (
    id: number,
    field: keyof Installment,
    value: string
  ) => {
    setInstallments((prev) =>
      prev.map((inst) => (inst.id === id ? { ...inst, [field]: value } : inst))
    );
  };

  const handleBankListChange = (list: string[]) => {
    setBankOptions(list);
    saveBankNames(list);
  };

  const handleTypeListChange = (list: string[]) => {
    setTypeOptions(list);
    saveFacilityTypes(list);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const requiredFields = [
      "type",
      "amount",
      "bankName",
      "contractNumber",
      "installmentCount",
      "interestRate",
    ];
    const missing = requiredFields.filter(
      (field) => !formData[field as keyof FacilityFormData]
    );

    if (missing.length > 0) {
      toast({
        title: "خطا",
        description: "لطفاً تمام فیلدهای ضروری را پر کنید",
        variant: "destructive",
      });
      return;
    }

    const totalPrincipal = installments.reduce(
      (sum, i) => sum + (parseInt(i.principalAmount) || 0),
      0
    );
    if (showInstallments && totalPrincipal !== parseInt(formData.amount)) {
      toast({
        title: "خطا",
        description: "جمع مبالغ اصل اقساط با مبلغ تسهیلات برابر نیست",
        variant: "destructive",
      });
      return;
    }

    try {
      const data = {
        type: formData.type,
        amount: parseInt(formData.amount),
        bankName: formData.bankName,
        contractNumber: formData.contractNumber,
        installmentCount: parseInt(formData.installmentCount),
        interestRate: parseFloat(formData.interestRate),
        penaltyRate: parseFloat(formData.penaltyRate || "0"),
        installmentAmount:
          parseInt(formData.installmentCount || "1") === 0
            ? 0
            : parseInt(formData.amount) / parseInt(formData.installmentCount || "1"),
        receivedDate: formData.receivedDate,
        firstInstallmentDate: formData.firstInstallmentDate,
        commissionAmount: parseInt(formData.commissionAmount || "0"),
        otherAmount: parseInt(formData.otherAmount || "0"),
        description: formData.description,
        installments,
      };

      if (facility) {
        updateFacility(facility.id, data);
        toast({
          title: "موفقیت",
          description: "تسهیلات با موفقیت ویرایش شد",
        });
      } else {
        addFacility(data);
        toast({
          title: "موفقیت",
          description: "تسهیلات با موفقیت ثبت شد",
        });
      }

      if (onSuccess) onSuccess();
      else onBack();
    } catch {
      toast({
        title: "خطا",
        description: "خطا در ثبت تسهیلات",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6 space-y-8 relative z-[9999] overflow-visible">
      {/* Enhanced Header */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-xl shadow-lg">
              {facility ? <Edit3 className="h-8 w-8 text-white" /> : <Building2 className="h-8 w-8 text-white" />}
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                {facility ? "ویرایش تسهیلات" : "ثبت تسهیلات جدید"}
              </h1>
              <p className="text-slate-500 mt-1">
                {facility ? "ویرایش اطلاعات تسهیلات موجود" : "افزودن تسهیلات جدید به سیستم"}
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2 bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
            >
              <ArrowRight className="h-4 w-4" />
              لغو
            </Button>
            <Button
              type="submit"
              form="facility-form"
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2"
            >
              {facility ? (
                <>
                  <Save className="h-4 w-4" />
                  ذخیره تغییرات
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" />
                  ثبت تسهیلات
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-slate-200/60">
          <CardTitle className="text-xl flex items-center gap-3">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-lg shadow-md">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <span className="bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
              اطلاعات تسهیلات
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8 overflow-visible">
          <form id="facility-form" onSubmit={handleSubmit} className="space-y-8 grid grid-cols-1 gap-6">
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <div className="flex items-end gap-3">
                <div className="flex-1">
                  <SearchableSelect
                    label="نوع تسهیلات"
                    value={formData.type}
                    onValueChange={(value) => updateField("type", value)}
                    options={typeOptions}
                    required
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setTypeDialog(true)}
                  className="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  افزودن نوع
                </Button>
              </div>
              <CurrencyInput
                label="مبلغ تسهیلات (ریال)"
                value={formData.amount}
                onChange={(value) => updateField("amount", value)}
                required
              />
              <div className="flex items-end gap-3">
                <div className="flex-1">
                  <SearchableSelect
                    label="نام بانک"
                    value={formData.bankName}
                    onValueChange={(value) => updateField("bankName", value)}
                    options={bankOptions}
                    required
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setBankDialog(true)}
                  className="bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-200 shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  افزودن بانک
                </Button>
              </div>
              <SimpleInput
                label="شماره قرارداد"
                value={formData.contractNumber}
                onChange={(value) => updateField("contractNumber", value)}
                required
              />
              <SimpleInput
                label="تعداد اقساط"
                value={formData.installmentCount}
                onChange={(value) => updateField("installmentCount", value)}
                type="number"
                required
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleAddInstallments}
                className="bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-indigo-700 hover:from-indigo-100 hover:to-purple-100 hover:border-indigo-300 transition-all duration-200 shadow-sm flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                تولید اقساط
              </Button>
              <SimpleInput
                label="نرخ سود (درصد)"
                value={formData.interestRate}
                onChange={(value) => updateField("interestRate", value)}
                type="number"
                step="0.1"
                required
              />
              <SimpleInput
                label="نرخ جریمه (درصد)"
                value={formData.penaltyRate}
                onChange={(value) => updateField("penaltyRate", value)}
                type="number"
                step="0.1"
              />
              <PersianDatePicker
                label="تاریخ اخذ"
                value={formData.receivedDate}
                onChange={(value) => updateField("receivedDate", value)}
                required
              />
              <PersianDatePicker
                label="تاریخ اولین قسط"
                value={formData.firstInstallmentDate}
                onChange={(value) => updateField("firstInstallmentDate", value)}
              />
              <CurrencyInput
                label="مبلغ کارمزد (ریال)"
                value={formData.commissionAmount}
                onChange={(value) => updateField("commissionAmount", value)}
              />
              <CurrencyInput
                label="مبلغ سایر (ریال)"
                value={formData.otherAmount}
                onChange={(value) => updateField("otherAmount", value)}
              />
              <div className="xl:col-span-2">
                <SimpleInput
                  label="توضیحات"
                  value={formData.description}
                  onChange={(value) => updateField("description", value)}
                />
              </div>
            </div>

            {showInstallments && installments.length > 0 && (
              <InstallmentTable installments={installments} onUpdateInstallment={updateInstallment} />
            )}


          </form>
        </CardContent>
      </Card>

      {/* دیالوگ‌های مدیریت لیست */}
      <ManageListDialog
        open={bankDialog}
        onOpenChange={setBankDialog}
        items={bankOptions}
        onChange={handleBankListChange}
        title="مدیریت بانک‌ها"
      />
      
      <ManageListDialog
        open={typeDialog}
        onOpenChange={setTypeDialog}
        items={typeOptions}
        onChange={handleTypeListChange}
        title="مدیریت نوع تسهیلات"
      />
    </div>
  );
};

export default SimpleFacilityForm;