import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import SearchableSelect from "@/components/common/SearchableSelect";
import ManageListDialog from "@/components/common/ManageListDialog";
import SimpleInput from "@/components/common/SimpleInput";
import PersianDatePicker from "@/components/common/PersianDatePicker";
import { Input } from "@/components/ui/input";
import CurrencyInput from "@/components/common/CurrencyInput";
import InstallmentTable from "@/components/facility/InstallmentTable";
import { Installment } from "@/types/facility";
import {
  addFacility,
  updateFacility,
  getBankNames,
  saveBankNames,
  getFacilityTypes,
  saveFacilityTypes,
} from "@/utils/storage";
import { FacilityFormData, Facility } from "@/types/types";

interface SimpleFacilityFormProps {
  onBack: () => void;
  onSuccess?: () => void;
  facility?: Facility;
}

const SimpleFacilityForm = ({
  onBack,
  onSuccess,
  facility,
}: SimpleFacilityFormProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FacilityFormData>(() => ({
    type: facility?.type || "",
    amount: facility?.amount?.toString() || "",
    bankName: facility?.bankName || "",
    contractNumber: facility?.contractNumber || "",
    installmentCount: facility?.installmentCount?.toString() || "",
    interestRate: facility?.interestRate?.toString() || "",
    penaltyRate: facility?.penaltyRate?.toString() || "",
    installmentAmount: facility?.installmentAmount?.toString() || "",
    receivedDate: facility?.receivedDate || "",
    firstInstallmentDate: facility?.firstInstallmentDate || "",
    commissionAmount: facility?.commissionAmount?.toString() || "",
    otherAmount: facility?.otherAmount?.toString() || "",
    description: facility?.description || "",
  }));

  const [installments, setInstallments] = useState<Installment[]>(() => facility?.installments || []);
  const [showInstallments, setShowInstallments] = useState(() => (facility?.installments?.length ?? 0) > 0);
  const [bankOptions, setBankOptions] = useState<string[]>(getBankNames());
  const [typeOptions, setTypeOptions] = useState<string[]>(getFacilityTypes());
  const [bankDialog, setBankDialog] = useState(false);
  const [typeDialog, setTypeDialog] = useState(false);

  const updateField = (field: keyof FacilityFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddInstallments = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const count = parseInt(formData.installmentCount);
    if (count > 0) {
      const list: Installment[] = Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        dueDate: "",
        principalAmount: "",
        interestAmount: "",
      }));
      setInstallments(list);
      setShowInstallments(true);
    }
  };

  const updateInstallment = (
    id: number,
    field: keyof Installment,
    value: string
  ) => {
    setInstallments((prev) =>
      prev.map((inst) => (inst.id === id ? { ...inst, [field]: value } : inst))
    );
  };

  const handleBankListChange = (list: string[]) => {
    setBankOptions(list);
    saveBankNames(list);
  };

  const handleTypeListChange = (list: string[]) => {
    setTypeOptions(list);
    saveFacilityTypes(list);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const requiredFields = [
      "type",
      "amount",
      "bankName",
      "contractNumber",
      "installmentCount",
      "interestRate",
    ];
    const missing = requiredFields.filter(
      (field) => !formData[field as keyof FacilityFormData]
    );

    if (missing.length > 0) {
      toast({
        title: "خطا",
        description: "لطفاً تمام فیلدهای ضروری را پر کنید",
        variant: "destructive",
      });
      return;
    }

    const totalPrincipal = installments.reduce(
      (sum, i) => sum + (parseInt(i.principalAmount) || 0),
      0
    );
    if (showInstallments && totalPrincipal !== parseInt(formData.amount)) {
      toast({
        title: "خطا",
        description: "جمع مبالغ اصل اقساط با مبلغ تسهیلات برابر نیست",
        variant: "destructive",
      });
      return;
    }

    try {
      const data = {
        type: formData.type,
        amount: parseInt(formData.amount),
        bankName: formData.bankName,
        contractNumber: formData.contractNumber,
        installmentCount: parseInt(formData.installmentCount),
        interestRate: parseFloat(formData.interestRate),
        penaltyRate: parseFloat(formData.penaltyRate || "0"),
        installmentAmount:
          parseInt(formData.installmentCount || "1") === 0
            ? 0
            : parseInt(formData.amount) / parseInt(formData.installmentCount || "1"),
        receivedDate: formData.receivedDate,
        firstInstallmentDate: formData.firstInstallmentDate,
        commissionAmount: parseInt(formData.commissionAmount || "0"),
        otherAmount: parseInt(formData.otherAmount || "0"),
        description: formData.description,
        installments,
      };

      if (facility) {
        updateFacility(facility.id, data);
        toast({
          title: "موفقیت",
          description: "تسهیلات با موفقیت ویرایش شد",
        });
      } else {
        addFacility(data);
        toast({
          title: "موفقیت",
          description: "تسهیلات با موفقیت ثبت شد",
        });
      }

      if (onSuccess) onSuccess();
      else onBack();
    } catch {
      toast({
        title: "خطا",
        description: "خطا در ثبت تسهیلات",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6 relative z-[9999] overflow-visible">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">{facility ? "ویرایش تسهیلات" : "ثبت تسهیلات جدید"}</h2>
        <Button variant="outline" onClick={onBack}>بازگشت</Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>اطلاعات تسهیلات</CardTitle>
        </CardHeader>
        <CardContent className="overflow-visible">
          <form onSubmit={handleSubmit} className="space-y-6 grid grid-cols-1 gap-4">
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <SearchableSelect
                    label="نوع تسهیلات"
                    value={formData.type}
                    onValueChange={(value) => updateField("type", value)}
                    options={typeOptions}
                    required
                  />
                </div>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setTypeDialog(true)}
                >
                  افزودن نوع
                </Button>
              </div>
              <CurrencyInput
                label="مبلغ تسهیلات (ریال)"
                value={formData.amount}
                onChange={(value) => updateField("amount", value)}
                required
              />
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <SearchableSelect
                    label="نام بانک"
                    value={formData.bankName}
                    onValueChange={(value) => updateField("bankName", value)}
                    options={bankOptions}
                    required
                  />
                </div>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setBankDialog(true)}
                >
                  افزودن بانک
                </Button>
              </div>
              <SimpleInput
                label="شماره قرارداد"
                value={formData.contractNumber}
                onChange={(value) => updateField("contractNumber", value)}
                required
              />
              <SimpleInput
                label="تعداد اقساط"
                value={formData.installmentCount}
                onChange={(value) => updateField("installmentCount", value)}
                type="number"
                required
              />
              <Button type="button" variant="outline" onClick={handleAddInstallments}>
                تولید اقساط
              </Button>
              <SimpleInput
                label="نرخ سود (درصد)"
                value={formData.interestRate}
                onChange={(value) => updateField("interestRate", value)}
                type="number"
                step="0.1"
                required
              />
              <SimpleInput
                label="نرخ جریمه (درصد)"
                value={formData.penaltyRate}
                onChange={(value) => updateField("penaltyRate", value)}
                type="number"
                step="0.1"
              />
              <PersianDatePicker
                label="تاریخ اخذ"
                value={formData.receivedDate}
                onChange={(value) => updateField("receivedDate", value)}
                required
              />
              <PersianDatePicker
                label="تاریخ اولین قسط"
                value={formData.firstInstallmentDate}
                onChange={(value) => updateField("firstInstallmentDate", value)}
              />
              <CurrencyInput
                label="مبلغ کارمزد (ریال)"
                value={formData.commissionAmount}
                onChange={(value) => updateField("commissionAmount", value)}
              />
              <CurrencyInput
                label="مبلغ سایر (ریال)"
                value={formData.otherAmount}
                onChange={(value) => updateField("otherAmount", value)}
              />
              <div className="xl:col-span-2">
                <SimpleInput
                  label="توضیحات"
                  value={formData.description}
                  onChange={(value) => updateField("description", value)}
                />
              </div>
            </div>

            {showInstallments && installments.length > 0 && (
              <InstallmentTable installments={installments} onUpdateInstallment={updateInstallment} />
            )}

            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={onBack}>لغو</Button>
              <Button type="submit">{facility ? "ذخیره تغییرات" : "ثبت تسهیلات"}</Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* دیالوگ‌های مدیریت لیست */}
      <ManageListDialog
        open={bankDialog}
        onOpenChange={setBankDialog}
        items={bankOptions}
        onChange={handleBankListChange}
        title="مدیریت بانک‌ها"
      />
      
      <ManageListDialog
        open={typeDialog}
        onOpenChange={setTypeDialog}
        items={typeOptions}
        onChange={handleTypeListChange}
        title="مدیریت نوع تسهیلات"
      />
    </div>
  );
};

export default SimpleFacilityForm;