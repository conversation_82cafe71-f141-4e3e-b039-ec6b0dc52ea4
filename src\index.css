@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Vazirmatn', sans-serif;
    direction: rtl;
  }
}

/* Custom styles for RTL layout */
.space-x-reverse > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* Custom animations for better UX */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Persian Date Picker Styles */
.rmdp-container {
  z-index: 9999 !important;
}

.rmdp-calendar {
  z-index: 9999 !important;
}

.rmdp-shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.rmdp-day.rmdp-selected span:not(.highlight) {
  background-color: var(--primary) !important;
  box-shadow: 0 0 5px var(--primary) !important;
}

.rmdp-day:not(.rmdp-disabled):not(.rmdp-day-hidden) span:hover {
  background-color: var(--accent) !important;
}

.rmdp-week-day {
  color: var(--primary) !important;
}

.rmdp-arrow {
  border: solid var(--primary) !important;
  border-width: 0 2px 2px 0 !important;
}

/* ----------- 5 MODERN & TRENDY COLOR THEMES FOR 2025 ----------- */

/* Theme 1: Dark Mode - Pure Black with Light Gray Text and Yellow Icons */
.theme1 {
  --primary: 0 0% 90%;                   /* Light gray text (#e5e5e5) */
  --primary-foreground: 0 0% 0%;         /* Pure black for contrast */
  --background: 0 0% 4%;                 /* Very dark black (#0a0a0a) */
  --foreground: 0 0% 94%;                /* Light gray text (#f0f0f0) */
  --card: 0 0% 0%;                       /* Pure black cards */
  --card-foreground: 0 0% 90%;           /* Light gray text on cards */
  --popover: 0 0% 0%;                    /* Pure black popover */
  --popover-foreground: 0 0% 90%;        /* Light gray text on popover */
  --secondary: 0 0% 6%;                  /* Slightly lighter black */
  --secondary-foreground: 0 0% 90%;      /* Light gray text on secondary */
  --muted: 0 0% 8%;                      /* Muted dark background */
  --muted-foreground: 0 0% 75%;          /* Muted light gray text */
  --accent: 0 0% 6%;                     /* Accent dark background */
  --accent-foreground: 0 0% 90%;         /* Light gray text on accent */
  --destructive: 0 70% 50%;              /* Red for destructive actions */
  --destructive-foreground: 0 0% 100%;   /* White text on destructive */
  --border: 0 0% 15%;                    /* Dark gray border */
  --input: 0 0% 6%;                      /* Dark input background */
  --ring: 51 100% 50%;                   /* Yellow ring for focus (#ffcc00) */
  --sidebar-background: 0 0% 0%;         /* Pure black sidebar */
  --sidebar-foreground: 0 0% 90%;        /* Light gray sidebar text */
}

/* Theme 2: Happy Mode - Pure Solid Colors (No Gradients) */
.theme2 {
  --primary: 0 100% 50%;                 /* Pure Red (#ff0000) */
  --primary-foreground: 0 0% 100%;       /* White text on red */
  --background: 180 100% 50%;            /* Pure Turquoise (#00ffff) */
  --foreground: 0 0% 0%;                 /* Black text */
  --card: 60 100% 50%;                   /* Pure Yellow (#ffff00) */
  --card-foreground: 0 0% 0%;            /* Black text on yellow */
  --popover: 120 100% 50%;               /* Pure Green (#00ff00) */
  --popover-foreground: 0 0% 0%;         /* Black text on green */
  --secondary: 270 100% 50%;             /* Pure Purple (#8000ff) */
  --secondary-foreground: 0 0% 100%;     /* White text on purple */
  --muted: 30 100% 50%;                  /* Pure Orange (#ff8000) */
  --muted-foreground: 0 0% 0%;           /* Black text on orange */
  --accent: 300 100% 50%;                /* Pure Pink (#ff00ff) */
  --accent-foreground: 0 0% 0%;          /* Black text on pink */
  --destructive: 0 100% 50%;             /* Pure Red for destructive */
  --destructive-foreground: 0 0% 100%;   /* White text on destructive */
  --border: 240 100% 50%;                /* Pure Blue borders (#0000ff) */
  --input: 120 100% 50%;                 /* Pure Green inputs */
  --ring: 0 100% 50%;                    /* Red ring for focus */
  --sidebar-background: 240 100% 50%;    /* Pure Blue sidebar (#0000ff) */
  --sidebar-foreground: 0 0% 100%;       /* White sidebar text */
}

/* Complete Dark Mode Overrides for Theme 1 */
.theme1 * {
  color-scheme: dark;
}

/* Background Colors - Pure Black */
.theme1 .bg-white,
.theme1 .bg-slate-50,
.theme1 .bg-slate-100,
.theme1 .bg-gray-50,
.theme1 .bg-gray-100,
.theme1 .bg-blue-50,
.theme1 .bg-indigo-50,
.theme1 .bg-emerald-50,
.theme1 .bg-teal-50,
.theme1 .bg-purple-50,
.theme1 .bg-pink-50 {
  background-color: #000000 !important;
}

.theme1 .bg-gradient-to-br,
.theme1 .bg-gradient-to-r {
  background: #0a0a0a !important;
}

/* Text Colors - Light Gray */
.theme1 .text-slate-800,
.theme1 .text-slate-700,
.theme1 .text-slate-600,
.theme1 .text-slate-500,
.theme1 .text-gray-900,
.theme1 .text-gray-800,
.theme1 .text-gray-700,
.theme1 .text-gray-600,
.theme1 .text-gray-500,
.theme1 .text-black,
.theme1 h1,
.theme1 h2,
.theme1 h3,
.theme1 h4,
.theme1 h5,
.theme1 h6,
.theme1 p,
.theme1 span,
.theme1 div,
.theme1 label {
  color: #e5e5e5 !important;
}

/* Borders - Dark Gray */
.theme1 .border-slate-200,
.theme1 .border-slate-300,
.theme1 .border-gray-200,
.theme1 .border-gray-300,
.theme1 .border-blue-200 {
  border-color: #262626 !important;
}

/* Icons - Caterpillar Yellow */
.theme1 svg,
.theme1 .lucide,
.theme1 [data-lucide] {
  color: #ffcc00 !important;
  stroke: #ffcc00 !important;
}

/* Sidebar Specific Overrides */
.theme1 aside {
  background-color: #000000 !important;
  border-color: #262626 !important;
}

.theme1 aside * {
  color: #e5e5e5 !important;
}

.theme1 aside svg {
  color: #ffcc00 !important;
  stroke: #ffcc00 !important;
}

/* Form and Input Overrides */
.theme1 input,
.theme1 textarea,
.theme1 select {
  background-color: #0a0a0a !important;
  color: #e5e5e5 !important;
  border-color: #262626 !important;
}

.theme1 input::placeholder,
.theme1 textarea::placeholder {
  color: #888888 !important;
}

/* Button Overrides */
.theme1 button {
  background-color: #0a0a0a !important;
  color: #e5e5e5 !important;
  border-color: #262626 !important;
}

.theme1 button:hover {
  background-color: #1a1a1a !important;
}

.theme1 button svg {
  color: #ffcc00 !important;
  stroke: #ffcc00 !important;
}

/* Card and Container Overrides */
.theme1 .card,
.theme1 [role="dialog"],
.theme1 [data-radix-popper-content-wrapper] {
  background-color: #000000 !important;
  border-color: #262626 !important;
}

/* Table Overrides */
.theme1 table,
.theme1 thead,
.theme1 tbody,
.theme1 tr,
.theme1 td,
.theme1 th {
  background-color: #000000 !important;
  color: #e5e5e5 !important;
  border-color: #262626 !important;
}

/* Dropdown and Popover Overrides */
.theme1 [data-radix-popover-content],
.theme1 [role="listbox"],
.theme1 [role="menu"],
.theme1 [role="menuitem"] {
  background-color: #000000 !important;
  color: #e5e5e5 !important;
  border-color: #262626 !important;
}

/* Specific Tailwind Class Overrides */
.theme1 .bg-white\/95,
.theme1 .bg-white\/70 {
  background-color: rgba(0, 0, 0, 0.95) !important;
}

.theme1 .backdrop-blur-sm {
  background-color: #000000 !important;
}

.theme1 .shadow-lg,
.theme1 .shadow-xl,
.theme1 .shadow-2xl {
  box-shadow: 0 10px 25px -3px rgba(255, 204, 0, 0.1), 0 4px 6px -2px rgba(255, 204, 0, 0.05) !important;
}

/* Text Gradient Overrides */
.theme1 .bg-gradient-to-r.from-slate-800.to-slate-600.bg-clip-text.text-transparent,
.theme1 .bg-gradient-to-r.from-emerald-600.to-teal-600.bg-clip-text.text-transparent {
  background: linear-gradient(to right, #e5e5e5, #f0f0f0) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  color: transparent !important;
}

/* Focus and Ring Colors */
.theme1 *:focus,
.theme1 *:focus-visible {
  outline-color: #ffcc00 !important;
  box-shadow: 0 0 0 2px #ffcc00 !important;
}

/* Hover States */
.theme1 .hover\:bg-gray-100:hover,
.theme1 .hover\:bg-slate-100:hover {
  background-color: #1a1a1a !important;
}

/* Alert and Error Messages */
.theme1 .bg-red-50,
.theme1 .bg-rose-50 {
  background-color: #1a0000 !important;
}

.theme1 .text-red-700,
.theme1 .text-red-600 {
  color: #ff6b6b !important;
}

/* Success Messages */
.theme1 .text-green-600 {
  color: #51cf66 !important;
}

.theme1 .text-blue-600,
.theme1 .text-blue-800 {
  color: #74c0fc !important;
}

/* Complete Happy Mode Overrides - Pure Solid Colors Only */

/* 1. SIDEBAR - Pure Blue (#0000ff) */
.theme2 aside {
  background-color: #0000ff !important;
  border-color: #0000ff !important;
}

.theme2 aside * {
  color: #ffffff !important;
}

/* 2. MAIN BACKGROUND - Pure Turquoise (#00ffff) */
.theme2 .bg-gradient-to-br,
.theme2 .bg-gradient-to-r {
  background: #00ffff !important;
}

/* 3. CARDS - Pure Yellow (#ffff00) */
.theme2 .bg-white,
.theme2 .card {
  background-color: #ffff00 !important;
}

.theme2 .bg-white *,
.theme2 .card * {
  color: #000000 !important;
}

/* 4. BUTTONS - Pure Red (#ff0000) */
.theme2 button {
  background-color: #ff0000 !important;
  color: #ffffff !important;
  border-color: #ff0000 !important;
}

.theme2 button:hover {
  background-color: #cc0000 !important;
}

/* 5. INPUT FIELDS - Pure Green (#00ff00) */
.theme2 input,
.theme2 textarea,
.theme2 select {
  background-color: #00ff00 !important;
  color: #000000 !important;
  border-color: #00ff00 !important;
}

.theme2 input::placeholder,
.theme2 textarea::placeholder {
  color: #333333 !important;
}

/* 6. HEADERS AND TITLES - Pure Purple (#8000ff) */
.theme2 h1,
.theme2 h2,
.theme2 h3,
.theme2 h4,
.theme2 h5,
.theme2 h6 {
  background-color: #8000ff !important;
  color: #ffffff !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  display: inline-block !important;
}

/* 7. BORDERS AND DIVIDERS - Pure Orange (#ff8000) */
.theme2 .border-slate-200,
.theme2 .border-slate-300,
.theme2 .border-gray-200,
.theme2 .border-gray-300,
.theme2 .border-blue-200 {
  border-color: #ff8000 !important;
  border-width: 2px !important;
}

/* 8. DROPDOWNS AND POPOVERS - Pure Pink (#ff00ff) */
.theme2 [data-radix-popover-content],
.theme2 [role="listbox"],
.theme2 [role="menu"],
.theme2 [role="menuitem"] {
  background-color: #ff00ff !important;
  color: #000000 !important;
  border-color: #ff00ff !important;
}

/* Additional Color Distribution */

/* Tables - Alternating Colors */
.theme2 table {
  background-color: #ffff00 !important; /* Yellow base */
}

.theme2 thead {
  background-color: #8000ff !important; /* Purple headers */
}

.theme2 thead * {
  color: #ffffff !important;
}

.theme2 tbody tr:nth-child(odd) {
  background-color: #00ffff !important; /* Turquoise odd rows */
}

.theme2 tbody tr:nth-child(even) {
  background-color: #00ff00 !important; /* Green even rows */
}

.theme2 td,
.theme2 th {
  color: #000000 !important;
  border-color: #ff8000 !important; /* Orange borders */
}

/* Remove ALL gradients */
.theme2 .bg-gradient-to-r,
.theme2 .bg-gradient-to-br,
.theme2 .bg-gradient-to-l,
.theme2 .bg-gradient-to-t,
.theme2 .bg-gradient-to-b {
  background: #00ffff !important; /* Fallback to turquoise */
}

/* Text Colors */
.theme2 .text-slate-800,
.theme2 .text-slate-700,
.theme2 .text-slate-600,
.theme2 .text-gray-900,
.theme2 .text-gray-800,
.theme2 .text-gray-700 {
  color: #000000 !important;
  font-weight: 600 !important;
}

/* Focus States */
.theme2 *:focus,
.theme2 *:focus-visible {
  outline-color: #ff0000 !important; /* Red focus */
  box-shadow: 0 0 0 2px #ff0000 !important;
}

/* Hover States */
.theme2 .hover\:bg-gray-100:hover,
.theme2 .hover\:bg-slate-100:hover {
  background-color: #ff8000 !important; /* Orange hover */
}

/* Shadows - Remove gradients, use solid colors */
.theme2 .shadow-lg,
.theme2 .shadow-xl,
.theme2 .shadow-2xl {
  box-shadow: 4px 4px 0px #000000 !important; /* Solid black shadow */
}

/* Status Colors */
.theme2 .text-green-600 {
  color: #00ff00 !important; /* Pure green for success */
}

.theme2 .text-red-600,
.theme2 .text-red-700 {
  color: #ff0000 !important; /* Pure red for errors */
}

.theme2 .text-blue-600,
.theme2 .text-blue-800 {
  color: #0000ff !important; /* Pure blue for info */
}

/* Theme 3: Fresh Green - Nature Inspired Theme */
.theme3 {
  --primary: 120 60% 50%;                /* Forest Green */
  --primary-foreground: 0 0% 100%;       /* White text */
  --background: 120 25% 95%;             /* Very Light Green */
  --foreground: 120 100% 15%;            /* Dark Green text */
  --card: 120 30% 98%;                   /* Light Green cards */
  --card-foreground: 120 100% 15%;       /* Dark Green text on cards */
  --popover: 120 30% 98%;                /* Light Green popover */
  --popover-foreground: 120 100% 15%;    /* Dark Green text on popover */
  --secondary: 120 20% 90%;              /* Light Green secondary */
  --secondary-foreground: 120 100% 15%;  /* Dark Green text on secondary */
  --muted: 120 15% 92%;                  /* Muted Green background */
  --muted-foreground: 120 30% 40%;       /* Muted Green text */
  --accent: 140 60% 70%;                 /* Bright Green accent */
  --accent-foreground: 120 100% 15%;     /* Dark Green text on accent */
  --destructive: 0 70% 50%;              /* Red destructive */
  --destructive-foreground: 0 0% 100%;   /* White text on destructive */
  --border: 120 20% 85%;                 /* Light Green border */
  --input: 120 25% 96%;                  /* Very Light Green inputs */
  --ring: 120 60% 50%;                   /* Green ring for focus */
  --sidebar-background: 120 40% 25%;     /* Dark Green sidebar */
  --sidebar-foreground: 0 0% 100%;       /* White sidebar text */
}

/* Theme 4: Warm Orange - Energetic Theme */
.theme4 {
  --primary: 25 95% 53%;                 /* Vibrant Orange */
  --primary-foreground: 0 0% 100%;       /* White text */
  --background: 35 100% 96%;             /* Very Light Orange */
  --foreground: 25 100% 15%;             /* Dark Orange text */
  --card: 35 80% 98%;                    /* Light Orange cards */
  --card-foreground: 25 100% 15%;        /* Dark Orange text on cards */
  --popover: 35 80% 98%;                 /* Light Orange popover */
  --popover-foreground: 25 100% 15%;     /* Dark Orange text on popover */
  --secondary: 35 60% 90%;               /* Light Orange secondary */
  --secondary-foreground: 25 100% 15%;   /* Dark Orange text on secondary */
  --muted: 35 40% 92%;                   /* Muted Orange background */
  --muted-foreground: 25 50% 40%;        /* Muted Orange text */
  --accent: 45 90% 65%;                  /* Yellow-Orange accent */
  --accent-foreground: 25 100% 15%;      /* Dark Orange text on accent */
  --destructive: 0 70% 50%;              /* Red destructive */
  --destructive-foreground: 0 0% 100%;   /* White text on destructive */
  --border: 35 50% 85%;                  /* Light Orange border */
  --input: 35 60% 96%;                   /* Very Light Orange inputs */
  --ring: 25 95% 53%;                    /* Orange ring for focus */
  --sidebar-background: 25 80% 35%;      /* Dark Orange sidebar */
  --sidebar-foreground: 0 0% 100%;       /* White sidebar text */
}

/* Theme 5: Royal Purple - Elegant Theme */
.theme5 {
  --primary: 270 95% 60%;                /* Royal Purple */
  --primary-foreground: 0 0% 100%;       /* White text */
  --background: 280 30% 96%;             /* Very Light Purple */
  --foreground: 270 100% 15%;            /* Dark Purple text */
  --card: 280 40% 98%;                   /* Light Purple cards */
  --card-foreground: 270 100% 15%;       /* Dark Purple text on cards */
  --popover: 280 40% 98%;                /* Light Purple popover */
  --popover-foreground: 270 100% 15%;    /* Dark Purple text on popover */
  --secondary: 280 30% 90%;              /* Light Purple secondary */
  --secondary-foreground: 270 100% 15%;  /* Dark Purple text on secondary */
  --muted: 280 20% 92%;                  /* Muted Purple background */
  --muted-foreground: 270 40% 40%;       /* Muted Purple text */
  --accent: 290 80% 70%;                 /* Bright Purple accent */
  --accent-foreground: 270 100% 15%;     /* Dark Purple text on accent */
  --destructive: 0 70% 50%;              /* Red destructive */
  --destructive-foreground: 0 0% 100%;   /* White text on destructive */
  --border: 280 30% 85%;                 /* Light Purple border */
  --input: 280 35% 96%;                  /* Very Light Purple inputs */
  --ring: 270 95% 60%;                   /* Purple ring for focus */
  --sidebar-background: 270 70% 35%;     /* Dark Purple sidebar */
  --sidebar-foreground: 0 0% 100%;       /* White sidebar text */
}
