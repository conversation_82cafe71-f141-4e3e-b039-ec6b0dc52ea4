@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Vazirmatn', sans-serif;
    direction: rtl;
  }
}

/* Custom styles for RTL layout */
.space-x-reverse > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* Custom animations for better UX */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Persian Date Picker Styles */
.rmdp-container {
  z-index: 9999 !important;
}

.rmdp-calendar {
  z-index: 9999 !important;
}

.rmdp-shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.rmdp-day.rmdp-selected span:not(.highlight) {
  background-color: var(--primary) !important;
  box-shadow: 0 0 5px var(--primary) !important;
}

.rmdp-day:not(.rmdp-disabled):not(.rmdp-day-hidden) span:hover {
  background-color: var(--accent) !important;
}

.rmdp-week-day {
  color: var(--primary) !important;
}

.rmdp-arrow {
  border: solid var(--primary) !important;
  border-width: 0 2px 2px 0 !important;
}

/* ----------- 5 MODERN & TRENDY COLOR THEMES FOR 2025 ----------- */

/* Theme 1: Digital Lavender & Soft Blue (Pantone 2025 Trend) */
.theme1 {
  --primary: 256 70% 70%;                /* Digital Lavender */
  --background: 240 40% 98%;             /* Soft Blue Background */
  --card: 0 0% 100%;                     /* White Card */
  --sidebar-background: 227 100% 37%;     /* Very Light Lavender */
  --sidebar-foreground: 0 0% 100%;     /* Muted Deep Lavender */
  --input: 256 60% 98%;                  /* Very Light Lavender for Inputs */
  --accent: 216 100% 90%;                /* Light Blue Accent */
}

/* Theme 2: Apricot Crush & Warm Coral (Pantone, Adobe trend) */
.theme2 {
  --primary: 22 92% 63%;                 /* Apricot Crush */
  --background: 33 100% 97%;             /* Light Warm Yellow */
  --card: 0 0% 100%;                     /* White Card */
  --sidebar-background: 88 38% 15%;      /* Soft Coral */
  --sidebar-foreground: 0 0% 100%;      /* Warm Orange-Brown */
  --input: 22 92% 98%;                   /* Light Apricot Input */
  --accent: 340 84% 77%;                 /* Pinkish Accent */
}

/* Theme 3: Cyber Lime & Fresh Green (Techy Modern) */
.theme3 {
  --primary: 90 92% 54%;                 /* Cyber Lime */
  --background: 72 60% 98%;              /* Very Light Green */
  --card: 0 0% 100%;                     /* White Card */
  --sidebar-background: 359 47% 51%;      /* Light Lime Sidebar */
  --sidebar-foreground: 330 10% 4%;     /* Deep Green */
  --input: 82 80% 96%;                   /* Light Green for Inputs */
  --accent: 180 78% 71%;                 /* Aqua Accent */
}

/* Theme 4: Sky Blue & Digital Aqua (Minimalist Cool) */
.theme4 {
  --primary: 196 100% 61%;               /* Sky Blue */
  --background: 210 60% 98%;             /* Light Blue */
  --card: 0 0% 100%;                     /* White */
  --sidebar-background: 192 70% 43%;     /* Very Light Sky Blue */
  --sidebar-foreground: 0 0% 100%;     /* Muted Teal */
  --input: 196 80% 97%;                  /* Light Sky Input */
  --accent: 170 100% 85%;                /* Mint Accent */
}

/* Theme 5: Neo Mint & Soft Gray (Futuristic Commercial) */
.theme5 {
  --primary: 154 71% 68%;                /* Neo Mint */
  --background: 160 30% 98%;             /* Very Pale Mint */
  --card: 0 0% 100%;                     /* White */
  --sidebar-background: 258 86% 34%;     /* Light Mint */
  --sidebar-foreground: 0 0% 100%;     /* Muted Mint */
  --input: 154 61% 97%;                  /* Minty Input */
  --accent: 220 15% 85%;                 /* Soft Grayish Blue Accent */
}
