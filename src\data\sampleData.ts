
export const sampleFacilities = [
  {
    id: 1001,
    facilityType: "تسهیلات مسکن",
    amount: "**********", // 2 میلیارد
    receivedDate: "1403/01/15",
    installmentCount: "60",
    interestRate: "18",
    penaltyRate: "24",
    bankName: "بانک ملی ایران",
    facilityNumber: "MSK-1403-001",
    installmentAmount: "********", // 45 میلیون
    firstInstallmentDate: "1403/02/15",
    installments: [
      { id: 1, dueDate: "1403/02/15", principalAmount: "********", interestAmount: "********" },
      { id: 2, dueDate: "1403/03/15", principalAmount: "********", interestAmount: "********" },
      { id: 3, dueDate: "1403/04/15", principalAmount: "********", interestAmount: "********" },
      { id: 4, dueDate: "1403/05/15", principalAmount: "********", interestAmount: "********" },
      { id: 5, dueDate: "1403/06/15", principalAmount: "********", interestAmount: "********" }
    ],
    createdAt: new Date("2024-04-05")
  },
  {
    id: 1002,
    facilityType: "تسهیلات خودرو",
    amount: "*********", // 800 میلیون
    receivedDate: "1403/02/20",
    installmentCount: "36",
    interestRate: "20",
    penaltyRate: "25",
    bankName: "بانک صادرات ایران",
    facilityNumber: "CAR-1403-002",
    installmentAmount: "********", // 25 میلیون
    firstInstallmentDate: "1403/03/20",
    installments: [
      { id: 1, dueDate: "1403/03/20", principalAmount: "********", interestAmount: "7000000" },
      { id: 2, dueDate: "1403/04/20", principalAmount: "********", interestAmount: "7000000" },
      { id: 3, dueDate: "1403/05/20", principalAmount: "********", interestAmount: "7000000" },
      { id: 4, dueDate: "1403/06/20", principalAmount: "********", interestAmount: "7000000" }
    ],
    createdAt: new Date("2024-05-10")
  },
  {
    id: 1003,
    facilityType: "تسهیلات کارگشایی",
    amount: "********00", // 1.5 میلیارد
    receivedDate: "1403/03/10",
    installmentCount: "48",
    interestRate: "16",
    penaltyRate: "22",
    bankName: "بانک پارسیان",
    facilityNumber: "BUS-1403-003",
    installmentAmount: "********", // 38 میلیون
    firstInstallmentDate: "1403/04/10",
    installments: [
      { id: 1, dueDate: "1403/04/10", principalAmount: "********", interestAmount: "********" },
      { id: 2, dueDate: "1403/05/10", principalAmount: "********", interestAmount: "********" },
      { id: 3, dueDate: "1403/06/10", principalAmount: "********", interestAmount: "********" }
    ],
    createdAt: new Date("2024-05-30")
  },
  {
    id: 1004,
    facilityType: "تسهیلات رفاهی",
    amount: "********0", // 300 میلیون
    receivedDate: "1403/01/25",
    installmentCount: "24",
    interestRate: "15",
    penaltyRate: "20",
    bankName: "بانک تجارت",
    facilityNumber: "REF-1403-004",
    installmentAmount: "********", // 15 میلیون
    firstInstallmentDate: "1403/02/25",
    installments: [
      { id: 1, dueDate: "1403/02/25", principalAmount: "********", interestAmount: "5000000" },
      { id: 2, dueDate: "1403/03/25", principalAmount: "********", interestAmount: "5000000" },
      { id: 3, dueDate: "1403/04/25", principalAmount: "********", interestAmount: "5000000" },
      { id: 4, dueDate: "1403/05/25", principalAmount: "********", interestAmount: "5000000" },
      { id: 5, dueDate: "1403/06/25", principalAmount: "********", interestAmount: "5000000" }
    ],
    createdAt: new Date("2024-04-15")
  },
  {
    id: 1005,
    facilityType: "تسهیلات تولیدی",
    amount: "**********", // 5 میلیارد
    receivedDate: "1403/02/05",
    installmentCount: "72",
    interestRate: "14",
    penaltyRate: "20",
    bankName: "بانک کشاورزی",
    facilityNumber: "PRD-1403-005",
    installmentAmount: "********", // 85 میلیون
    firstInstallmentDate: "1403/03/05",
    installments: [
      { id: 1, dueDate: "1403/03/05", principalAmount: "********", interestAmount: "********" },
      { id: 2, dueDate: "1403/04/05", principalAmount: "********", interestAmount: "********" },
      { id: 3, dueDate: "1403/05/05", principalAmount: "********", interestAmount: "********" }
    ],
    createdAt: new Date("2024-04-25")
  }
];

export const samplePayments = [
  {
    id: 2001,
    facilityId: "1001",
    installmentNumber: "1",
    paymentDate: "1403/02/15",
    principalAmount: "********",
    interestAmount: "********",
    penaltyAmount: "0",
    totalAmount: "********",
    notes: "پرداخت به موقع قسط اول",
    facilityInfo: sampleFacilities[0],
    installmentInfo: sampleFacilities[0].installments[0],
    createdAt: new Date("2024-05-15")
  },
  {
    id: 2002,
    facilityId: "1001",
    installmentNumber: "2",
    paymentDate: "1403/03/20",
    principalAmount: "********",
    interestAmount: "********",
    penaltyAmount: "2000000",
    totalAmount: "47000000",
    notes: "پرداخت با 5 روز تاخیر",
    facilityInfo: sampleFacilities[0],
    installmentInfo: sampleFacilities[0].installments[1],
    createdAt: new Date("2024-06-10")
  },
  {
    id: 2003,
    facilityId: "1002",
    installmentNumber: "1",
    paymentDate: "1403/03/20",
    principalAmount: "********",
    interestAmount: "7000000",
    penaltyAmount: "0",
    totalAmount: "********",
    notes: "پرداخت قسط اول تسهیلات خودرو",
    facilityInfo: sampleFacilities[1],
    installmentInfo: sampleFacilities[1].installments[0],
    createdAt: new Date("2024-06-20")
  },
  {
    id: 2004,
    facilityId: "1003",
    installmentNumber: "1",
    paymentDate: "1403/04/12",
    principalAmount: "********",
    interestAmount: "********",
    penaltyAmount: "1000000",
    totalAmount: "39000000",
    notes: "پرداخت با 2 روز تاخیر",
    facilityInfo: sampleFacilities[2],
    installmentInfo: sampleFacilities[2].installments[0],
    createdAt: new Date("2024-07-02")
  },
  {
    id: 2005,
    facilityId: "1004",
    installmentNumber: "1",
    paymentDate: "1403/02/25",
    principalAmount: "********",
    interestAmount: "5000000",
    penaltyAmount: "0",
    totalAmount: "********",
    notes: "پرداخت به موقع",
    facilityInfo: sampleFacilities[3],
    installmentInfo: sampleFacilities[3].installments[0],
    createdAt: new Date("2024-05-25")
  },
  {
    id: 2006,
    facilityId: "1004",
    installmentNumber: "2",
    paymentDate: "1403/03/30",
    principalAmount: "********",
    interestAmount: "5000000",
    penaltyAmount: "3000000",
    totalAmount: "********",
    notes: "پرداخت با 5 روز تاخیر و جریمه",
    facilityInfo: sampleFacilities[3],
    installmentInfo: sampleFacilities[3].installments[1],
    createdAt: new Date("2024-06-20")
  }
];

export const loadSampleData = () => {
  localStorage.setItem('facilities', JSON.stringify(sampleFacilities));
  localStorage.setItem('payments', JSON.stringify(samplePayments));
};

export const clearAllData = () => {
  localStorage.removeItem('facilities');
  localStorage.removeItem('payments');
};

export const getSummary = () => {
  const facilities = JSON.parse(localStorage.getItem('facilities') || '[]');
  const payments = JSON.parse(localStorage.getItem('payments') || '[]');
  
  return {
    facilitiesCount: facilities.length,
    paymentsCount: payments.length,
    totalFacilityAmount: facilities.reduce((sum: number, f: any) => sum + parseInt(f.amount || '0'), 0),
    totalPaymentAmount: payments.reduce((sum: number, p: any) => sum + parseInt(p.totalAmount || '0'), 0)
  };
};
