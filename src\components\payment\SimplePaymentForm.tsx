import { useState, useEffect, use<PERSON>emo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import SearchableSelect from "@/components/common/SearchableSelect";
import SimpleInput from "@/components/common/SimpleInput";
import CurrencyInput from "@/components/common/CurrencyInput";
import PersianDatePicker from "@/components/common/PersianDatePicker";
import { getFacilities, addPayment, updatePayment, getRemainingInstallmentAmount } from "@/utils/storage";
import { formatCurrency, toEnglishDigits, formatDate } from "@/utils/formatters";
import { persianToDate } from "@/utils/jalali";
import { Facility, PaymentFormData, Payment } from "@/types/types";
import {
  CreditCard,
  ArrowRight,
  Save,
  Edit3,
  DollarSign,
  Calendar,
  FileText,
  AlertCircle,
  CheckCircle2,
  TrendingUp,
  <PERSON>rk<PERSON>
} from "lucide-react";

interface SimplePaymentFormProps {
  onBack: () => void;
  onSuccess?: () => void;
  payment?: Payment;
}

const SimplePaymentForm = ({ onBack, onSuccess, payment }: SimplePaymentFormProps) => {
  const { toast } = useToast();
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [formData, setFormData] = useState<PaymentFormData>(() => ({
    facilityId: payment ? `${payment.facilityId}|` : "",
    installmentNumber: payment?.installmentNumber?.toString() || "",
    paymentDate: payment?.paymentDate || "",
    principalAmount: payment?.principalAmount?.toString() || "",
    interestAmount: payment?.interestAmount?.toString() || "",
    penaltyAmount: payment?.penaltyAmount?.toString() || "",
    commissionAmount: payment?.commissionAmount?.toString() || "",
    otherAmount: payment?.otherAmount?.toString() || "",
    feeAmount: payment?.feeAmount?.toString() || "",
    notes: payment?.notes || "",
  }));

  useEffect(() => {
    setFacilities(getFacilities());
  }, []);

  // پس از بارگذاری تسهیلات، اگر پرداخت در حالت ویرایش باشد، مقدار facilityId را کامل کن
  useEffect(() => {
    if (payment && facilities.length > 0 && formData.facilityId.endsWith("|")) {
      const facility = facilities.find((f) => f.id === payment.facilityId);
      if (facility) {
        setFormData((prev) => ({
          ...prev,
          facilityId: `${facility.id}|${facility.bankName} - ${facility.contractNumber}`,
        }));
      }
    }
  }, [facilities, payment]);

  const facilityOptions = useMemo(
    () => facilities.map((f) => `${f.id}|${f.bankName} - ${f.contractNumber}`),
    [facilities]
  );

  const selectedFacility = useMemo(() => {
    if (!formData.facilityId) return null;
    const facilityId = parseInt(formData.facilityId.split("|")[0]);
    return facilities.find((f) => f.id === facilityId) || null;
  }, [formData.facilityId, facilities]);

  const installmentOptions = useMemo(() => {
    if (!selectedFacility) return [];
    return Array.from(
      { length: selectedFacility.installmentCount },
      (_, i) => `${i + 1}`,
    );
  }, [selectedFacility]);
  
  // اطلاعات قسط انتخاب شده
  const selectedInstallment = useMemo(() => {
    if (!selectedFacility || !formData.installmentNumber) return null;
    const installmentIdx = parseInt(formData.installmentNumber) - 1;
    return selectedFacility.installments?.[installmentIdx] || null;
  }, [selectedFacility, formData.installmentNumber]);

  const updateField = (field: keyof PaymentFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const totalAmount = useMemo(() => {
    const principal = parseInt(formData.principalAmount) || 0;
    const interest = parseInt(formData.interestAmount) || 0;
    const penalty = parseInt(formData.penaltyAmount) || 0;
    const commission = parseInt(formData.commissionAmount) || 0;
    const other = parseInt(formData.otherAmount) || 0;
    const fee = parseInt(formData.feeAmount) || 0;
    return principal + interest + penalty + commission + other + fee;
  }, [
    formData.principalAmount,
    formData.interestAmount,
    formData.penaltyAmount,
    formData.commissionAmount,
    formData.otherAmount,
    formData.feeAmount,
  ]);

  const parseDate = (value: string): Date | null => {
    const [y, m, d] = toEnglishDigits(value).split("/").map(Number);
    if (!y || !m || !d) return null;
    return persianToDate(y, m, d);
  };

  const paymentDiff = useMemo(() => {
    if (!formData.paymentDate || !selectedFacility || !formData.installmentNumber) return null;
    const installmentIdx = parseInt(formData.installmentNumber) - 1;
    const installment = selectedFacility.installments
      ? selectedFacility.installments[installmentIdx]
      : undefined;
    if (!installment) return null;
    const payDate = parseDate(formData.paymentDate);
    const dueDate = parseDate(installment.dueDate);
    if (!payDate || !dueDate) return null;
    const diff = Math.floor((payDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
    return diff;
  }, [formData.paymentDate, formData.installmentNumber, selectedFacility]);
  
  // محاسبه مبالغ باقیمانده برای قسط انتخاب شده
  const remainingAmounts = useMemo(() => {
    if (!selectedFacility || !formData.installmentNumber) {
      return { principalRemaining: 0, commissionRemaining: 0, otherRemaining: 0 };
    }
    
    const facilityId = selectedFacility.id;
    const installmentNumber = parseInt(formData.installmentNumber);
    
    return getRemainingInstallmentAmount(facilityId, installmentNumber);
  }, [selectedFacility, formData.installmentNumber]);

  // متغیر برای نگهداری پیام خطا
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // پاک کردن پیام خطای قبلی
    setErrorMessage(null);

    if (!formData.facilityId || !formData.installmentNumber || !formData.paymentDate) {
      setErrorMessage("لطفاً تمام فیلدهای ضروری را پر کنید");
      return;
    }

    if (!/^\d{4}\/\d{2}\/\d{2}$/.test(formData.paymentDate)) {
      setErrorMessage("تاریخ پرداخت باید در قالب YYYY/MM/DD باشد");
      return;
    }

    if (totalAmount <= 0) {
      setErrorMessage("مبلغ پرداختی باید بیشتر از صفر باشد");
      return;
    }
    
    // کنترل مقادیر وارد شده با مقادیر باقیمانده
    const principalAmount = parseInt(formData.principalAmount) || 0;
    const commissionAmount = parseInt(formData.commissionAmount) || 0;
    const otherAmount = parseInt(formData.otherAmount) || 0;
    
    // فقط برای پرداخت‌های جدید (نه ویرایش) کنترل مقادیر باقیمانده را انجام می‌دهیم
    if (!payment) {
      // بررسی مبلغ اصل
      if (principalAmount > remainingAmounts.principalRemaining) {
        setErrorMessage(`مبلغ اصل وارد شده (${formatCurrency(principalAmount)} ریال) بیشتر از مبلغ باقیمانده (${formatCurrency(remainingAmounts.principalRemaining)} ریال) است`);
        return;
      }
      
      // بررسی کارمزد
      if (commissionAmount > remainingAmounts.commissionRemaining) {
        setErrorMessage(`مبلغ کارمزد وارد شده (${formatCurrency(commissionAmount)} ریال) بیشتر از مبلغ باقیمانده (${formatCurrency(remainingAmounts.commissionRemaining)} ریال) است`);
        return;
      }
      
      // بررسی سایر
      if (otherAmount > remainingAmounts.otherRemaining) {
        setErrorMessage(`مبلغ سایر وارد شده (${formatCurrency(otherAmount)} ریال) بیشتر از مبلغ باقیمانده (${formatCurrency(remainingAmounts.otherRemaining)} ریال) است`);
        return;
      }
    }

    try {
      const facilityId = parseInt(formData.facilityId.split("|")[0]);
      const data: Omit<Payment, "id" | "createdAt"> = {
        facilityId,
        installmentNumber: parseInt(formData.installmentNumber),
        paymentDate: formData.paymentDate,
        principalAmount: principalAmount,
        interestAmount: parseInt(formData.interestAmount) || 0,
        penaltyAmount: parseInt(formData.penaltyAmount) || 0,
        commissionAmount: commissionAmount,
        otherAmount: otherAmount,
        feeAmount: 0, // مقدار هزینه همیشه صفر خواهد بود
        totalAmount,
        notes: formData.notes,
      };

      if (payment) {
        updatePayment(payment.id, data);
        toast({ title: "موفقیت", description: "پرداخت با موفقیت ویرایش شد" });
      } else {
        addPayment(data);
        toast({ title: "موفقیت", description: "پرداخت با موفقیت ثبت شد" });
      }

      if (onSuccess) onSuccess();
      else onBack();
    } catch (error) {
      toast({ title: "خطا", description: "خطا در ثبت پرداخت", variant: "destructive" });
    }
  };

  return (
    <div className="fixed inset-0 right-48 z-50 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-y-auto overflow-x-hidden">
      <div className="min-h-full flex flex-col space-y-8 p-6">
      {/* Enhanced Header */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-r from-emerald-600 to-teal-600 p-3 rounded-xl shadow-lg">
              {payment ? <Edit3 className="h-8 w-8 text-white" /> : <CreditCard className="h-8 w-8 text-white" />}
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                {payment ? "ویرایش پرداخت" : "ثبت پرداخت جدید"}
              </h1>
              <p className="text-slate-500 mt-1">
                {payment ? "ویرایش اطلاعات پرداخت موجود" : "افزودن پرداخت جدید به سیستم"}
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2 bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
            >
              <ArrowRight className="h-4 w-4" />
              لغو
            </Button>
            <Button
              type="submit"
              form="payment-form"
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2"
            >
              {payment ? (
                <>
                  <Save className="h-4 w-4" />
                  ذخیره تغییرات
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" />
                  ثبت پرداخت
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-slate-200/60">
          <CardTitle className="text-xl flex items-center gap-3">
            <div className="bg-gradient-to-r from-emerald-600 to-teal-600 p-2 rounded-lg shadow-md">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <span className="bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
              اطلاعات پرداخت
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <form id="payment-form" onSubmit={handleSubmit} className="space-y-8">
            {errorMessage && (
              <div className="bg-gradient-to-r from-red-50 to-rose-50 border-2 border-red-200 text-red-700 px-6 py-4 rounded-xl shadow-lg flex items-center gap-3" role="alert">
                <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
                <div>
                  <strong className="font-bold">خطا: </strong>
                  <span className="block sm:inline">{errorMessage}</span>
                </div>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <SearchableSelect
                label="انتخاب تسهیلات"
                value={formData.facilityId}
                onValueChange={(value) => {
                  updateField("facilityId", value);
                  updateField("installmentNumber", "");
                }}
                options={facilityOptions}
                placeholder={
                  facilities.length === 0
                    ? "هیچ تسهیلاتی وجود ندارد"
                    : "انتخاب کنید"
                }
                required
              />

              <div className="space-y-1">
                <SearchableSelect
                  label="شماره قسط"
                  value={formData.installmentNumber}
                  onValueChange={(value) => updateField("installmentNumber", value)}
                  options={installmentOptions}
                  placeholder={
                    !selectedFacility
                      ? "ابتدا تسهیلات را انتخاب کنید"
                      : "انتخاب کنید"
                  }
                  required
                />
                {selectedInstallment && (
                  <p className="text-xs text-green-600 font-medium">
                    تاریخ سررسید: {formatDate(selectedInstallment.dueDate)}
                  </p>
                )}
              </div>

              <div>
                <PersianDatePicker
                  label="تاریخ پرداخت"
                  value={formData.paymentDate}
                  onChange={(value) => updateField("paymentDate", value)}
                  required
                />
                {paymentDiff !== null && (
                  <p className={`text-sm mt-1 ${paymentDiff > 0 ? "text-red-600" : paymentDiff < 0 ? "text-green-600" : "text-gray-500"}`}>
                    {paymentDiff > 0
                      ? `${paymentDiff} روز دیرتر`
                      : paymentDiff < 0
                      ? `${Math.abs(paymentDiff)} روز زودتر`
                      : "در تاریخ مقرر"}
                  </p>
                )}
              </div>

              <div className="space-y-1">
                <CurrencyInput
                  label="مبلغ اصل (ریال)"
                  value={formData.principalAmount}
                  onChange={(value) => updateField("principalAmount", value)}
                />
                {formData.installmentNumber && (
                  <div className="space-y-1">
                    <p className="text-xs text-blue-600">
                      مبلغ باقیمانده: {formatCurrency(remainingAmounts.principalRemaining)} ریال
                    </p>
                    {selectedInstallment && (
                      <p className="text-xs text-green-600 font-medium">
                        مبلغ قسط: {formatCurrency(selectedInstallment.principalAmount)} ریال
                      </p>
                    )}
                  </div>
                )}
              </div>

              <div className="space-y-1">
                <CurrencyInput
                  label="مبلغ سود (ریال)"
                  value={formData.interestAmount}
                  onChange={(value) => updateField("interestAmount", value)}
                />
                {formData.installmentNumber && (
                  <div className="space-y-1">
                    <p className="text-xs text-blue-600">
                      مبلغ باقیمانده: {formatCurrency(remainingAmounts.interestRemaining)} ریال
                    </p>
                    {selectedInstallment && (
                      <p className="text-xs text-green-600 font-medium">
                        مبلغ سود قسط: {formatCurrency(selectedInstallment.interestAmount)} ریال
                      </p>
                    )}
                  </div>
                )}
              </div>

              <CurrencyInput
                label="مبلغ جریمه (ریال)"
                value={formData.penaltyAmount}
                onChange={(value) => updateField("penaltyAmount", value)}
              />

              <div className="space-y-1">
                <CurrencyInput
                  label="کارمزد"
                  value={formData.commissionAmount}
                  onChange={(value) => updateField("commissionAmount", value)}
                />
                {formData.installmentNumber && (
                  <p className="text-xs text-blue-600">
                    مبلغ باقیمانده: {formatCurrency(remainingAmounts.commissionRemaining)} ریال
                  </p>
                )}
              </div>

              <div className="space-y-1">
                <CurrencyInput
                  label="سایر"
                  value={formData.otherAmount}
                  onChange={(value) => updateField("otherAmount", value)}
                />
                {formData.installmentNumber && (
                  <p className="text-xs text-blue-600">
                    مبلغ باقیمانده: {formatCurrency(remainingAmounts.otherRemaining)} ریال
                  </p>
                )}
              </div>

              {/* فیلد هزینه حذف شد */}

              <div className="space-y-2">
                <label className="text-sm font-medium">مجموع مبلغ</label>
                <div className="p-3 bg-gray-50 rounded-md text-lg font-semibold">
                  {formatCurrency(totalAmount)} ریال
                </div>
              </div>

              <div className="md:col-span-2">
                <SimpleInput
                  label="یادداشت"
                  value={formData.notes}
                  onChange={(value) => updateField("notes", value)}
                  placeholder="توضیحات اضافی"
                />
              </div>
            </div>

            {selectedFacility && (
              <div className="p-6 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-2xl border border-blue-200/60 shadow-lg">
                <div className="flex items-center gap-2 mb-4">
                  <CheckCircle2 className="h-5 w-5 text-blue-600" />
                  <h4 className="font-semibold text-blue-800">
                    اطلاعات تسهیلات انتخاب شده:
                  </h4>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-white/70 backdrop-blur-sm p-3 rounded-lg border border-white/50 shadow-sm">
                    <span className="text-slate-600 text-sm">نوع:</span>
                    <div className="font-semibold text-slate-800">{selectedFacility.type}</div>
                  </div>
                  <div className="bg-white/70 backdrop-blur-sm p-3 rounded-lg border border-white/50 shadow-sm">
                    <span className="text-slate-600 text-sm">بانک:</span>
                    <div className="font-semibold text-slate-800">{selectedFacility.bankName}</div>
                  </div>
                  <div className="bg-white/70 backdrop-blur-sm p-3 rounded-lg border border-white/50 shadow-sm">
                    <span className="text-slate-600 text-sm">مبلغ کل:</span>
                    <div className="font-semibold text-slate-800">{formatCurrency(selectedFacility.amount)} ریال</div>
                  </div>
                  <div className="bg-white/70 backdrop-blur-sm p-3 rounded-lg border border-white/50 shadow-sm">
                    <span className="text-slate-600 text-sm">نرخ سود:</span>
                    <div className="font-semibold text-slate-800">{selectedFacility.interestRate}%</div>
                  </div>
                </div>
              </div>
            )}


          </form>
        </CardContent>
      </Card>
      </div>
    </div>
  );
};

export default SimplePaymentForm;