diff a/src/components/PaymentAllocationReport.tsx b/src/components/PaymentAllocationReport.tsx	(rejected hunks)
@@ -1,86 +1,109 @@
 
-import { useState, useEffect } from "react";
+import { useState, useEffect, useMemo } from "react";
 import { Button } from "@/components/ui/button";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Badge } from "@/components/ui/badge";
 import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
 import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
-import { formatMoney } from "@/utils/formatMoney";
+import { Input } from "@/components/ui/input";
+import { formatCurrency } from "@/utils/formatters";
 
 interface PaymentAllocationReportProps {
   onBack: () => void;
 }
 
 interface Facility {
   id: number;
   facilityType: string;
   amount: string;
   bankName: string;
   facilityNumber: string;
   interestRate: string;
   penaltyRate: string;
   installments?: any[];
 }
 
 interface Payment {
   id: number;
-  facilityId: string;
-  installmentNumber: string;
+  facilityId: number;
+  installmentNumber: number;
   paymentDate: string;
-  principalAmount: string;
-  interestAmount: string;
-  penaltyAmount: string;
-  totalAmount: string;
-  facilityInfo: Facility;
+  principalAmount: number;
+  interestAmount: number;
+  penaltyAmount: number;
+  totalAmount: number;
+}
+
+interface PaymentWithInfo extends Payment {
+  facilityInfo: Facility | undefined;
 }
 
 interface AllocationResult {
   method: string;
   principal: number;
   interest: number;
   penalty: number;
   remaining: number;
 }
 
+interface AllocationComparison extends AllocationResult {
+  diffPrincipal: number;
+  diffInterest: number;
+  diffPenalty: number;
+}
+
 const PaymentAllocationReport = ({ onBack }: PaymentAllocationReportProps) => {
   const [facilities, setFacilities] = useState<Facility[]>([]);
-  const [payments, setPayments] = useState<Payment[]>([]);
+  const [payments, setPayments] = useState<PaymentWithInfo[]>([]);
   const [selectedFacility, setSelectedFacility] = useState<string>("");
+  const [search, setSearch] = useState("");
+  const [page, setPage] = useState(0);
+  const pageSize = 5;
 
   useEffect(() => {
     const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
     const savedPayments = JSON.parse(localStorage.getItem('payments') || '[]');
     setFacilities(savedFacilities);
-    setPayments(savedPayments);
+    const mapped = savedPayments.map((p: any) => ({
+      ...p,
+      facilityId: Number(p.facilityId),
+      installmentNumber: Number(p.installmentNumber),
+      principalAmount: Number(p.principalAmount),
+      interestAmount: Number(p.interestAmount),
+      penaltyAmount: Number(p.penaltyAmount),
+      totalAmount: Number(p.totalAmount),
+      facilityInfo: savedFacilities.find((f: any) => f.id === Number(p.facilityId)),
+    }));
+    setPayments(mapped);
   }, []);
 
-  const calculateAllocationMethods = (payment: Payment): AllocationResult[] => {
-    const totalPayment = parseFloat(payment.totalAmount);
-    const principalDue = parseFloat(payment.principalAmount) || 0;
-    const interestDue = parseFloat(payment.interestAmount) || 0;
-    const penaltyDue = parseFloat(payment.penaltyAmount) || 0;
+  const calculateAllocationMethods = (payment: Payment): AllocationComparison[] => {
+    const totalPayment = payment.totalAmount;
+    const principalDue = payment.principalAmount || 0;
+    const interestDue = payment.interestAmount || 0;
+    const penaltyDue = payment.penaltyAmount || 0;
     const totalDue = principalDue + interestDue + penaltyDue;
 
     let remaining = totalPayment;
     const results: AllocationResult[] = [];
 
     // Method 1: Interest and Penalty first, then Principal
     let method1Remaining = totalPayment;
     let method1Interest = Math.min(method1Remaining, interestDue);
     method1Remaining -= method1Interest;
     let method1Penalty = Math.min(method1Remaining, penaltyDue);
     method1Remaining -= method1Penalty;
     let method1Principal = Math.min(method1Remaining, principalDue);
     method1Remaining -= method1Principal;
 
     results.push({
       method: "روش اول: سود و جریمه سپس اصل",
       principal: method1Principal,
       interest: method1Interest,
       penalty: method1Penalty,
       remaining: method1Remaining
     });
 
     // Method 2: Proportional allocation
     const totalRatio = totalDue > 0 ? totalPayment / totalDue : 0;
     let method2Principal = Math.min(principalDue * totalRatio, totalPayment);
@@ -104,181 +127,210 @@ const PaymentAllocationReport = ({ onBack }: PaymentAllocationReportProps) => {
     const principalInterestTotal = principalDue + interestDue;
     if (principalInterestTotal > 0) {
       const principalRatio = principalDue / principalInterestTotal;
       const interestRatio = interestDue / principalInterestTotal;
       let method3Principal = Math.min(method3Remaining * principalRatio, principalDue);
       let method3Interest = Math.min(method3Remaining * interestRatio, interestDue);
       method3Remaining -= (method3Principal + method3Interest);
       
       results.push({
         method: "روش سوم: جریمه سپس اصل و سود متناسب",
         principal: method3Principal,
         interest: method3Interest,
         penalty: method3Penalty,
         remaining: method3Remaining
       });
     } else {
       results.push({
         method: "روش سوم: جریمه سپس اصل و سود متناسب",
         principal: 0,
         interest: 0,
         penalty: method3Penalty,
         remaining: method3Remaining
       });
     }
 
-    return results;
+    return results.map((r) => ({
+      ...r,
+      diffPrincipal: payment.principalAmount - r.principal,
+      diffInterest: payment.interestAmount - r.interest,
+      diffPenalty: payment.penaltyAmount - r.penalty,
+    }));
   };
 
-  const facilityPayments = selectedFacility 
-    ? payments.filter(p => p.facilityId === selectedFacility)
+  const facilityPayments = selectedFacility
+    ? payments.filter((p) => p.facilityId.toString() === selectedFacility)
     : payments;
 
-  const selectedFacilityData = facilities.find(f => f.id.toString() === selectedFacility);
+  const filteredPayments = useMemo(
+    () =>
+      facilityPayments.filter((p) => {
+        const term = search.trim();
+        return (
+          (p.facilityInfo?.bankName || '').includes(term) ||
+          (p.facilityInfo?.facilityNumber || '').includes(term) ||
+          p.installmentNumber.toString().includes(term)
+        );
+      }),
+    [facilityPayments, search],
+  );
+
+  const pageCount = Math.ceil(filteredPayments.length / pageSize);
+  const paged = filteredPayments.slice(page * pageSize, page * pageSize + pageSize);
+
+  const exportCSV = () => {
+    const header = 'تاریخ پرداخت,نام بانک,شماره تسهیلات,شماره قسط,مبلغ کل,اصل,سود,جریمه';
+    const rows = filteredPayments.map((p) => [
+      p.paymentDate,
+      p.facilityInfo?.bankName || '',
+      p.facilityInfo?.facilityNumber || '',
+      p.installmentNumber.toString(),
+      p.totalAmount.toString(),
+      p.principalAmount.toString(),
+      p.interestAmount.toString(),
+      p.penaltyAmount.toString(),
+    ].join(','));
+    const csv = [header, ...rows].join('\n');
+    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
+    const url = URL.createObjectURL(blob);
+    const link = document.createElement('a');
+    link.href = url;
+    link.download = 'allocation_report.csv';
+    link.click();
+    URL.revokeObjectURL(url);
+  };
+
+  const exportPDF = () => {
+    window.print();
+  };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
         <h2 className="text-2xl font-bold text-gray-800">گزارش روش‌های تخصیص پرداخت</h2>
-        <Button variant="outline" onClick={onBack}>
-          بازگشت
-        </Button>
+        <Button variant="outline" onClick={onBack}>بازگشت</Button>
       </div>
 
       <Card className="bg-white shadow-lg">
         <CardHeader>
-          <CardTitle>انتخاب تسهیلات</CardTitle>
+          <CardTitle>فیلتر</CardTitle>
         </CardHeader>
-        <CardContent>
-          <Select value={selectedFacility} onValueChange={setSelectedFacility}>
-            <SelectTrigger>
-              <SelectValue placeholder="همه تسهیلات" />
-            </SelectTrigger>
-            <SelectContent>
-              <SelectItem value="">همه تسهیلات</SelectItem>
-              {facilities.map((facility) => (
-                <SelectItem key={facility.id} value={facility.id.toString()}>
-                  {facility.facilityType} - {facility.bankName} - {facility.facilityNumber}
-                </SelectItem>
-              ))}
-            </SelectContent>
-          </Select>
+        <CardContent className="space-y-4">
+          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
+            <Select value={selectedFacility} onValueChange={(v) => { setSelectedFacility(v); setPage(0); }}>
+              <SelectTrigger>
+                <SelectValue placeholder="همه تسهیلات" />
+              </SelectTrigger>
+              <SelectContent>
+                <SelectItem value="">همه تسهیلات</SelectItem>
+                {facilities.map((f) => (
+                  <SelectItem key={f.id} value={f.id.toString()}>
+                    {f.bankName} - {f.facilityNumber}
+                  </SelectItem>
+                ))}
+              </SelectContent>
+            </Select>
 
-          {selectedFacilityData && (
-            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
-              <h4 className="font-semibold text-blue-800 mb-2">اطلاعات تسهیلات:</h4>
-              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
-                <div>
-                  <span className="text-gray-600">نوع: </span>
-                  <span className="font-semibold">{selectedFacilityData.facilityType}</span>
-                </div>
-                <div>
-                  <span className="text-gray-600">بانک: </span>
-                  <span className="font-semibold">{selectedFacilityData.bankName}</span>
-                </div>
-                <div>
-                  <span className="text-gray-600">نرخ سود: </span>
-                  <span className="font-semibold">{selectedFacilityData.interestRate}%</span>
-                </div>
-                <div>
-                  <span className="text-gray-600">نرخ جریمه: </span>
-                  <span className="font-semibold">{selectedFacilityData.penaltyRate}%</span>
-                </div>
-              </div>
-            </div>
-          )}
+            <Input
+              placeholder="جستجو"
+              value={search}
+              onChange={(e) => { setSearch(e.target.value); setPage(0); }}
+            />
+          </div>
+          <div className="flex gap-2">
+            <Button variant="outline" onClick={exportCSV}>خروجی Excel</Button>
+            <Button variant="outline" onClick={exportPDF}>خروجی PDF</Button>
+          </div>
         </CardContent>
       </Card>
 
-      {facilityPayments.length === 0 ? (
+      {filteredPayments.length === 0 ? (
         <Card>
-          <CardContent className="p-6 text-center">
-            <p className="text-gray-500">هیچ پرداختی برای نمایش وجود ندارد</p>
-          </CardContent>
+          <CardContent className="p-6 text-center">هیچ پرداختی برای نمایش وجود ندارد</CardContent>
         </Card>
       ) : (
-        <div className="space-y-6">
-          {facilityPayments.map((payment) => {
-            const allocationResults = calculateAllocationMethods(payment);
-            
-            return (
-              <Card key={payment.id} className="bg-white shadow-md">
-                <CardHeader>
-                  <div className="flex justify-between items-start">
-                    <div>
-                      <CardTitle className="text-lg">
-                        {payment.facilityInfo?.facilityType} - قسط {payment.installmentNumber}
-                      </CardTitle>
-                      <p className="text-sm text-gray-600">
-                        {payment.facilityInfo?.bankName} - {payment.facilityInfo?.facilityNumber}
-                      </p>
-                      <p className="text-sm text-gray-600">
-                        تاریخ پرداخت: {payment.paymentDate}
-                      </p>
-                    </div>
-                    <Badge variant="outline" className="text-green-600 border-green-600">
-                      {formatMoney(payment.totalAmount)} ریال
-                    </Badge>
-                  </div>
-                </CardHeader>
-                <CardContent>
-                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
-                    <h5 className="font-semibold mb-2">مبالغ سررسید:</h5>
-                    <div className="grid grid-cols-3 gap-4 text-sm">
-                      <div>
-                        <span className="text-gray-600">اصل: </span>
-                        <span className="font-semibold">{formatMoney(payment.principalAmount)} ریال</span>
-                      </div>
-                      <div>
-                        <span className="text-gray-600">سود: </span>
-                        <span className="font-semibold">{formatMoney(payment.interestAmount)} ریال</span>
-                      </div>
-                      <div>
-                        <span className="text-gray-600">جریمه: </span>
-                        <span className="font-semibold">{formatMoney(payment.penaltyAmount)} ریال</span>
-                      </div>
-                    </div>
-                  </div>
+        <>
+          <div className="overflow-x-auto">
+            <Table>
+              <TableHeader>
+                <TableRow>
+                  <TableHead className="text-right">نام بانک</TableHead>
+                  <TableHead className="text-right">شماره تسهیلات</TableHead>
+                  <TableHead className="text-right">شماره قسط</TableHead>
+                  <TableHead className="text-right">مبلغ پرداختی</TableHead>
+                  <TableHead className="text-right">اصل</TableHead>
+                  <TableHead className="text-right">سود</TableHead>
+                  <TableHead className="text-right">جریمه</TableHead>
+                  <TableHead className="text-right">روش1 اصل</TableHead>
+                  <TableHead className="text-right">روش1 سود</TableHead>
+                  <TableHead className="text-right">روش1 جریمه</TableHead>
+                  <TableHead className="text-right">انحراف1 اصل</TableHead>
+                  <TableHead className="text-right">انحراف1 سود</TableHead>
+                  <TableHead className="text-right">انحراف1 جریمه</TableHead>
+                  <TableHead className="text-right">روش2 اصل</TableHead>
+                  <TableHead className="text-right">روش2 سود</TableHead>
+                  <TableHead className="text-right">روش2 جریمه</TableHead>
+                  <TableHead className="text-right">انحراف2 اصل</TableHead>
+                  <TableHead className="text-right">انحراف2 سود</TableHead>
+                  <TableHead className="text-right">انحراف2 جریمه</TableHead>
+                  <TableHead className="text-right">روش3 اصل</TableHead>
+                  <TableHead className="text-right">روش3 سود</TableHead>
+                  <TableHead className="text-right">روش3 جریمه</TableHead>
+                  <TableHead className="text-right">انحراف3 اصل</TableHead>
+                  <TableHead className="text-right">انحراف3 سود</TableHead>
+                  <TableHead className="text-right">انحراف3 جریمه</TableHead>
+                </TableRow>
+              </TableHeader>
+              <TableBody>
+                {paged.map((p) => {
+                  const alloc = calculateAllocationMethods(p);
+                  const m1 = alloc[0];
+                  const m2 = alloc[1];
+                  const m3 = alloc[2];
+                  return (
+                    <TableRow key={p.id}>
+                      <TableCell>{p.facilityInfo?.bankName}</TableCell>
+                      <TableCell>{p.facilityInfo?.facilityNumber}</TableCell>
+                      <TableCell>{p.installmentNumber}</TableCell>
+                      <TableCell>{formatCurrency(p.totalAmount)}</TableCell>
+                      <TableCell>{formatCurrency(p.principalAmount)}</TableCell>
+                      <TableCell>{formatCurrency(p.interestAmount)}</TableCell>
+                      <TableCell>{formatCurrency(p.penaltyAmount)}</TableCell>
+                      <TableCell>{formatCurrency(m1.principal)}</TableCell>
+                      <TableCell>{formatCurrency(m1.interest)}</TableCell>
+                      <TableCell>{formatCurrency(m1.penalty)}</TableCell>
+                      <TableCell>{formatCurrency(m1.diffPrincipal)}</TableCell>
+                      <TableCell>{formatCurrency(m1.diffInterest)}</TableCell>
+                      <TableCell>{formatCurrency(m1.diffPenalty)}</TableCell>
+                      <TableCell>{formatCurrency(m2.principal)}</TableCell>
+                      <TableCell>{formatCurrency(m2.interest)}</TableCell>
+                      <TableCell>{formatCurrency(m2.penalty)}</TableCell>
+                      <TableCell>{formatCurrency(m2.diffPrincipal)}</TableCell>
+                      <TableCell>{formatCurrency(m2.diffInterest)}</TableCell>
+                      <TableCell>{formatCurrency(m2.diffPenalty)}</TableCell>
+                      <TableCell>{formatCurrency(m3.principal)}</TableCell>
+                      <TableCell>{formatCurrency(m3.interest)}</TableCell>
+                      <TableCell>{formatCurrency(m3.penalty)}</TableCell>
+                      <TableCell>{formatCurrency(m3.diffPrincipal)}</TableCell>
+                      <TableCell>{formatCurrency(m3.diffInterest)}</TableCell>
+                      <TableCell>{formatCurrency(m3.diffPenalty)}</TableCell>
+                    </TableRow>
+                  );
+                })}
+              </TableBody>
+            </Table>
+          </div>
 
-                  <Table>
-                    <TableHeader>
-                      <TableRow>
-                        <TableHead className="text-right">روش تخصیص</TableHead>
-                        <TableHead className="text-right">اصل</TableHead>
-                        <TableHead className="text-right">سود</TableHead>
-                        <TableHead className="text-right">جریمه</TableHead>
-                        <TableHead className="text-right">باقیمانده</TableHead>
-                      </TableRow>
-                    </TableHeader>
-                    <TableBody>
-                      {allocationResults.map((result, index) => (
-                        <TableRow key={index}>
-                          <TableCell className="font-medium">{result.method}</TableCell>
-                          <TableCell className="text-blue-600 font-semibold">
-                            {formatMoney(result.principal.toString())} ریال
-                          </TableCell>
-                          <TableCell className="text-orange-600 font-semibold">
-                            {formatMoney(result.interest.toString())} ریال
-                          </TableCell>
-                          <TableCell className="text-red-600 font-semibold">
-                            {formatMoney(result.penalty.toString())} ریال
-                          </TableCell>
-                          <TableCell className={`font-semibold ${
-                            result.remaining > 0 ? 'text-green-600' : 'text-gray-500'
-                          }`}>
-                            {result.remaining > 0 ? `+${formatMoney(result.remaining.toString())}` : '0'} ریال
-                          </TableCell>
-                        </TableRow>
-                      ))}
-                    </TableBody>
-                  </Table>
-                </CardContent>
-              </Card>
-            );
-          })}
-        </div>
+          <div className="flex items-center justify-between mt-4">
+            <span className="text-sm">صفحه {page + 1} از {pageCount}</span>
+            <div className="space-x-2 space-x-reverse">
+              <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.max(p - 1, 0))} disabled={page === 0}>قبلی</Button>
+              <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.min(p + 1, pageCount - 1))} disabled={page >= pageCount - 1}>بعدی</Button>
+            </div>
+          </div>
+        </>
       )}
     </div>
   );
 };
 
 export default PaymentAllocationReport;
