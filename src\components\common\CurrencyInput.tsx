
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { formatCurrency, parseCurrency } from "@/utils/formatters";

interface CurrencyInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
}

const CurrencyInput = ({ 
  label, 
  value, 
  onChange, 
  placeholder = "",
  required = false 
}: CurrencyInputProps) => {
  const displayValue = value ? formatCurrency(value) : '';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    if (inputValue === '') {
      onChange('');
      return;
    }
    const numericValue = parseCurrency(inputValue);
    onChange(numericValue.toString());
  };

  return (
    <div className="space-y-2">
      <Label>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      <Input
        type="text"
        value={displayValue}
        onChange={handleChange}
        placeholder={placeholder}
        className="text-right"
      />
    </div>
  );
};

export default CurrencyInput;
