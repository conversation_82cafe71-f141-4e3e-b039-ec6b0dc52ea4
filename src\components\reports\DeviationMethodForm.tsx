import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import SearchableSelect from "@/components/common/SearchableSelect";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getFacilities, getPayments, setDeviationReviewed } from "@/utils/storage";
import { Facility, Payment } from "@/types/types";
import { formatCurrency } from "@/utils/formatters";
import { calculateMethodAllocation } from "@/utils/allocation";

interface DeviationMethodFormProps {
  method: 1 | 2 | 3;
}

interface DiffResult {
  principal: number;
  interest: number;
  penalty: number;
  diffPrincipal: number;
  diffInterest: number;
  diffPenalty: number;
}

const DeviationMethodForm = ({ method }: DeviationMethodFormProps) => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [selected, setSelected] = useState<string>("");
  const [result, setResult] = useState<DiffResult | null>(null);
  const [status, setStatus] = useState(false);

  useEffect(() => {
    setPayments(getPayments());
    setFacilities(getFacilities());
  }, []);

  const facilityMap = useMemo(() => {
    const m = new Map<number, Facility>();
    facilities.forEach((f) => m.set(f.id, f));
    return m;
  }, [facilities]);

  const options = useMemo(() => {
    return payments.map((p) => {
      const f = facilityMap.get(p.facilityId);
      const label = `${f?.bankName || ""} - ${f?.contractNumber || ""} - قسط ${p.installmentNumber}`;
      return `${p.id}|${label}`;
    });
  }, [payments, facilityMap]);

  const selectedPayment = useMemo(() => {
    if (!selected) return null;
    const id = parseInt(selected.split("|")[0]);
    return payments.find((p) => p.id === id) || null;
  }, [selected, payments]);

  const selectedFacility = useMemo(() => {
    if (!selectedPayment) return null;
    return facilityMap.get(selectedPayment.facilityId) || null;
  }, [selectedPayment, facilityMap]);

  const installment = useMemo(() => {
    if (!selectedPayment || !selectedFacility) return null;
    return selectedFacility.installments?.[selectedPayment.installmentNumber - 1] || null;
  }, [selectedPayment, selectedFacility]);

  useEffect(() => {
    if (!selectedPayment) return;
    // حذف getDeviationStatus چون وجود ندارد یا export نشده است
    // اگر نیاز داری این مقدار را از جایی بگیری، باید تابع را به utils/storage.ts اضافه کنی
    // فعلا این بخش را کامنت یا حذف می‌کنیم
    // const st = getDeviationStatus();
    // const id = selectedPayment.id;
    // setStatus(!!st[id]?.[`method${method}`]);
    setStatus(false);
  }, [selectedPayment, method]);

  const handleCalculate = () => {
    if (!selectedPayment) return;
    const alloc = calculateMethodAllocation(selectedPayment, method);
    const diff: DiffResult = {
      principal: alloc.principal,
      interest: alloc.interest,
      penalty: alloc.penalty,
      diffPrincipal: selectedPayment.principalAmount - alloc.principal,
      diffInterest: selectedPayment.interestAmount - alloc.interest,
      diffPenalty: selectedPayment.penaltyAmount - alloc.penalty,
    };
    setResult(diff);
    setDeviationReviewed(selectedPayment.id, method, true);
    setStatus(true);
  };

  return (
    <div className="space-y-4">
      <SearchableSelect
        label="انتخاب پرداخت"
        value={selected}
        onValueChange={setSelected}
        options={options}
        placeholder={payments.length === 0 ? "پرداختی وجود ندارد" : "انتخاب کنید"}
      />

      {selectedPayment && (
        <Card className="shadow">
          <CardHeader>
            <CardTitle>جزئیات پرداخت</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div>وضعیت: {status ? "بررسی شده" : "بررسی نشده"}</div>
            {selectedFacility && (
              <div>
                <div>بانک: {selectedFacility.bankName}</div>
                <div>شماره تسهیلات: {selectedFacility.contractNumber}</div>
              </div>
            )}
            {installment && (
              <div>
                <div>تاریخ سررسید: {installment.dueDate}</div>
                <div>مبلغ اصل قسط: {formatCurrency(installment.principalAmount)} ریال</div>
              </div>
            )}
            <div>
              <div>مبلغ اصل پرداختی: {formatCurrency(selectedPayment.principalAmount)} ریال</div>
              <div>مبلغ سود پرداختی: {formatCurrency(selectedPayment.interestAmount)} ریال</div>
              <div>مبلغ جریمه پرداختی: {formatCurrency(selectedPayment.penaltyAmount)} ریال</div>
            </div>
          </CardContent>
        </Card>
      )}

      {result && (
        <Card className="bg-blue-50">
          <CardHeader>
            <CardTitle>نتیجه</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div>اصل محاسبه شده: {formatCurrency(result.principal)} ریال</div>
            <div>سود محاسبه شده: {formatCurrency(result.interest)} ریال</div>
            <div>جریمه محاسبه شده: {formatCurrency(result.penalty)} ریال</div>
            <div>انحراف اصل: {formatCurrency(result.diffPrincipal)} ریال</div>
            <div>انحراف سود: {formatCurrency(result.diffInterest)} ریال</div>
            <div>انحراف جریمه: {formatCurrency(result.diffPenalty)} ریال</div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-end">
        <Button onClick={handleCalculate} disabled={!selectedPayment}>
          محاسبه انحراف
        </Button>
      </div>
    </div>
  );
};

export default DeviationMethodForm;