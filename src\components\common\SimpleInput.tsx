import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface SimpleInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  type?: string;
  placeholder?: string;
  required?: boolean;
  readOnly?: boolean;
  step?: string;
}

const SimpleInput = ({ 
  label, 
  value, 
  onChange, 
  type = "text", 
  placeholder = "",
  required = false,
  readOnly = false,
  step,
}: SimpleInputProps) => {
  return (
    <div className="space-y-2">
      <Label>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      <Input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        readOnly={readOnly}
        step={step}
      />
    </div>
  );
};

export default SimpleInput;
