import { Payment } from "@/types/types";

export interface AllocationValues {
  principal: number;
  interest: number;
  penalty: number;
}

export const calculateMethodAllocation = (
  payment: Payment,
  method: 1 | 2 | 3,
): AllocationValues => {
  const totalPayment = payment.totalAmount;
  const principalDue = payment.principalAmount || 0;
  const interestDue = payment.interestAmount || 0;
  const penaltyDue = payment.penaltyAmount || 0;
  const totalDue = principalDue + interestDue + penaltyDue;

  switch (method) {
    case 1: {
      let remaining = totalPayment;
      const interest = Math.min(remaining, interestDue);
      remaining -= interest;
      const penalty = Math.min(remaining, penaltyDue);
      remaining -= penalty;
      const principal = Math.min(remaining, principalDue);
      return { principal, interest, penalty };
    }
    case 2: {
      const ratio = totalDue > 0 ? totalPayment / totalDue : 0;
      const principal = Math.min(principalDue * ratio, totalPayment);
      const interest = Math.min(interestDue * ratio, totalPayment - principal);
      const penalty = Math.min(
        penaltyDue * ratio,
        totalPayment - principal - interest,
      );
      return { principal, interest, penalty };
    }
    case 3: {
      let remaining = totalPayment;
      const penalty = Math.min(remaining, penaltyDue);
      remaining -= penalty;
      const principalInterestTotal = principalDue + interestDue;
      if (principalInterestTotal > 0) {
        const principal = Math.min(
          remaining * (principalDue / principalInterestTotal),
          principalDue,
        );
        const interest = Math.min(
          remaining * (interestDue / principalInterestTotal),
          interestDue,
        );
        return { principal, interest, penalty };
      }
      return { principal: 0, interest: 0, penalty };
    }
  }
};
