import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Edit, Trash2, FileText, Printer, ArrowRight, Search, CreditCard, TrendingUp, AlertTriangle } from "lucide-react";
import { getFacilities, getPayments, deletePayment } from "@/utils/storage";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Facility, Payment } from "@/types/types";
import SimplePaymentForm from "@/components/payment/SimplePaymentForm";
import { useToast } from "@/hooks/use-toast";
import * as XLSX from 'xlsx';

interface SimplePaymentListProps {
  onBack: () => void;
  onPaymentUpdated?: () => void; // تابع جدید برای اطلاع‌رسانی به‌روزرسانی پرداخت
}

const SimplePaymentList = ({ onBack, onPaymentUpdated }: SimplePaymentListProps) => {
  const { toast } = useToast();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [search, setSearch] = useState("");
  const [editPayment, setEditPayment] = useState<Payment | null>(null);

  useEffect(() => {
    refreshData();
  }, []);

  const refreshData = () => {
    setPayments(getPayments());
    setFacilities(getFacilities());
  };

  const facilityMap = useMemo(() => {
    const map = new Map<number, Facility>();
    facilities.forEach((f) => map.set(f.id, f));
    return map;
  }, [facilities]);

  const filtered = useMemo(() => {
    const term = search.trim();
    return payments.filter((p) => {
      const f = facilityMap.get(p.facilityId);
      if (!f) return false;
      return f.bankName.includes(term) || f.contractNumber.includes(term);
    });
  }, [payments, facilityMap, search]);

  const [deleteConfirmId, setDeleteConfirmId] = useState<number | null>(null);
  
  const handleDelete = (id: number) => {
    // نمایش تأییدیه حذف
    setDeleteConfirmId(id);
  };
  
  const confirmDelete = () => {
    if (deleteConfirmId === null) return;
    
    deletePayment(deleteConfirmId);
    toast({ title: "حذف شد" });
    refreshData();
    
    // اطلاع‌رسانی به کامپوننت والد که پرداخت حذف شده است
    if (onPaymentUpdated) {
      onPaymentUpdated();
    }
    
    setDeleteConfirmId(null);
  };

  const handleSaved = () => {
    setEditPayment(null);
    refreshData();
    
    // اطلاع‌رسانی به کامپوننت والد که پرداخت به‌روزرسانی شده است
    if (onPaymentUpdated) {
      onPaymentUpdated();
    }
  };
  
  // صادر کردن به اکسل
  const exportToExcel = () => {
    try {
      // تبدیل داده‌ها به فرمت مناسب برای اکسل
      const data = filtered.map((p) => {
        const f = facilityMap.get(p.facilityId);
        if (!f) return null;
        
        return {
          'تاریخ پرداخت': formatDate(p.paymentDate),
          'نام بانک': f.bankName,
          'شماره تسهیلات': f.contractNumber,
          'شماره قسط': p.installmentNumber,
          'مبلغ اصل': typeof p.principalAmount === 'string' ? parseInt(p.principalAmount) : p.principalAmount,
          'مبلغ سود': typeof p.interestAmount === 'string' ? parseInt(p.interestAmount) : p.interestAmount,
          'مبلغ جریمه': typeof p.penaltyAmount === 'string' ? parseInt(p.penaltyAmount) : p.penaltyAmount,
          'مبلغ کل': p.totalAmount || 0
        };
      }).filter(Boolean);
      
      // ایجاد کاربرگ و کتاب کار
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "پرداخت‌ها");
      
      // تنظیم عرض ستون‌ها
      const maxWidth = 20;
      const cols = Object.keys(data[0]).map(() => ({ wch: maxWidth }));
      worksheet['!cols'] = cols;
      
      // ذخیره فایل با فرمت xlsx
      XLSX.writeFile(workbook, "payments.xlsx");
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({ 
        title: "خطا", 
        description: "خطا در صدور فایل اکسل",
        variant: "destructive" 
      });
    }
  };
  
  // چاپ به صورت PDF
  const printAsPDF = () => {
    window.print();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6 space-y-8">
      {/* Enhanced Header */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-r from-emerald-600 to-teal-600 p-3 rounded-xl shadow-lg">
              <CreditCard className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                لیست پرداخت‌ها
              </h1>
              <p className="text-slate-500 mt-1">مدیریت و مشاهده تمام پرداخت‌های ثبت شده</p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={exportToExcel}
              className="flex items-center gap-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 transition-all duration-200 shadow-sm"
            >
              <FileText className="h-4 w-4" />
              دریافت Excel
            </Button>
            <Button
              variant="outline"
              onClick={printAsPDF}
              className="flex items-center gap-2 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-all duration-200 shadow-sm"
            >
              <Printer className="h-4 w-4" />
              دریافت PDF
            </Button>
            <Button
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2 bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
            >
              <ArrowRight className="h-4 w-4" />
              بازگشت
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Search */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Search className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-slate-700">جستجو در پرداخت‌ها</h3>
        </div>
        <Input
          placeholder="جستجوی بانک یا شماره تسهیلات"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="h-12 border-2 border-slate-200 hover:border-purple-300 focus:border-purple-500 transition-all duration-200 bg-white shadow-sm"
        />
      </div>

      {/* Enhanced Table */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
        <div className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-slate-200/60 p-6">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-6 w-6 text-emerald-600" />
            <h3 className="text-xl font-semibold text-slate-800">جدول پرداخت‌ها</h3>
          </div>
        </div>
        <div className="overflow-x-auto w-full">
          <Table className="w-full table-fixed border-collapse">
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 border-b-2 border-slate-200">
                <TableHead className="text-right w-[12%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-blue-50">تاریخ پرداخت</TableHead>
                <TableHead className="text-right w-[15%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-indigo-50">نام بانک</TableHead>
                <TableHead className="text-right w-[15%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-purple-50">شماره تسهیلات</TableHead>
                <TableHead className="text-right w-[10%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-pink-50">شماره قسط</TableHead>
                <TableHead className="text-right w-[15%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-emerald-50">مبلغ اصل</TableHead>
                <TableHead className="text-right w-[15%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-teal-50">مبلغ سود</TableHead>
                <TableHead className="text-right w-[15%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-orange-50">مبلغ جریمه</TableHead>
                <TableHead className="w-[10%] border-r border-slate-200 p-4 font-semibold text-slate-700 bg-slate-50">عملیات</TableHead>
              </TableRow>
            </TableHeader>
          <TableBody>
            {filtered.map((p) => {
              const f = facilityMap.get(p.facilityId);
              if (!f) return null;
              return (
                <TableRow
                  key={p.id}
                  className={`border-b border-slate-200 hover:bg-gradient-to-r hover:from-emerald-50/50 hover:to-teal-50/50 transition-all duration-200 ${
                    (p.id % 2 === 0) ? 'bg-white' : 'bg-slate-50/30'
                  }`}
                >
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{formatDate(p.paymentDate)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-800">{f.bankName}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{f.contractNumber}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-bold text-blue-600 bg-blue-50/50">{p.installmentNumber}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-emerald-700">{formatCurrency(p.principalAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-teal-700">{formatCurrency(p.interestAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-orange-700">{formatCurrency(p.penaltyAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditPayment(p)}
                        className="bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-200 shadow-sm"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDelete(p.id)}
                        className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-all duration-200 shadow-sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* Enhanced Delete Confirmation Dialog */}
      {deleteConfirmId !== null && (
        <Dialog open={true} onOpenChange={(open) => !open && setDeleteConfirmId(null)}>
          <DialogContent className="max-w-md bg-white rounded-2xl shadow-2xl border border-slate-200/60">
            <div className="p-8 space-y-6">
              <div className="flex items-center justify-center">
                <div className="bg-gradient-to-r from-red-100 to-rose-100 p-4 rounded-full">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-center text-slate-800">تأیید حذف</h3>
              <p className="text-center text-slate-600 leading-relaxed">
                آیا از حذف این پرداخت اطمینان دارید؟
                <br />
                <span className="text-sm text-red-600 font-medium">این عمل قابل بازگشت نیست.</span>
              </p>
              <div className="flex justify-center gap-4 pt-4">
                <Button
                  variant="destructive"
                  onClick={confirmDelete}
                  className="bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  بله، حذف شود
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setDeleteConfirmId(null)}
                  className="bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
                >
                  <ArrowRight className="h-4 w-4 mr-2" />
                  انصراف
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
      
      <Dialog open={!!editPayment} onOpenChange={(o) => !o && setEditPayment(null)}>
        <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
          {editPayment && (
            <SimplePaymentForm
              payment={editPayment}
              onBack={() => setEditPayment(null)}
              onSuccess={handleSaved}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SimplePaymentList;