import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Edit, Trash2, FileText, Printer } from "lucide-react";
import { getFacilities, getPayments, deletePayment } from "@/utils/storage";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Facility, Payment } from "@/types/types";
import SimplePaymentForm from "@/components/payment/SimplePaymentForm";
import { useToast } from "@/hooks/use-toast";
import * as XLSX from 'xlsx';

interface SimplePaymentListProps {
  onBack: () => void;
  onPaymentUpdated?: () => void; // تابع جدید برای اطلاع‌رسانی به‌روزرسانی پرداخت
}

const SimplePaymentList = ({ onBack, onPaymentUpdated }: SimplePaymentListProps) => {
  const { toast } = useToast();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [search, setSearch] = useState("");
  const [editPayment, setEditPayment] = useState<Payment | null>(null);

  useEffect(() => {
    refreshData();
  }, []);

  const refreshData = () => {
    setPayments(getPayments());
    setFacilities(getFacilities());
  };

  const facilityMap = useMemo(() => {
    const map = new Map<number, Facility>();
    facilities.forEach((f) => map.set(f.id, f));
    return map;
  }, [facilities]);

  const filtered = useMemo(() => {
    const term = search.trim();
    return payments.filter((p) => {
      const f = facilityMap.get(p.facilityId);
      if (!f) return false;
      return f.bankName.includes(term) || f.contractNumber.includes(term);
    });
  }, [payments, facilityMap, search]);

  const [deleteConfirmId, setDeleteConfirmId] = useState<number | null>(null);
  
  const handleDelete = (id: number) => {
    // نمایش تأییدیه حذف
    setDeleteConfirmId(id);
  };
  
  const confirmDelete = () => {
    if (deleteConfirmId === null) return;
    
    deletePayment(deleteConfirmId);
    toast({ title: "حذف شد" });
    refreshData();
    
    // اطلاع‌رسانی به کامپوننت والد که پرداخت حذف شده است
    if (onPaymentUpdated) {
      onPaymentUpdated();
    }
    
    setDeleteConfirmId(null);
  };

  const handleSaved = () => {
    setEditPayment(null);
    refreshData();
    
    // اطلاع‌رسانی به کامپوننت والد که پرداخت به‌روزرسانی شده است
    if (onPaymentUpdated) {
      onPaymentUpdated();
    }
  };
  
  // صادر کردن به اکسل
  const exportToExcel = () => {
    try {
      // تبدیل داده‌ها به فرمت مناسب برای اکسل
      const data = filtered.map((p) => {
        const f = facilityMap.get(p.facilityId);
        if (!f) return null;
        
        return {
          'تاریخ پرداخت': formatDate(p.paymentDate),
          'نام بانک': f.bankName,
          'شماره تسهیلات': f.contractNumber,
          'شماره قسط': p.installmentNumber,
          'مبلغ اصل': typeof p.principalAmount === 'string' ? parseInt(p.principalAmount) : p.principalAmount,
          'مبلغ سود': typeof p.interestAmount === 'string' ? parseInt(p.interestAmount) : p.interestAmount,
          'مبلغ جریمه': typeof p.penaltyAmount === 'string' ? parseInt(p.penaltyAmount) : p.penaltyAmount,
          'مبلغ کل': p.totalAmount || 0
        };
      }).filter(Boolean);
      
      // ایجاد کاربرگ و کتاب کار
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "پرداخت‌ها");
      
      // تنظیم عرض ستون‌ها
      const maxWidth = 20;
      const cols = Object.keys(data[0]).map(() => ({ wch: maxWidth }));
      worksheet['!cols'] = cols;
      
      // ذخیره فایل با فرمت xlsx
      XLSX.writeFile(workbook, "payments.xlsx");
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({ 
        title: "خطا", 
        description: "خطا در صدور فایل اکسل",
        variant: "destructive" 
      });
    }
  };
  
  // چاپ به صورت PDF
  const printAsPDF = () => {
    window.print();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">لیست پرداخت‌ها</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportToExcel} className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            دریافت Excel
          </Button>
          <Button variant="outline" onClick={printAsPDF} className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            دریافت PDF
          </Button>
          <Button variant="outline" onClick={onBack}>
            بازگشت
          </Button>
        </div>
      </div>

      <Input
        placeholder="جستجوی بانک یا شماره تسهیلات"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className="overflow-x-auto w-full">
        <Table className="w-full table-fixed border-collapse border border-gray-300">
          <TableHeader>
            <TableRow>
              <TableHead className="text-right w-[12%] border border-gray-300">تاریخ پرداخت</TableHead>
              <TableHead className="text-right w-[15%] border border-gray-300">نام بانک</TableHead>
              <TableHead className="text-right w-[15%] border border-gray-300">شماره تسهیلات</TableHead>
              <TableHead className="text-right w-[10%] border border-gray-300">شماره قسط</TableHead>
              <TableHead className="text-right w-[15%] border border-gray-300">مبلغ اصل</TableHead>
              <TableHead className="text-right w-[15%] border border-gray-300">مبلغ سود</TableHead>
              <TableHead className="text-right w-[15%] border border-gray-300">مبلغ جریمه</TableHead>
              <TableHead className="w-[10%] border border-gray-300" />
            </TableRow>
          </TableHeader>
          <TableBody>
            {filtered.map((p) => {
              const f = facilityMap.get(p.facilityId);
              if (!f) return null;
              return (
                <TableRow key={p.id} className="hover:bg-gray-50">
                  <TableCell className="border border-gray-300">{formatDate(p.paymentDate)}</TableCell>
                  <TableCell className="border border-gray-300">{f.bankName}</TableCell>
                  <TableCell className="border border-gray-300">{f.contractNumber}</TableCell>
                  <TableCell className="border border-gray-300">{p.installmentNumber}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(p.principalAmount)}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(p.interestAmount)}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(p.penaltyAmount)}</TableCell>
                  <TableCell className="border border-gray-300">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditPayment(p)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDelete(p.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* دیالوگ تأییدیه حذف */}
      {deleteConfirmId !== null && (
        <Dialog open={true} onOpenChange={(open) => !open && setDeleteConfirmId(null)}>
          <DialogContent className="max-w-md">
            <div className="p-6 space-y-6">
              <h3 className="text-xl font-bold text-center">تأیید حذف</h3>
              <p className="text-center">
                آیا از حذف این پرداخت اطمینان دارید؟
              </p>
              <div className="flex justify-center gap-4 pt-4">
                <Button variant="destructive" onClick={confirmDelete}>
                  بله، حذف شود
                </Button>
                <Button variant="outline" onClick={() => setDeleteConfirmId(null)}>
                  انصراف
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
      
      <Dialog open={!!editPayment} onOpenChange={(o) => !o && setEditPayment(null)}>
        <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
          {editPayment && (
            <SimplePaymentForm
              payment={editPayment}
              onBack={() => setEditPayment(null)}
              onSuccess={handleSaved}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SimplePaymentList;