import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import DeviationMethodForm from "../reports/DeviationMethodForm";

interface ControlMethod2Props {
  onBack: () => void;
}

const ControlMethod2 = ({ onBack }: ControlMethod2Props) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">کنترل تسهیلات - روش دوم</h2>
        <Button variant="outline" onClick={onBack}>
          بازگشت
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>روش دوم کنترل تسهیلات</CardTitle>
        </CardHeader>
        <CardContent>
          <DeviationMethodForm method={2} />
        </CardContent>
      </Card>
    </div>
  );
};

export default ControlMethod2;