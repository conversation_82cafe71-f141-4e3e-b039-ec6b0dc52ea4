
import { Button } from "@/components/ui/button";

interface FacilityFormActionsProps {
  onCancel: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

const FacilityFormActions = ({ onCancel }: FacilityFormActionsProps) => {
  return (
    <div className="flex justify-end space-x-2 space-x-reverse">
      <Button type="button" variant="outline" onClick={onCancel}>
        لغو
      </Button>
      <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
        ثبت تسهیلات
      </Button>
    </div>
  );
};

export default FacilityFormActions;
