 (cd "$(git rev-parse --show-toplevel)" && git apply --3way <<'EOF' 
diff --git a/src/components/Dashboard.tsx b/src/components/Dashboard.tsx
index 0754e7facfe9392a1c60db0af327052c1c085079..798d7769ab33b7c8fff8a05efc8e033d816c0401 100644
--- a/src/components/Dashboard.tsx
+++ b/src/components/Dashboard.tsx
@@ -1,111 +1,204 @@
 
+import { useEffect, useState } from "react";
 import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
-import { Calculator, FileText, List, Database, TrendingUp } from "lucide-react";
+import { Calculator, FileText, List, TrendingUp } from "lucide-react";
+import { getFacilities, getPayments } from "@/utils/storage";
+import { formatCurrency } from "@/utils/formatters";
+import {
+  <PERSON><PERSON><PERSON>,
+  <PERSON>,
+  XAxis,
+  YAxis,
+  <PERSON>lt<PERSON>,
+  ResponsiveContainer,
+  CartesianGrid,
+} from "recharts";
 
 interface DashboardProps {
   onNavigateToFacilityForm: () => void;
   onNavigateToPaymentForm: () => void;
   onNavigateToFacilityList: () => void;
-  onNavigateToTestData: () => void;
+  onNavigateToPaymentList: () => void;
   onNavigateToPaymentAllocation: () => void;
 }
 
-const Dashboard = ({ 
-  onNavigateToFacilityForm, 
-  onNavigateToPaymentForm, 
+const Dashboard = ({
+  onNavigateToFacilityForm,
+  onNavigateToPaymentForm,
   onNavigateToFacilityList,
-  onNavigateToTestData,
-  onNavigateToPaymentAllocation
+  onNavigateToPaymentList,
+  onNavigateToPaymentAllocation,
 }: DashboardProps) => {
+  const [facilityCount, setFacilityCount] = useState(0);
+  const [paymentSum, setPaymentSum] = useState(0);
+  const [interestPenaltySum, setInterestPenaltySum] = useState(0);
+  const [chartData, setChartData] = useState<{ month: number; count: number }[]>([]);
+
+  useEffect(() => {
+    const facilities = getFacilities();
+    const payments = getPayments();
+    setFacilityCount(facilities.length);
+    setPaymentSum(payments.reduce((a, p) => a + (p.totalAmount || 0), 0));
+    setInterestPenaltySum(
+      payments.reduce((a, p) => a + (p.interestAmount || 0) + (p.penaltyAmount || 0), 0),
+    );
+
+    const monthCounts: Record<number, number> = {};
+    const year = new Date().getFullYear();
+    facilities.forEach((f) => {
+      (f.installments || []).forEach((inst) => {
+        const [y, m] = inst.dueDate.split('/').map(Number);
+        if (y === year) {
+          monthCounts[m] = (monthCounts[m] || 0) + 1;
+        }
+      });
+    });
+    const data = Object.entries(monthCounts).map(([m, c]) => ({
+      month: Number(m),
+      count: c,
+    }));
+    setChartData(data);
+  }, []);
   return (
     <div className="space-y-8">
       <div className="text-center">
         <h1 className="text-4xl font-bold text-gray-800 mb-4">
           سیستم مدیریت تسهیلات بانکی
         </h1>
         <p className="text-lg text-gray-600 max-w-2xl mx-auto">
           مدیریت ساده و کارآمد تسهیلات بانکی و پرداخت‌ها
         </p>
       </div>
 
+      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>تعداد تسهیلات</CardTitle>
+          </CardHeader>
+          <CardContent className="text-2xl font-bold text-blue-600">
+            {facilityCount}
+          </CardContent>
+        </Card>
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>مجموع پرداختی</CardTitle>
+          </CardHeader>
+          <CardContent className="text-2xl font-bold text-green-600">
+            {formatCurrency(paymentSum)} ریال
+          </CardContent>
+        </Card>
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>سود و جریمه پرداختی</CardTitle>
+          </CardHeader>
+          <CardContent className="text-2xl font-bold text-orange-600">
+            {formatCurrency(interestPenaltySum)} ریال
+          </CardContent>
+        </Card>
+      </div>
+
+      {chartData.length > 0 ? (
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>تعداد اقساط سررسید هر ماه</CardTitle>
+          </CardHeader>
+          <CardContent style={{ height: 300 }}>
+            <ResponsiveContainer width="100%" height="100%">
+              <BarChart data={chartData} layout="vertical">
+                <CartesianGrid strokeDasharray="3 3" />
+                <XAxis type="number" allowDecimals={false} />
+                <YAxis dataKey="month" type="category" />
+                <Tooltip />
+                <Bar dataKey="count" fill="#3b82f6" />
+              </BarChart>
+            </ResponsiveContainer>
+          </CardContent>
+        </Card>
+      ) : (
+        <Card className="bg-white shadow">
+          <CardContent className="p-6 text-center text-gray-500">
+            هیچ داده‌ای ثبت نشده
+          </CardContent>
+        </Card>
+      )}
+
       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
         <Card 
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
           onClick={onNavigateToFacilityForm}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
             <CardTitle className="text-lg font-semibold">ثبت تسهیلات</CardTitle>
             <FileText className="h-5 w-5 text-blue-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
               ثبت اطلاعات تسهیلات جدید
             </CardDescription>
           </CardContent>
         </Card>
 
         <Card 
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
           onClick={onNavigateToPaymentForm}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
             <CardTitle className="text-lg font-semibold">ثبت پرداخت</CardTitle>
             <Calculator className="h-5 w-5 text-green-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
               ثبت پرداخت اقساط
             </CardDescription>
           </CardContent>
         </Card>
 
-        <Card 
+        <Card
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
           onClick={onNavigateToFacilityList}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
             <CardTitle className="text-lg font-semibold">لیست تسهیلات</CardTitle>
             <List className="h-5 w-5 text-purple-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
               مشاهده تمام تسهیلات و پرداخت‌ها
             </CardDescription>
           </CardContent>
         </Card>
 
-        <Card 
+        <Card
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
-          onClick={onNavigateToPaymentAllocation}
+          onClick={onNavigateToPaymentList}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
-            <CardTitle className="text-lg font-semibold">گزارش تخصیص</CardTitle>
-            <TrendingUp className="h-5 w-5 text-orange-600 mr-auto" />
+            <CardTitle className="text-lg font-semibold">لیست پرداخت‌ها</CardTitle>
+            <List className="h-5 w-5 text-purple-600 mr-auto" />
           </CardHeader>
           <CardContent>
-            <CardDescription>
-              مقایسه روش‌های تخصیص پرداخت
-            </CardDescription>
+            <CardDescription>مشاهده تمام پرداخت‌ها</CardDescription>
           </CardContent>
         </Card>
 
         <Card 
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
-          onClick={onNavigateToTestData}
+          onClick={onNavigateToPaymentAllocation}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
-            <CardTitle className="text-lg font-semibold">داده‌های آزمایشی</CardTitle>
-            <Database className="h-5 w-5 text-teal-600 mr-auto" />
+            <CardTitle className="text-lg font-semibold">گزارش تخصیص</CardTitle>
+            <TrendingUp className="h-5 w-5 text-orange-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
-              بارگذاری داده‌های نمونه
+              مقایسه روش‌های تخصیص پرداخت
             </CardDescription>
           </CardContent>
         </Card>
+
       </div>
     </div>
   );
 };
 
 export default Dashboard;
diff --git a/src/components/FacilityList.tsx b/src/components/FacilityList.tsx
index 8bdfac4c4de70e96a60d6b420fe382da2feb7ffa..f55d373638f80d07950cc319776be14540820cad 100644
--- a/src/components/FacilityList.tsx
+++ b/src/components/FacilityList.tsx
@@ -1,90 +1,94 @@
 
 import { useState, useEffect } from "react";
 import { Button } from "@/components/ui/button";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Badge } from "@/components/ui/badge";
 import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
 import { Edit, Trash2, Plus } from "lucide-react";
 import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
 } from "@/components/ui/dialog";
+import SimpleFacilityForm from "./facility/SimpleFacilityForm";
+import SimplePaymentForm from "./payment/SimplePaymentForm";
 import { useToast } from "@/hooks/use-toast";
 
 interface FacilityListProps {
   onBack: () => void;
   onAddFacility: () => void;
   onAddPayment: () => void;
 }
 
 interface Facility {
   id: number;
   facilityType: string;
   amount: string;
   receivedDate: string;
   bankName: string;
   facilityNumber: string;
   installmentCount: string;
   interestRate: string;
   penaltyRate: string;
   installmentAmount: string;
   firstInstallmentDate: string;
 }
 
 interface Payment {
   id: number;
   facilityId: string;
   installmentNumber: string;
   paymentDate: string;
   principalAmount: string;
   interestAmount: string;
   penaltyAmount: string;
   totalAmount: string;
   notes: string;
   facilityInfo: Facility;
 }
 
 const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps) => {
   const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
   const [payments, setPayments] = useState<Payment[]>([]);
   const [deleteDialog, setDeleteDialog] = useState<{
     isOpen: boolean;
     type: 'facility' | 'payment';
     id: number;
     title: string;
   }>({
     isOpen: false,
     type: 'facility',
     id: 0,
     title: '',
   });
+  const [editFacility, setEditFacility] = useState<Facility | null>(null);
+  const [editPayment, setEditPayment] = useState<Payment | null>(null);
 
   useEffect(() => {
     const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
     const savedPayments = JSON.parse(localStorage.getItem('payments') || '[]');
     setFacilities(savedFacilities);
     setPayments(savedPayments);
   }, []);
 
   const formatMoney = (amount: string) => {
     return new Intl.NumberFormat('fa-IR').format(parseFloat(amount) || 0);
   };
 
   const formatDate = (dateString: string) => {
     try {
       const date = new Date(dateString);
       return new Intl.DateTimeFormat('fa-IR').format(date);
     } catch {
       return dateString;
     }
   };
 
   const getFacilityPayments = (facilityId: number) => {
     return payments.filter(payment => payment.facilityId === facilityId.toString());
   };
 
@@ -130,50 +134,67 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
       localStorage.setItem('facilities', JSON.stringify(updatedFacilities));
       
       // Also remove related payments
       const updatedPayments = payments.filter(p => p.facilityId !== deleteDialog.id.toString());
       setPayments(updatedPayments);
       localStorage.setItem('payments', JSON.stringify(updatedPayments));
       
       toast({
         title: "موفقیت",
         description: "تسهیلات و پرداخت‌های مرتبط حذف شد",
       });
     } else {
       const updatedPayments = payments.filter(p => p.id !== deleteDialog.id);
       setPayments(updatedPayments);
       localStorage.setItem('payments', JSON.stringify(updatedPayments));
       
       toast({
         title: "موفقیت",
         description: "پرداخت حذف شد",
       });
     }
     
     setDeleteDialog({ isOpen: false, type: 'facility', id: 0, title: '' });
   };
 
+  const refreshData = () => {
+    const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
+    const savedPayments = JSON.parse(localStorage.getItem('payments') || '[]');
+    setFacilities(savedFacilities);
+    setPayments(savedPayments);
+  };
+
+  const handleFacilityUpdated = () => {
+    setEditFacility(null);
+    refreshData();
+  };
+
+  const handlePaymentUpdated = () => {
+    setEditPayment(null);
+    refreshData();
+  };
+
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
         <h2 className="text-2xl font-bold text-gray-800">لیست تسهیلات و پرداخت‌ها</h2>
         <Button variant="outline" onClick={onBack}>
           بازگشت
         </Button>
       </div>
 
       <Tabs defaultValue="facilities" className="w-full">
         <TabsList className="grid w-full grid-cols-2">
           <TabsTrigger value="facilities">تسهیلات</TabsTrigger>
           <TabsTrigger value="payments">پرداخت‌ها</TabsTrigger>
         </TabsList>
 
         <TabsContent value="facilities" className="space-y-4">
           <div className="flex justify-end">
             <Button onClick={onAddFacility} className="flex items-center gap-2">
               <Plus className="h-4 w-4" />
               افزودن تسهیلات
             </Button>
           </div>
           
           {facilities.length === 0 ? (
             <Card>
@@ -185,51 +206,51 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
             facilities.map((facility) => {
               const paymentStatus = getPaymentStatus(facility);
               const totalPaid = getTotalPaid(facility.id);
               const facilityPayments = getFacilityPayments(facility.id);
               
               return (
                 <Card key={facility.id} className="bg-white shadow-md">
                   <CardHeader>
                     <div className="flex justify-between items-start">
                       <div>
                         <CardTitle className="text-lg">
                           {facility.facilityType} - {facility.bankName}
                         </CardTitle>
                         <p className="text-sm text-gray-600">
                           شماره: {facility.facilityNumber}
                         </p>
                       </div>
                       <div className="flex items-center gap-2">
                         <Badge className={`${paymentStatus.color} text-white`}>
                           {paymentStatus.status}
                         </Badge>
                         <div className="flex gap-1">
                           <Button
                             variant="outline"
                             size="sm"
-                            onClick={() => {/* TODO: Implement edit */}}
+                            onClick={() => setEditFacility(facility)}
                             className="h-8 w-8 p-0"
                           >
                             <Edit className="h-4 w-4" />
                           </Button>
                           <Button
                             variant="outline"
                             size="sm"
                             onClick={() => handleDeleteFacility(facility.id)}
                             className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                           >
                             <Trash2 className="h-4 w-4" />
                           </Button>
                         </div>
                       </div>
                     </div>
                   </CardHeader>
                   <CardContent>
                     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                       <div>
                         <span className="text-sm text-gray-600">مبلغ کل:</span>
                         <p className="font-semibold">{formatMoney(facility.amount)} ریال</p>
                       </div>
                       <div>
                         <span className="text-sm text-gray-600">پرداخت شده:</span>
                         <p className="font-semibold text-green-600">{formatMoney(totalPaid.toString())} ریال</p>
@@ -281,51 +302,51 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
             </Card>
           ) : (
             <div className="space-y-4">
               {payments
                 .sort((a, b) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime())
                 .map((payment) => (
                   <Card key={payment.id} className="bg-white shadow-md">
                     <CardHeader>
                       <div className="flex justify-between items-start">
                         <div>
                           <CardTitle className="text-lg">
                             {payment.facilityInfo?.facilityType} - قسط {payment.installmentNumber}
                           </CardTitle>
                           <p className="text-sm text-gray-600">
                             {payment.facilityInfo?.bankName} - {payment.facilityInfo?.facilityNumber}
                           </p>
                         </div>
                         <div className="flex items-center gap-2">
                           <Badge variant="outline">
                             {formatDate(payment.paymentDate)}
                           </Badge>
                           <div className="flex gap-1">
                             <Button
                               variant="outline"
                               size="sm"
-                              onClick={() => {/* TODO: Implement edit */}}
+                              onClick={() => setEditPayment(payment)}
                               className="h-8 w-8 p-0"
                             >
                               <Edit className="h-4 w-4" />
                             </Button>
                             <Button
                               variant="outline"
                               size="sm"
                               onClick={() => handleDeletePayment(payment.id)}
                               className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                             >
                               <Trash2 className="h-4 w-4" />
                             </Button>
                           </div>
                         </div>
                       </div>
                     </CardHeader>
                     <CardContent>
                       <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                         <div>
                           <span className="text-sm text-gray-600">اصل:</span>
                           <p className="font-semibold text-blue-600">
                             {formatMoney(payment.principalAmount)} ریال
                           </p>
                         </div>
                         <div>
@@ -362,30 +383,40 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
       </Tabs>
 
       <Dialog open={deleteDialog.isOpen} onOpenChange={(open) => !open && setDeleteDialog({ isOpen: false, type: 'facility', id: 0, title: '' })}>
         <DialogContent>
           <DialogHeader>
             <DialogTitle>تأیید حذف</DialogTitle>
             <DialogDescription>
               آیا مطمئن هستید که می‌خواهید "{deleteDialog.title}" را حذف کنید؟
               {deleteDialog.type === 'facility' && (
                 <span className="block mt-2 text-red-600">
                   تمام پرداخت‌های مرتبط با این تسهیلات نیز حذف خواهد شد.
                 </span>
               )}
             </DialogDescription>
           </DialogHeader>
           <DialogFooter>
             <Button variant="outline" onClick={() => setDeleteDialog({ isOpen: false, type: 'facility', id: 0, title: '' })}>
               لغو
             </Button>
             <Button variant="destructive" onClick={confirmDelete}>
               حذف
             </Button>
           </DialogFooter>
         </DialogContent>
       </Dialog>
+      <Dialog open={!!editFacility} onOpenChange={(open) => !open && setEditFacility(null)}>
+        <DialogContent className="max-w-xl">
+          <SimpleFacilityForm onBack={() => setEditFacility(null)} onSuccess={handleFacilityUpdated} facility={editFacility!} />
+        </DialogContent>
+      </Dialog>
+      <Dialog open={!!editPayment} onOpenChange={(open) => !open && setEditPayment(null)}>
+        <DialogContent className="max-w-xl">
+          <SimplePaymentForm onBack={() => setEditPayment(null)} onSuccess={handlePaymentUpdated} payment={editPayment!} />
+        </DialogContent>
+      </Dialog>
     </div>
   );
 };
 
 export default FacilityList;
diff --git a/src/components/PaymentAllocationReport.tsx b/src/components/PaymentAllocationReport.tsx
index 1d61926b90db20b091e4a0e6a784f9d530011ff6..d5c2e17e24aef700aea38d342c736d8ce7ab1805 100644
--- a/src/components/PaymentAllocationReport.tsx
+++ b/src/components/PaymentAllocationReport.tsx
@@ -1,86 +1,109 @@
 
-import { useState, useEffect } from "react";
+import { useState, useEffect, useMemo } from "react";
 import { Button } from "@/components/ui/button";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Badge } from "@/components/ui/badge";
 import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
 import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
-import { formatMoney } from "@/utils/formatMoney";
+import { Input } from "@/components/ui/input";
+import { formatCurrency } from "@/utils/formatters";
 
 interface PaymentAllocationReportProps {
   onBack: () => void;
 }
 
 interface Facility {
   id: number;
   facilityType: string;
   amount: string;
   bankName: string;
   facilityNumber: string;
   interestRate: string;
   penaltyRate: string;
   installments?: any[];
 }
 
 interface Payment {
   id: number;
-  facilityId: string;
-  installmentNumber: string;
+  facilityId: number;
+  installmentNumber: number;
   paymentDate: string;
-  principalAmount: string;
-  interestAmount: string;
-  penaltyAmount: string;
-  totalAmount: string;
-  facilityInfo: Facility;
+  principalAmount: number;
+  interestAmount: number;
+  penaltyAmount: number;
+  totalAmount: number;
+}
+
+interface PaymentWithInfo extends Payment {
+  facilityInfo: Facility | undefined;
 }
 
 interface AllocationResult {
   method: string;
   principal: number;
   interest: number;
   penalty: number;
   remaining: number;
 }
 
+interface AllocationComparison extends AllocationResult {
+  diffPrincipal: number;
+  diffInterest: number;
+  diffPenalty: number;
+}
+
 const PaymentAllocationReport = ({ onBack }: PaymentAllocationReportProps) => {
   const [facilities, setFacilities] = useState<Facility[]>([]);
-  const [payments, setPayments] = useState<Payment[]>([]);
+  const [payments, setPayments] = useState<PaymentWithInfo[]>([]);
   const [selectedFacility, setSelectedFacility] = useState<string>("");
+  const [search, setSearch] = useState("");
+  const [page, setPage] = useState(0);
+  const pageSize = 5;
 
   useEffect(() => {
     const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
     const savedPayments = JSON.parse(localStorage.getItem('payments') || '[]');
     setFacilities(savedFacilities);
-    setPayments(savedPayments);
+    const mapped = savedPayments.map((p: any) => ({
+      ...p,
+      facilityId: Number(p.facilityId),
+      installmentNumber: Number(p.installmentNumber),
+      principalAmount: Number(p.principalAmount),
+      interestAmount: Number(p.interestAmount),
+      penaltyAmount: Number(p.penaltyAmount),
+      totalAmount: Number(p.totalAmount),
+      facilityInfo: savedFacilities.find((f: any) => f.id === Number(p.facilityId)),
+    }));
+    setPayments(mapped);
   }, []);
 
-  const calculateAllocationMethods = (payment: Payment): AllocationResult[] => {
-    const totalPayment = parseFloat(payment.totalAmount);
-    const principalDue = parseFloat(payment.principalAmount) || 0;
-    const interestDue = parseFloat(payment.interestAmount) || 0;
-    const penaltyDue = parseFloat(payment.penaltyAmount) || 0;
+  const calculateAllocationMethods = (payment: Payment): AllocationComparison[] => {
+    const totalPayment = payment.totalAmount;
+    const principalDue = payment.principalAmount || 0;
+    const interestDue = payment.interestAmount || 0;
+    const penaltyDue = payment.penaltyAmount || 0;
     const totalDue = principalDue + interestDue + penaltyDue;
 
     let remaining = totalPayment;
     const results: AllocationResult[] = [];
 
     // Method 1: Interest and Penalty first, then Principal
     let method1Remaining = totalPayment;
     let method1Interest = Math.min(method1Remaining, interestDue);
     method1Remaining -= method1Interest;
     let method1Penalty = Math.min(method1Remaining, penaltyDue);
     method1Remaining -= method1Penalty;
     let method1Principal = Math.min(method1Remaining, principalDue);
     method1Remaining -= method1Principal;
 
     results.push({
       method: "روش اول: سود و جریمه سپس اصل",
       principal: method1Principal,
       interest: method1Interest,
       penalty: method1Penalty,
       remaining: method1Remaining
     });
 
     // Method 2: Proportional allocation
     const totalRatio = totalDue > 0 ? totalPayment / totalDue : 0;
     let method2Principal = Math.min(principalDue * totalRatio, totalPayment);
@@ -104,181 +127,210 @@ const PaymentAllocationReport = ({ onBack }: PaymentAllocationReportProps) => {
     const principalInterestTotal = principalDue + interestDue;
     if (principalInterestTotal > 0) {
       const principalRatio = principalDue / principalInterestTotal;
       const interestRatio = interestDue / principalInterestTotal;
       let method3Principal = Math.min(method3Remaining * principalRatio, principalDue);
       let method3Interest = Math.min(method3Remaining * interestRatio, interestDue);
       method3Remaining -= (method3Principal + method3Interest);
       
       results.push({
         method: "روش سوم: جریمه سپس اصل و سود متناسب",
         principal: method3Principal,
         interest: method3Interest,
         penalty: method3Penalty,
         remaining: method3Remaining
       });
     } else {
       results.push({
         method: "روش سوم: جریمه سپس اصل و سود متناسب",
         principal: 0,
         interest: 0,
         penalty: method3Penalty,
         remaining: method3Remaining
       });
     }
 
-    return results;
+    return results.map((r) => ({
+      ...r,
+      diffPrincipal: payment.principalAmount - r.principal,
+      diffInterest: payment.interestAmount - r.interest,
+      diffPenalty: payment.penaltyAmount - r.penalty,
+    }));
   };
 
-  const facilityPayments = selectedFacility 
-    ? payments.filter(p => p.facilityId === selectedFacility)
+  const facilityPayments = selectedFacility
+    ? payments.filter((p) => p.facilityId.toString() === selectedFacility)
     : payments;
 
-  const selectedFacilityData = facilities.find(f => f.id.toString() === selectedFacility);
+  const filteredPayments = useMemo(
+    () =>
+      facilityPayments.filter((p) => {
+        const term = search.trim();
+        return (
+          (p.facilityInfo?.bankName || '').includes(term) ||
+          (p.facilityInfo?.facilityNumber || '').includes(term) ||
+          p.installmentNumber.toString().includes(term)
+        );
+      }),
+    [facilityPayments, search],
+  );
+
+  const pageCount = Math.ceil(filteredPayments.length / pageSize);
+  const paged = filteredPayments.slice(page * pageSize, page * pageSize + pageSize);
+
+  const exportCSV = () => {
+    const header = 'تاریخ پرداخت,نام بانک,شماره تسهیلات,شماره قسط,مبلغ کل,اصل,سود,جریمه';
+    const rows = filteredPayments.map((p) => [
+      p.paymentDate,
+      p.facilityInfo?.bankName || '',
+      p.facilityInfo?.facilityNumber || '',
+      p.installmentNumber.toString(),
+      p.totalAmount.toString(),
+      p.principalAmount.toString(),
+      p.interestAmount.toString(),
+      p.penaltyAmount.toString(),
+    ].join(','));
+    const csv = [header, ...rows].join('\n');
+    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
+    const url = URL.createObjectURL(blob);
+    const link = document.createElement('a');
+    link.href = url;
+    link.download = 'allocation_report.csv';
+    link.click();
+    URL.revokeObjectURL(url);
+  };
+
+  const exportPDF = () => {
+    window.print();
+  };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
         <h2 className="text-2xl font-bold text-gray-800">گزارش روش‌های تخصیص پرداخت</h2>
-        <Button variant="outline" onClick={onBack}>
-          بازگشت
-        </Button>
+        <Button variant="outline" onClick={onBack}>بازگشت</Button>
       </div>
 
       <Card className="bg-white shadow-lg">
         <CardHeader>
-          <CardTitle>انتخاب تسهیلات</CardTitle>
+          <CardTitle>فیلتر</CardTitle>
         </CardHeader>
-        <CardContent>
-          <Select value={selectedFacility} onValueChange={setSelectedFacility}>
-            <SelectTrigger>
-              <SelectValue placeholder="همه تسهیلات" />
-            </SelectTrigger>
-            <SelectContent>
-              <SelectItem value="">همه تسهیلات</SelectItem>
-              {facilities.map((facility) => (
-                <SelectItem key={facility.id} value={facility.id.toString()}>
-                  {facility.facilityType} - {facility.bankName} - {facility.facilityNumber}
-                </SelectItem>
-              ))}
-            </SelectContent>
-          </Select>
+        <CardContent className="space-y-4">
+          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
+            <Select value={selectedFacility} onValueChange={(v) => { setSelectedFacility(v); setPage(0); }}>
+              <SelectTrigger>
+                <SelectValue placeholder="همه تسهیلات" />
+              </SelectTrigger>
+              <SelectContent>
+                <SelectItem value="">همه تسهیلات</SelectItem>
+                {facilities.map((f) => (
+                  <SelectItem key={f.id} value={f.id.toString()}>
+                    {f.bankName} - {f.facilityNumber}
+                  </SelectItem>
+                ))}
+              </SelectContent>
+            </Select>
 
-          {selectedFacilityData && (
-            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
-              <h4 className="font-semibold text-blue-800 mb-2">اطلاعات تسهیلات:</h4>
-              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
-                <div>
-                  <span className="text-gray-600">نوع: </span>
-                  <span className="font-semibold">{selectedFacilityData.facilityType}</span>
-                </div>
-                <div>
-                  <span className="text-gray-600">بانک: </span>
-                  <span className="font-semibold">{selectedFacilityData.bankName}</span>
-                </div>
-                <div>
-                  <span className="text-gray-600">نرخ سود: </span>
-                  <span className="font-semibold">{selectedFacilityData.interestRate}%</span>
-                </div>
-                <div>
-                  <span className="text-gray-600">نرخ جریمه: </span>
-                  <span className="font-semibold">{selectedFacilityData.penaltyRate}%</span>
-                </div>
-              </div>
-            </div>
-          )}
+            <Input
+              placeholder="جستجو"
+              value={search}
+              onChange={(e) => { setSearch(e.target.value); setPage(0); }}
+            />
+          </div>
+          <div className="flex gap-2">
+            <Button variant="outline" onClick={exportCSV}>خروجی Excel</Button>
+            <Button variant="outline" onClick={exportPDF}>خروجی PDF</Button>
+          </div>
         </CardContent>
       </Card>
 
-      {facilityPayments.length === 0 ? (
+      {filteredPayments.length === 0 ? (
         <Card>
-          <CardContent className="p-6 text-center">
-            <p className="text-gray-500">هیچ پرداختی برای نمایش وجود ندارد</p>
-          </CardContent>
+          <CardContent className="p-6 text-center">هیچ پرداختی برای نمایش وجود ندارد</CardContent>
         </Card>
       ) : (
-        <div className="space-y-6">
-          {facilityPayments.map((payment) => {
-            const allocationResults = calculateAllocationMethods(payment);
-            
-            return (
-              <Card key={payment.id} className="bg-white shadow-md">
-                <CardHeader>
-                  <div className="flex justify-between items-start">
-                    <div>
-                      <CardTitle className="text-lg">
-                        {payment.facilityInfo?.facilityType} - قسط {payment.installmentNumber}
-                      </CardTitle>
-                      <p className="text-sm text-gray-600">
-                        {payment.facilityInfo?.bankName} - {payment.facilityInfo?.facilityNumber}
-                      </p>
-                      <p className="text-sm text-gray-600">
-                        تاریخ پرداخت: {payment.paymentDate}
-                      </p>
-                    </div>
-                    <Badge variant="outline" className="text-green-600 border-green-600">
-                      {formatMoney(payment.totalAmount)} ریال
-                    </Badge>
-                  </div>
-                </CardHeader>
-                <CardContent>
-                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
-                    <h5 className="font-semibold mb-2">مبالغ سررسید:</h5>
-                    <div className="grid grid-cols-3 gap-4 text-sm">
-                      <div>
-                        <span className="text-gray-600">اصل: </span>
-                        <span className="font-semibold">{formatMoney(payment.principalAmount)} ریال</span>
-                      </div>
-                      <div>
-                        <span className="text-gray-600">سود: </span>
-                        <span className="font-semibold">{formatMoney(payment.interestAmount)} ریال</span>
-                      </div>
-                      <div>
-                        <span className="text-gray-600">جریمه: </span>
-                        <span className="font-semibold">{formatMoney(payment.penaltyAmount)} ریال</span>
-                      </div>
-                    </div>
-                  </div>
+        <>
+          <div className="overflow-x-auto">
+            <Table>
+              <TableHeader>
+                <TableRow>
+                  <TableHead className="text-right">نام بانک</TableHead>
+                  <TableHead className="text-right">شماره تسهیلات</TableHead>
+                  <TableHead className="text-right">شماره قسط</TableHead>
+                  <TableHead className="text-right">مبلغ پرداختی</TableHead>
+                  <TableHead className="text-right">اصل</TableHead>
+                  <TableHead className="text-right">سود</TableHead>
+                  <TableHead className="text-right">جریمه</TableHead>
+                  <TableHead className="text-right">روش1 اصل</TableHead>
+                  <TableHead className="text-right">روش1 سود</TableHead>
+                  <TableHead className="text-right">روش1 جریمه</TableHead>
+                  <TableHead className="text-right">انحراف1 اصل</TableHead>
+                  <TableHead className="text-right">انحراف1 سود</TableHead>
+                  <TableHead className="text-right">انحراف1 جریمه</TableHead>
+                  <TableHead className="text-right">روش2 اصل</TableHead>
+                  <TableHead className="text-right">روش2 سود</TableHead>
+                  <TableHead className="text-right">روش2 جریمه</TableHead>
+                  <TableHead className="text-right">انحراف2 اصل</TableHead>
+                  <TableHead className="text-right">انحراف2 سود</TableHead>
+                  <TableHead className="text-right">انحراف2 جریمه</TableHead>
+                  <TableHead className="text-right">روش3 اصل</TableHead>
+                  <TableHead className="text-right">روش3 سود</TableHead>
+                  <TableHead className="text-right">روش3 جریمه</TableHead>
+                  <TableHead className="text-right">انحراف3 اصل</TableHead>
+                  <TableHead className="text-right">انحراف3 سود</TableHead>
+                  <TableHead className="text-right">انحراف3 جریمه</TableHead>
+                </TableRow>
+              </TableHeader>
+              <TableBody>
+                {paged.map((p) => {
+                  const alloc = calculateAllocationMethods(p);
+                  const m1 = alloc[0];
+                  const m2 = alloc[1];
+                  const m3 = alloc[2];
+                  return (
+                    <TableRow key={p.id}>
+                      <TableCell>{p.facilityInfo?.bankName}</TableCell>
+                      <TableCell>{p.facilityInfo?.facilityNumber}</TableCell>
+                      <TableCell>{p.installmentNumber}</TableCell>
+                      <TableCell>{formatCurrency(p.totalAmount)}</TableCell>
+                      <TableCell>{formatCurrency(p.principalAmount)}</TableCell>
+                      <TableCell>{formatCurrency(p.interestAmount)}</TableCell>
+                      <TableCell>{formatCurrency(p.penaltyAmount)}</TableCell>
+                      <TableCell>{formatCurrency(m1.principal)}</TableCell>
+                      <TableCell>{formatCurrency(m1.interest)}</TableCell>
+                      <TableCell>{formatCurrency(m1.penalty)}</TableCell>
+                      <TableCell>{formatCurrency(m1.diffPrincipal)}</TableCell>
+                      <TableCell>{formatCurrency(m1.diffInterest)}</TableCell>
+                      <TableCell>{formatCurrency(m1.diffPenalty)}</TableCell>
+                      <TableCell>{formatCurrency(m2.principal)}</TableCell>
+                      <TableCell>{formatCurrency(m2.interest)}</TableCell>
+                      <TableCell>{formatCurrency(m2.penalty)}</TableCell>
+                      <TableCell>{formatCurrency(m2.diffPrincipal)}</TableCell>
+                      <TableCell>{formatCurrency(m2.diffInterest)}</TableCell>
+                      <TableCell>{formatCurrency(m2.diffPenalty)}</TableCell>
+                      <TableCell>{formatCurrency(m3.principal)}</TableCell>
+                      <TableCell>{formatCurrency(m3.interest)}</TableCell>
+                      <TableCell>{formatCurrency(m3.penalty)}</TableCell>
+                      <TableCell>{formatCurrency(m3.diffPrincipal)}</TableCell>
+                      <TableCell>{formatCurrency(m3.diffInterest)}</TableCell>
+                      <TableCell>{formatCurrency(m3.diffPenalty)}</TableCell>
+                    </TableRow>
+                  );
+                })}
+              </TableBody>
+            </Table>
+          </div>
 
-                  <Table>
-                    <TableHeader>
-                      <TableRow>
-                        <TableHead className="text-right">روش تخصیص</TableHead>
-                        <TableHead className="text-right">اصل</TableHead>
-                        <TableHead className="text-right">سود</TableHead>
-                        <TableHead className="text-right">جریمه</TableHead>
-                        <TableHead className="text-right">باقیمانده</TableHead>
-                      </TableRow>
-                    </TableHeader>
-                    <TableBody>
-                      {allocationResults.map((result, index) => (
-                        <TableRow key={index}>
-                          <TableCell className="font-medium">{result.method}</TableCell>
-                          <TableCell className="text-blue-600 font-semibold">
-                            {formatMoney(result.principal.toString())} ریال
-                          </TableCell>
-                          <TableCell className="text-orange-600 font-semibold">
-                            {formatMoney(result.interest.toString())} ریال
-                          </TableCell>
-                          <TableCell className="text-red-600 font-semibold">
-                            {formatMoney(result.penalty.toString())} ریال
-                          </TableCell>
-                          <TableCell className={`font-semibold ${
-                            result.remaining > 0 ? 'text-green-600' : 'text-gray-500'
-                          }`}>
-                            {result.remaining > 0 ? `+${formatMoney(result.remaining.toString())}` : '0'} ریال
-                          </TableCell>
-                        </TableRow>
-                      ))}
-                    </TableBody>
-                  </Table>
-                </CardContent>
-              </Card>
-            );
-          })}
-        </div>
+          <div className="flex items-center justify-between mt-4">
+            <span className="text-sm">صفحه {page + 1} از {pageCount}</span>
+            <div className="space-x-2 space-x-reverse">
+              <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.max(p - 1, 0))} disabled={page === 0}>قبلی</Button>
+              <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.min(p + 1, pageCount - 1))} disabled={page >= pageCount - 1}>بعدی</Button>
+            </div>
+          </div>
+        </>
       )}
     </div>
   );
 };
 
 export default PaymentAllocationReport;
diff --git a/src/components/PaymentForm.tsx b/src/components/PaymentForm.tsx
index b6519f154ffc8210ebe22727aa8bd715bbb7839f..54e76dccca7f4cc64ea2245cbf4b13aed5eef6b3 100644
--- a/src/components/PaymentForm.tsx
+++ b/src/components/PaymentForm.tsx
@@ -19,75 +19,70 @@ interface Facility {
   amount: string;
   bankName: string;
   facilityNumber: string;
   installmentCount: string;
   installmentAmount: string;
   installments?: Installment[];
 }
 
 const PaymentForm = ({ onBack }: PaymentFormProps) => {
   const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
   const [formData, setFormData] = useState({
     facilityId: "",
     installmentNumber: "",
     paymentDate: "",
     principalAmount: "",
     interestAmount: "",
     penaltyAmount: "",
     totalAmount: "",
     notes: "",
   });
 
   useEffect(() => {
     try {
       const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
-      console.log("PaymentForm - loaded facilities:", savedFacilities);
       setFacilities(savedFacilities);
-    } catch (error) {
-      console.error('Error loading facilities:', error);
+    } catch {
       setFacilities([]);
     }
   }, []);
 
   useEffect(() => {
     const principal = parseFloat(formData.principalAmount) || 0;
     const interest = parseFloat(formData.interestAmount) || 0;
     const penalty = parseFloat(formData.penaltyAmount) || 0;
     const total = principal + interest + penalty;
     setFormData(prev => ({ ...prev, totalAmount: total.toString() }));
   }, [formData.principalAmount, formData.interestAmount, formData.penaltyAmount]);
 
   const selectedFacility = facilities.find(f => f.id.toString() === formData.facilityId);
   const availableInstallments = selectedFacility?.installments || [];
   const selectedInstallment = availableInstallments.find(
     inst => inst.id.toString() === formData.installmentNumber
   );
 
-  console.log("PaymentForm - selectedFacility:", selectedFacility);
-  console.log("PaymentForm - availableInstallments:", availableInstallments);
-  console.log("PaymentForm - formData:", formData);
 
   // Filter out any invalid facilities or installments
   const validFacilities = facilities.filter(facility => 
     facility && 
     facility.id !== null && 
     facility.id !== undefined && 
     facility.id.toString().trim() !== ""
   );
 
   const validInstallments = availableInstallments.filter(installment => 
     installment && 
     installment.id !== null && 
     installment.id !== undefined && 
     installment.id.toString().trim() !== ""
   );
 
   const calculateSettlementDays = () => {
     if (!selectedInstallment?.dueDate || !formData.paymentDate) return null;
     
     const dueDate = new Date(selectedInstallment.dueDate);
     const paymentDate = new Date(formData.paymentDate);
     
     const diffTime = paymentDate.getTime() - dueDate.getTime();
     const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
     
@@ -167,51 +162,50 @@ const PaymentForm = ({ onBack }: PaymentFormProps) => {
         </Button>
       </div>
 
       <Card className="bg-white shadow-lg">
         <CardHeader>
           <CardTitle>اطلاعات پرداخت</CardTitle>
         </CardHeader>
         <CardContent>
           <form onSubmit={handleSubmit} className="space-y-6">
             <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
               {/* Facility Selection */}
               <div className="space-y-2 md:col-span-2">
                 <Label htmlFor="facilityId">انتخاب تسهیلات *</Label>
                 {validFacilities.length > 0 ? (
                   <Select 
                     value={formData.facilityId || ""} 
                     onValueChange={(value) => setFormData({...formData, facilityId: value, installmentNumber: ""})}
                   >
                     <SelectTrigger>
                       <SelectValue placeholder="انتخاب کنید" />
                     </SelectTrigger>
                     <SelectContent>
                       {validFacilities.map((facility) => {
                         const key = `facility_${facility.id}`;
                         const value = facility.id.toString();
-                        console.log("Rendering facility SelectItem:", { key, value, facility });
                         return (
                           <SelectItem key={key} value={value}>
                             {`${facility.facilityType} - ${facility.bankName} - ${facility.facilityNumber}`}
                           </SelectItem>
                         );
                       })}
                     </SelectContent>
                   </Select>
                 ) : (
                   <div className="p-3 border rounded-md bg-gray-50 text-gray-500">
                     هیچ تسهیلاتی یافت نشد
                   </div>
                 )}
               </div>
 
               {/* Selected Facility Info */}
               {selectedFacility && (
                 <div className="md:col-span-2 p-4 bg-blue-50 rounded-lg">
                   <h4 className="font-semibold text-blue-800 mb-2">اطلاعات تسهیلات انتخاب شده:</h4>
                   <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                     <div>
                       <span className="text-gray-600">نوع: </span>
                       <span className="font-semibold">{selectedFacility.facilityType}</span>
                     </div>
                     <div>
@@ -223,51 +217,50 @@ const PaymentForm = ({ onBack }: PaymentFormProps) => {
                       <span className="font-semibold">{selectedFacility.facilityNumber}</span>
                     </div>
                     <div>
                       <span className="text-gray-600">مبلغ قسط: </span>
                       <span className="font-semibold">{formatMoney(selectedFacility.installmentAmount)} ریال</span>
                     </div>
                   </div>
                 </div>
               )}
 
               {/* Installment Number Dropdown */}
               <div className="space-y-2">
                 <Label htmlFor="installmentNumber">شماره قسط *</Label>
                 {selectedFacility && validInstallments.length > 0 ? (
                   <Select 
                     value={formData.installmentNumber || ""} 
                     onValueChange={handleInstallmentChange}
                   >
                     <SelectTrigger>
                       <SelectValue placeholder="انتخاب کنید" />
                     </SelectTrigger>
                     <SelectContent>
                       {validInstallments.map((installment) => {
                         const key = `installment_${installment.id}`;
                         const value = installment.id.toString();
-                        console.log("Rendering installment SelectItem:", { key, value, installment });
                         return (
                           <SelectItem key={key} value={value}>
                             {`قسط ${installment.id}`}
                           </SelectItem>
                         );
                       })}
                     </SelectContent>
                   </Select>
                 ) : (
                   <div className="p-3 border rounded-md bg-gray-50 text-gray-500">
                     {!selectedFacility ? "ابتدا تسهیلات را انتخاب کنید" : "هیچ قسطی یافت نشد"}
                   </div>
                 )}
               </div>
 
               {/* Selected Installment Info */}
               {selectedInstallment && (
                 <div className="space-y-2">
                   <Label>اطلاعات قسط انتخاب شده</Label>
                   <div className="p-3 bg-green-50 rounded-lg text-sm space-y-1">
                     <div>
                       <span className="text-gray-600">تاریخ سررسید: </span>
                       <span className="font-semibold">{selectedInstallment.dueDate || 'تعریف نشده'}</span>
                     </div>
                     <div>
diff --git a/src/components/TestDataManager.tsx b/src/components/TestDataManager.tsx
deleted file mode 100644
index 6d1d8adb764d510a0c4f64a11945c98d6e463541..0000000000000000000000000000000000000000
--- a/src/components/TestDataManager.tsx
+++ /dev/null
@@ -1,172 +0,0 @@
-
-import { useState } from "react";
-import { Button } from "@/components/ui/button";
-import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
-import { useToast } from "@/hooks/use-toast";
-import { loadSampleData, clearAllData, getSummary } from "@/data/sampleData";
-import { formatMoney } from "@/utils/formatMoney";
-import { DatabaseIcon, TrashIcon, RefreshCwIcon } from "lucide-react";
-
-interface TestDataManagerProps {
-  onBack: () => void;
-}
-
-const TestDataManager = ({ onBack }: TestDataManagerProps) => {
-  const { toast } = useToast();
-  const [summary, setSummary] = useState(getSummary());
-  const [isLoading, setIsLoading] = useState(false);
-
-  const refreshSummary = () => {
-    setSummary(getSummary());
-  };
-
-  const handleLoadSampleData = async () => {
-    setIsLoading(true);
-    try {
-      loadSampleData();
-      refreshSummary();
-      toast({
-        title: "موفقیت",
-        description: "داده‌های نمونه با موفقیت بارگذاری شد",
-      });
-    } catch (error) {
-      toast({
-        title: "خطا",
-        description: "خطا در بارگذاری داده‌های نمونه",
-        variant: "destructive",
-      });
-    } finally {
-      setIsLoading(false);
-    }
-  };
-
-  const handleClearAllData = async () => {
-    if (window.confirm("آیا از حذف تمام داده‌ها اطمینان دارید؟")) {
-      setIsLoading(true);
-      try {
-        clearAllData();
-        refreshSummary();
-        toast({
-          title: "موفقیت",
-          description: "تمام داده‌ها با موفقیت حذف شد",
-        });
-      } catch (error) {
-        toast({
-          title: "خطا",
-          description: "خطا در حذف داده‌ها",
-          variant: "destructive",
-        });
-      } finally {
-        setIsLoading(false);
-      }
-    }
-  };
-
-  return (
-    <div className="space-y-6">
-      <div className="flex items-center justify-between">
-        <h2 className="text-2xl font-bold text-gray-800">مدیریت داده‌های آزمایشی</h2>
-        <Button variant="outline" onClick={onBack}>
-          بازگشت
-        </Button>
-      </div>
-
-      {/* Current Data Summary */}
-      <Card className="bg-white shadow-lg">
-        <CardHeader className="flex flex-row items-center justify-between">
-          <CardTitle className="flex items-center gap-2">
-            <DatabaseIcon className="h-5 w-5" />
-            وضعیت فعلی داده‌ها
-          </CardTitle>
-          <Button variant="outline" size="sm" onClick={refreshSummary}>
-            <RefreshCwIcon className="h-4 w-4 ml-1" />
-            بروزرسانی
-          </Button>
-        </CardHeader>
-        <CardContent>
-          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
-            <div className="bg-blue-50 p-4 rounded-lg text-center">
-              <div className="text-2xl font-bold text-blue-600">{summary.facilitiesCount}</div>
-              <div className="text-sm text-blue-800">تسهیلات ثبت شده</div>
-            </div>
-            <div className="bg-green-50 p-4 rounded-lg text-center">
-              <div className="text-2xl font-bold text-green-600">{summary.paymentsCount}</div>
-              <div className="text-sm text-green-800">پرداخت ثبت شده</div>
-            </div>
-            <div className="bg-purple-50 p-4 rounded-lg text-center">
-              <div className="text-lg font-bold text-purple-600">
-                {formatMoney(summary.totalFacilityAmount.toString())}
-              </div>
-              <div className="text-sm text-purple-800">مجموع تسهیلات (ریال)</div>
-            </div>
-            <div className="bg-orange-50 p-4 rounded-lg text-center">
-              <div className="text-lg font-bold text-orange-600">
-                {formatMoney(summary.totalPaymentAmount.toString())}
-              </div>
-              <div className="text-sm text-orange-800">مجموع پرداخت‌ها (ریال)</div>
-            </div>
-          </div>
-        </CardContent>
-      </Card>
-
-      {/* Sample Data Info */}
-      <Card className="bg-white shadow-lg">
-        <CardHeader>
-          <CardTitle>درباره داده‌های نمونه</CardTitle>
-        </CardHeader>
-        <CardContent className="space-y-4">
-          <div className="bg-yellow-50 p-4 rounded-lg">
-            <h4 className="font-semibold text-yellow-800 mb-2">داده‌های نمونه شامل:</h4>
-            <ul className="list-disc list-inside text-sm text-yellow-700 space-y-1">
-              <li>5 تسهیلات مختلف (مسکن، خودرو، کارگشایی، رفاهی، تولیدی)</li>
-              <li>بانک‌های مختلف (ملی، صادرات، پارسیان، تجارت، کشاورزی)</li>
-              <li>اقساط تعریف شده برای هر تسهیلات</li>
-              <li>6 پرداخت نمونه با وضعیت‌های مختلف</li>
-              <li>مبالغ واقع‌گرایانه و تاریخ‌های منطقی</li>
-            </ul>
-          </div>
-        </CardContent>
-      </Card>
-
-      {/* Action Buttons */}
-      <Card className="bg-white shadow-lg">
-        <CardHeader>
-          <CardTitle>عملیات</CardTitle>
-        </CardHeader>
-        <CardContent>
-          <div className="flex flex-col sm:flex-row gap-4">
-            <Button
-              onClick={handleLoadSampleData}
-              disabled={isLoading}
-              className="bg-blue-600 hover:bg-blue-700 flex-1"
-            >
-              <DatabaseIcon className="h-4 w-4 ml-2" />
-              {isLoading ? "در حال بارگذاری..." : "بارگذاری داده‌های نمونه"}
-            </Button>
-            
-            <Button
-              onClick={handleClearAllData}
-              disabled={isLoading}
-              variant="destructive"
-              className="flex-1"
-            >
-              <TrashIcon className="h-4 w-4 ml-2" />
-              {isLoading ? "در حال حذف..." : "حذف تمام داده‌ها"}
-            </Button>
-          </div>
-          
-          <div className="mt-4 text-sm text-gray-600">
-            <p className="mb-2">
-              <strong>بارگذاری داده‌های نمونه:</strong> داده‌های موجود را جایگزین می‌کند و 5 تسهیلات و 6 پرداخت نمونه اضافه می‌کند.
-            </p>
-            <p>
-              <strong>حذف تمام داده‌ها:</strong> تمام تسهیلات و پرداخت‌های ذخیره شده را حذف می‌کند.
-            </p>
-          </div>
-        </CardContent>
-      </Card>
-    </div>
-  );
-};
-
-export default TestDataManager;
diff --git a/src/components/common/ManageListDialog.tsx b/src/components/common/ManageListDialog.tsx
new file mode 100644
index 0000000000000000000000000000000000000000..0e57058ade87e94ed2c20d8c4fd25a59767c71aa
--- /dev/null
+++ b/src/components/common/ManageListDialog.tsx
@@ -0,0 +1,77 @@
+import React, { useState } from "react";
+import { Dialog, DialogContent } from "@/components/ui/dialog";
+import { Button } from "@/components/ui/button";
+import { Input } from "@/components/ui/input";
+import { Trash } from "lucide-react";
+
+interface ManageListDialogProps {
+  open: boolean;
+  onOpenChange: (open: boolean) => void;
+  items: string[];
+  onChange: (items: string[]) => void;
+  title: string;
+}
+
+const ManageListDialog = ({
+  open,
+  onOpenChange,
+  items,
+  onChange,
+  title,
+}: ManageListDialogProps) => {
+  const [newItem, setNewItem] = useState("");
+
+  const handleAdd = () => {
+    const value = newItem.trim();
+    if (!value) return;
+    onChange([...items, value]);
+    setNewItem("");
+  };
+
+  const handleUpdate = (index: number, value: string) => {
+    const list = [...items];
+    list[index] = value;
+    onChange(list);
+  };
+
+  const handleDelete = (index: number) => {
+    const list = [...items];
+    list.splice(index, 1);
+    onChange(list);
+  };
+
+  return (
+    <Dialog open={open} onOpenChange={onOpenChange}>
+      <DialogContent className="space-y-4 max-w-sm">
+        <h3 className="text-lg font-bold">{title}</h3>
+        {items.map((item, idx) => (
+          <div key={idx} className="flex gap-2 items-center">
+            <Input
+              value={item}
+              onChange={(e) => handleUpdate(idx, e.target.value)}
+              className="flex-1"
+            />
+            <Button
+              size="icon"
+              variant="outline"
+              onClick={() => handleDelete(idx)}
+            >
+              <Trash className="h-4 w-4" />
+            </Button>
+          </div>
+        ))}
+        <div className="flex gap-2">
+          <Input
+            placeholder="افزودن مورد جدید"
+            value={newItem}
+            onChange={(e) => setNewItem(e.target.value)}
+            className="flex-1"
+          />
+          <Button onClick={handleAdd}>افزودن</Button>
+        </div>
+      </DialogContent>
+    </Dialog>
+  );
+};
+
+export default ManageListDialog;
diff --git a/src/components/common/SearchableSelect.tsx b/src/components/common/SearchableSelect.tsx
new file mode 100644
index 0000000000000000000000000000000000000000..d0912eb9c2e3620139d2b687e1179a8db9398da3
--- /dev/null
+++ b/src/components/common/SearchableSelect.tsx
@@ -0,0 +1,93 @@
+import React from "react";
+import { Button } from "@/components/ui/button";
+import {
+  Command,
+  CommandEmpty,
+  CommandGroup,
+  CommandInput,
+  CommandItem,
+  CommandList,
+} from "@/components/ui/command";
+import {
+  Popover,
+  PopoverContent,
+  PopoverTrigger,
+} from "@/components/ui/popover";
+import { Check, ChevronsUpDown } from "lucide-react";
+import { cn } from "@/lib/utils";
+
+interface SearchableSelectProps {
+  label: string;
+  options: string[];
+  value: string;
+  onValueChange: (value: string) => void;
+  placeholder?: string;
+  required?: boolean;
+}
+
+const SearchableSelect = ({
+  label,
+  options,
+  value,
+  onValueChange,
+  placeholder = "انتخاب کنید",
+  required = false,
+}: SearchableSelectProps) => {
+  const [open, setOpen] = React.useState(false);
+
+  const getLabel = (opt: string) =>
+    opt.includes("|") ? opt.split("|")[1] : opt;
+
+  const selectedLabel = value ? getLabel(value) : "";
+
+  return (
+    <div className="space-y-2">
+      <label className="text-sm font-medium">
+        {label} {required && <span className="text-red-500">*</span>}
+      </label>
+      <Popover open={open} onOpenChange={setOpen}>
+        <PopoverTrigger asChild>
+          <Button
+            variant="outline"
+            role="combobox"
+            aria-expanded={open}
+            className="w-full justify-between"
+          >
+            {selectedLabel || placeholder}
+            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
+          </Button>
+        </PopoverTrigger>
+        <PopoverContent className="p-0" align="start">
+          <Command>
+            <CommandInput placeholder="جستجو..." className="text-sm" />
+            <CommandList>
+              <CommandEmpty>موردی یافت نشد.</CommandEmpty>
+              <CommandGroup>
+                {options.map((option) => (
+                  <CommandItem
+                    key={option}
+                    value={option}
+                    onSelect={(current) => {
+                      onValueChange(current);
+                      setOpen(false);
+                    }}
+                  >
+                    <Check
+                      className={cn(
+                        "ml-2 h-4 w-4",
+                        value === option ? "opacity-100" : "opacity-0",
+                      )}
+                    />
+                    {getLabel(option)}
+                  </CommandItem>
+                ))}
+              </CommandGroup>
+            </CommandList>
+          </Command>
+        </PopoverContent>
+      </Popover>
+    </div>
+  );
+};
+
+export default SearchableSelect;
diff --git a/src/components/facility/FacilityFormFields.tsx b/src/components/facility/FacilityFormFields.tsx
index 7327353afa3f3bef0e64afa281f4487713523716..430589a7cc54a8a76fad51d7fffbb0aaaf85faae 100644
--- a/src/components/facility/FacilityFormFields.tsx
+++ b/src/components/facility/FacilityFormFields.tsx
@@ -24,117 +24,112 @@ const facilityTypes = [
   "تحصیلی",
   "سایر"
 ];
 
 const banks = [
   "بانک ملی ایران",
   "بانک صادرات ایران",
   "بانک تجارت",
   "بانک کشاورزی",
   "بانک صنعت و معدن",
   "بانک رفاه کارگران",
   "بانک توسعه تعاون",
   "بانک پست بانک",
   "بانک دی",
   "بانک پارسیان",
   "بانک پاسارگاد",
   "بانک کارآفرین",
   "سایر"
 ];
 
 const FacilityFormFields = ({ formData, onUpdateFormData, onAddInstallments }: FacilityFormFieldsProps) => {
   // Filter out any empty or invalid facility types and banks
   const validFacilityTypes = facilityTypes.filter(type => type && type.trim() !== "");
   const validBanks = banks.filter(bank => bank && bank.trim() !== "");
 
-  console.log("FacilityFormFields - formData:", formData);
-  console.log("FacilityFormFields - validFacilityTypes:", validFacilityTypes);
-  console.log("FacilityFormFields - validBanks:", validBanks);
 
   return (
     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
       {/* نوع تسهیلات */}
       <div className="space-y-2">
         <Label htmlFor="facilityType">نوع تسهیلات *</Label>
         <Select 
           value={formData.facilityType || ""} 
           onValueChange={(value) => onUpdateFormData({facilityType: value})}
         >
           <SelectTrigger>
             <SelectValue placeholder="انتخاب کنید" />
           </SelectTrigger>
           <SelectContent>
             {validFacilityTypes.map((type, index) => {
               const key = `facility_type_${index}`;
-              console.log("Rendering facility type SelectItem:", { key, value: type });
               return (
                 <SelectItem key={key} value={type}>
                   {type}
                 </SelectItem>
               );
             })}
           </SelectContent>
         </Select>
       </div>
 
       {/* مبلغ تسهیلات با فرمت مبلغ */}
       <div className="space-y-2">
         <Label htmlFor="amount">مبلغ تسهیلات (ریال) *</Label>
         <MoneyInput
           id="amount"
           value={formData.amount}
           onChange={(value) => onUpdateFormData({amount: value})}
           placeholder="مثال: 500000000"
         />
       </div>
 
       {/* تاریخ اخذ */}
       <div className="space-y-2">
         <Label htmlFor="receivedDate">تاریخ اخذ *</Label>
         <Input
           id="receivedDate"
           type="text"
           placeholder="yyyy/mm/dd"
           value={formData.receivedDate}
           onChange={(e) => onUpdateFormData({receivedDate: e.target.value})}
         />
       </div>
 
       {/* نام بانک */}
       <div className="space-y-2">
         <Label htmlFor="bankName">نام بانک *</Label>
         <Select 
           value={formData.bankName || ""} 
           onValueChange={(value) => onUpdateFormData({bankName: value})}
         >
           <SelectTrigger>
             <SelectValue placeholder="انتخاب کنید" />
           </SelectTrigger>
           <SelectContent>
             {validBanks.map((bank, index) => {
               const key = `bank_${index}`;
-              console.log("Rendering bank SelectItem:", { key, value: bank });
               return (
                 <SelectItem key={key} value={bank}>
                   {bank}
                 </SelectItem>
               );
             })}
           </SelectContent>
         </Select>
       </div>
 
       {/* شماره تسهیلات */}
       <div className="space-y-2">
         <Label htmlFor="facilityNumber">شماره تسهیلات *</Label>
         <Input
           id="facilityNumber"
           placeholder="شماره قرارداد تسهیلات"
           value={formData.facilityNumber}
           onChange={(e) => onUpdateFormData({facilityNumber: e.target.value})}
         />
       </div>
 
       {/* تعداد اقساط */}
       <div className="space-y-2">
         <Label htmlFor="installmentCount">تعداد اقساط *</Label>
         <div className="flex gap-2">
diff --git a/src/components/facility/SimpleFacilityForm.tsx b/src/components/facility/SimpleFacilityForm.tsx
index 2d9135c752c38b37d79cf3c956394243f4b5683e..5fb8b4454a169476cd727db63f558f795ef3ccb1 100644
--- a/src/components/facility/SimpleFacilityForm.tsx
+++ b/src/components/facility/SimpleFacilityForm.tsx
@@ -1,190 +1,367 @@
-
 import { useState } from "react";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Button } from "@/components/ui/button";
 import { useToast } from "@/hooks/use-toast";
-import SimpleSelect from "@/components/common/SimpleSelect";
+import SearchableSelect from "@/components/common/SearchableSelect";
+import ManageListDialog from "@/components/common/ManageListDialog";
 import SimpleInput from "@/components/common/SimpleInput";
+import { Input } from "@/components/ui/input";
 import CurrencyInput from "@/components/common/CurrencyInput";
-import { FACILITY_TYPES, BANK_NAMES } from "@/utils/constants";
-import { addFacility } from "@/utils/storage";
-import { FacilityFormData } from "@/types/types";
+import InstallmentTable from "./InstallmentTable";
+import { Installment } from "@/types/facility";
+import {
+  addFacility,
+  updateFacility,
+  getBankNames,
+  saveBankNames,
+  getFacilityTypes,
+  saveFacilityTypes,
+} from "@/utils/storage";
+import { FacilityFormData, Facility } from "@/types/types";
 
 interface SimpleFacilityFormProps {
   onBack: () => void;
   onSuccess?: () => void;
+  facility?: Facility;
 }
 
-const SimpleFacilityForm = ({ onBack, onSuccess }: SimpleFacilityFormProps) => {
+const SimpleFacilityForm = ({
+  onBack,
+  onSuccess,
+  facility,
+}: SimpleFacilityFormProps) => {
   const { toast } = useToast();
-  const [formData, setFormData] = useState<FacilityFormData>({
-    type: "",
-    amount: "",
-    bankName: "",
-    contractNumber: "",
-    installmentCount: "",
-    interestRate: "",
-    penaltyRate: "",
-    installmentAmount: "",
-    receivedDate: "",
-    firstInstallmentDate: ""
-  });
+  const [formData, setFormData] = useState<FacilityFormData>(() => ({
+    type: facility?.type || "",
+    amount: facility?.amount?.toString() || "",
+    bankName: facility?.bankName || "",
+    contractNumber: facility?.contractNumber || "",
+    installmentCount: facility?.installmentCount?.toString() || "",
+    interestRate: facility?.interestRate?.toString() || "",
+    penaltyRate: facility?.penaltyRate?.toString() || "",
+    receivedDate: facility?.receivedDate || "",
+    firstInstallmentDate: facility?.firstInstallmentDate || "",
+  }));
+
+  const [installments, setInstallments] = useState<Installment[]>(
+    () => facility?.installments || [],
+  );
+  const [showInstallments, setShowInstallments] = useState(
+    () => (facility?.installments?.length ?? 0) > 0,
+  );
+  const [bankOptions, setBankOptions] = useState<string[]>(getBankNames());
+  const [typeOptions, setTypeOptions] = useState<string[]>(getFacilityTypes());
+  const [bankDialog, setBankDialog] = useState(false);
+  const [typeDialog, setTypeDialog] = useState(false);
 
   const updateField = (field: keyof FacilityFormData, value: string) => {
-    setFormData(prev => ({ ...prev, [field]: value }));
+    setFormData((prev) => ({ ...prev, [field]: value }));
+  };
+
+  const handleAddInstallments = (e: React.MouseEvent<HTMLButtonElement>) => {
+    e.preventDefault();
+    const count = parseInt(formData.installmentCount);
+    if (count > 0) {
+      const list: Installment[] = Array.from({ length: count }, (_, i) => ({
+        id: i + 1,
+        dueDate: "",
+        principalAmount: "",
+        interestAmount: "",
+      }));
+      setInstallments(list);
+      setShowInstallments(true);
+    }
+  };
+
+  const updateInstallment = (
+    id: number,
+    field: keyof Installment,
+    value: string,
+  ) => {
+    setInstallments((prev) =>
+      prev.map((inst) => (inst.id === id ? { ...inst, [field]: value } : inst)),
+    );
+  };
+
+  const handleBankListChange = (list: string[]) => {
+    setBankOptions(list);
+    saveBankNames(list);
+  };
+
+  const handleTypeListChange = (list: string[]) => {
+    setTypeOptions(list);
+    saveFacilityTypes(list);
   };
 
   const handleSubmit = (e: React.FormEvent) => {
     e.preventDefault();
-    
+
     // بررسی فیلدهای ضروری
-    const requiredFields = ['type', 'amount', 'bankName', 'contractNumber', 'installmentCount', 'interestRate'];
-    const missing = requiredFields.filter(field => !formData[field as keyof FacilityFormData]);
-    
+    const requiredFields = [
+      "type",
+      "amount",
+      "bankName",
+      "contractNumber",
+      "installmentCount",
+      "interestRate",
+    ];
+    const missing = requiredFields.filter(
+      (field) => !formData[field as keyof FacilityFormData],
+    );
+
     if (missing.length > 0) {
       toast({
         title: "خطا",
         description: "لطفاً تمام فیلدهای ضروری را پر کنید",
         variant: "destructive",
       });
       return;
     }
 
-    try {
-      addFacility({
-        type: formData.type,
-        amount: parseInt(formData.amount),
-        bankName: formData.bankName,
-        contractNumber: formData.contractNumber,
-        installmentCount: parseInt(formData.installmentCount),
-        interestRate: parseFloat(formData.interestRate),
-        penaltyRate: parseFloat(formData.penaltyRate || "0"),
-        installmentAmount: parseInt(formData.installmentAmount || "0"),
-        receivedDate: formData.receivedDate,
-        firstInstallmentDate: formData.firstInstallmentDate
-      });
-
+    const totalPrincipal = installments.reduce(
+      (sum, i) => sum + (parseInt(i.principalAmount) || 0),
+      0,
+    );
+    if (showInstallments && totalPrincipal !== parseInt(formData.amount)) {
       toast({
-        title: "موفقیت",
-        description: "تسهیلات با موفقیت ثبت شد",
+        title: "خطا",
+        description: "جمع مبالغ اصل اقساط با مبلغ تسهیلات برابر نیست",
+        variant: "destructive",
       });
+      return;
+    }
+
+    try {
+      if (facility) {
+        updateFacility(facility.id, {
+          type: formData.type,
+          amount: parseInt(formData.amount),
+          bankName: formData.bankName,
+          contractNumber: formData.contractNumber,
+          installmentCount: parseInt(formData.installmentCount),
+          interestRate: parseFloat(formData.interestRate),
+          penaltyRate: parseFloat(formData.penaltyRate || "0"),
+          installmentAmount:
+            parseInt(formData.amount) /
+            parseInt(formData.installmentCount || "1"),
+          receivedDate: formData.receivedDate,
+          firstInstallmentDate: formData.firstInstallmentDate,
+          installments,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "تسهیلات با موفقیت ویرایش شد",
+        });
+      } else {
+        addFacility({
+          type: formData.type,
+          amount: parseInt(formData.amount),
+          bankName: formData.bankName,
+          contractNumber: formData.contractNumber,
+          installmentCount: parseInt(formData.installmentCount),
+          interestRate: parseFloat(formData.interestRate),
+          penaltyRate: parseFloat(formData.penaltyRate || "0"),
+          installmentAmount:
+            parseInt(formData.amount) /
+            parseInt(formData.installmentCount || "1"),
+          receivedDate: formData.receivedDate,
+          firstInstallmentDate: formData.firstInstallmentDate,
+          installments,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "تسهیلات با موفقیت ثبت شد",
+        });
+      }
 
       if (onSuccess) onSuccess();
       else onBack();
     } catch (error) {
       toast({
         title: "خطا",
         description: "خطا در ثبت تسهیلات",
         variant: "destructive",
       });
     }
   };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
-        <h2 className="text-2xl font-bold">ثبت تسهیلات جدید</h2>
+        <h2 className="text-2xl font-bold">
+          {facility ? "ویرایش تسهیلات" : "ثبت تسهیلات جدید"}
+        </h2>
         <Button variant="outline" onClick={onBack}>
           بازگشت
         </Button>
       </div>
 
       <Card>
         <CardHeader>
           <CardTitle>اطلاعات تسهیلات</CardTitle>
         </CardHeader>
         <CardContent>
           <form onSubmit={handleSubmit} className="space-y-6">
-            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
-              <SimpleSelect
-                label="نوع تسهیلات"
-                value={formData.type}
-                onValueChange={(value) => updateField('type', value)}
-                options={FACILITY_TYPES}
-                required
-              />
+            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
+              <div className="flex items-end gap-2">
+                <div className="flex-1">
+                  <SearchableSelect
+                    label="نوع تسهیلات"
+                    value={formData.type}
+                    onValueChange={(value) => updateField("type", value)}
+                    options={typeOptions}
+                    required
+                  />
+                </div>
+                <Button
+                  type="button"
+                  variant="outline"
+                  size="sm"
+                  onClick={() => setTypeDialog(true)}
+                >
+                  افزودن نوع
+                </Button>
+              </div>
 
               <CurrencyInput
                 label="مبلغ تسهیلات (ریال)"
                 value={formData.amount}
-                onChange={(value) => updateField('amount', value)}
+                onChange={(value) => updateField("amount", value)}
                 required
               />
 
-              <SimpleSelect
-                label="نام بانک"
-                value={formData.bankName}
-                onValueChange={(value) => updateField('bankName', value)}
-                options={BANK_NAMES}
-                required
-              />
+              <div className="flex items-end gap-2">
+                <div className="flex-1">
+                  <SearchableSelect
+                    label="نام بانک"
+                    value={formData.bankName}
+                    onValueChange={(value) => updateField("bankName", value)}
+                    options={bankOptions}
+                    required
+                  />
+                </div>
+                <Button
+                  type="button"
+                  variant="outline"
+                  size="sm"
+                  onClick={() => setBankDialog(true)}
+                >
+                  افزودن بانک
+                </Button>
+              </div>
 
               <SimpleInput
                 label="شماره قرارداد"
                 value={formData.contractNumber}
-                onChange={(value) => updateField('contractNumber', value)}
+                onChange={(value) => updateField("contractNumber", value)}
                 required
               />
 
-              <SimpleInput
-                label="تعداد اقساط"
-                value={formData.installmentCount}
-                onChange={(value) => updateField('installmentCount', value)}
-                type="number"
-                required
-              />
+              <div className="space-y-2">
+                <label
+                  htmlFor="installmentCount"
+                  className="text-sm font-medium"
+                >
+                  تعداد اقساط *
+                </label>
+                <div className="flex gap-2">
+                  <Input
+                    id="installmentCount"
+                    type="number"
+                    value={formData.installmentCount}
+                    onChange={(e) =>
+                      updateField("installmentCount", e.target.value)
+                    }
+                    className="flex-1"
+                  />
+                  {formData.installmentCount && (
+                    <Button
+                      type="button"
+                      variant="outline"
+                      onClick={handleAddInstallments}
+                    >
+                      افزودن اقساط
+                    </Button>
+                  )}
+                </div>
+              </div>
 
               <SimpleInput
                 label="نرخ سود (درصد)"
                 value={formData.interestRate}
-                onChange={(value) => updateField('interestRate', value)}
+                onChange={(value) => updateField("interestRate", value)}
                 type="number"
                 step="0.1"
                 required
               />
 
               <SimpleInput
                 label="نرخ جریمه (درصد)"
                 value={formData.penaltyRate}
-                onChange={(value) => updateField('penaltyRate', value)}
+                onChange={(value) => updateField("penaltyRate", value)}
                 type="number"
                 step="0.1"
               />
 
-              <CurrencyInput
-                label="مبلغ هر قسط (ریال)"
-                value={formData.installmentAmount}
-                onChange={(value) => updateField('installmentAmount', value)}
-              />
+              <div className="space-y-2">
+                <label className="text-sm font-medium">تاریخ اخذ</label>
+                <Input
+                  value={formData.receivedDate}
+                  onChange={(e) => updateField("receivedDate", e.target.value)}
+                  placeholder="YYYY/MM/DD"
+                  pattern="\d{4}/\d{2}/\d{2}"
+                />
+              </div>
 
-              <SimpleInput
-                label="تاریخ اخذ"
-                value={formData.receivedDate}
-                onChange={(value) => updateField('receivedDate', value)}
-                type="date"
-              />
+              <div className="space-y-2">
+                <label className="text-sm font-medium">تاریخ اولین قسط</label>
+                <Input
+                  value={formData.firstInstallmentDate}
+                  onChange={(e) =>
+                    updateField("firstInstallmentDate", e.target.value)
+                  }
+                  placeholder="YYYY/MM/DD"
+                  pattern="\d{4}/\d{2}/\d{2}"
+                />
+              </div>
+            </div>
 
-              <SimpleInput
-                label="تاریخ اولین قسط"
-                value={formData.firstInstallmentDate}
-                onChange={(value) => updateField('firstInstallmentDate', value)}
-                type="date"
+            {showInstallments && installments.length > 0 && (
+              <InstallmentTable
+                installments={installments}
+                onUpdateInstallment={updateInstallment}
               />
-            </div>
+            )}
 
             <div className="flex justify-end space-x-2 space-x-reverse">
               <Button type="button" variant="outline" onClick={onBack}>
                 لغو
               </Button>
               <Button type="submit">
-                ثبت تسهیلات
+                {facility ? "ذخیره تغییرات" : "ثبت تسهیلات"}
               </Button>
             </div>
           </form>
         </CardContent>
       </Card>
+      <ManageListDialog
+        open={bankDialog}
+        onOpenChange={setBankDialog}
+        items={bankOptions}
+        onChange={handleBankListChange}
+        title="مدیریت بانک‌ها"
+      />
+      <ManageListDialog
+        open={typeDialog}
+        onOpenChange={setTypeDialog}
+        items={typeOptions}
+        onChange={handleTypeListChange}
+        title="مدیریت نوع تسهیلات"
+      />
     </div>
   );
 };
 
 export default SimpleFacilityForm;
diff --git a/src/components/lists/SimpleFacilityList.tsx b/src/components/lists/SimpleFacilityList.tsx
index 0361e752ff385c7b44e74246ff146c3c7a985831..38f4321a824904afaf19ccae065ab6803c98b9db 100644
--- a/src/components/lists/SimpleFacilityList.tsx
+++ b/src/components/lists/SimpleFacilityList.tsx
@@ -1,137 +1,201 @@
-
-import { useState, useEffect } from "react";
-import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
+import { useEffect, useMemo, useState } from "react";
 import { Button } from "@/components/ui/button";
-import { Badge } from "@/components/ui/badge";
-import { getFacilities, getPayments } from "@/utils/storage";
-import { formatCurrency, formatDate } from "@/utils/formatters";
+import { Input } from "@/components/ui/input";
+import {
+  Table,
+  TableBody,
+  TableCell,
+  TableHead,
+  TableHeader,
+  TableRow,
+} from "@/components/ui/table";
+import { Dialog, DialogContent } from "@/components/ui/dialog";
+import { Edit, Trash2, Plus } from "lucide-react";
+import {
+  getFacilities,
+  getPayments,
+  deleteFacility,
+  deletePaymentsByFacility,
+} from "@/utils/storage";
+import { formatCurrency } from "@/utils/formatters";
 import { Facility, Payment } from "@/types/types";
-import { Plus } from "lucide-react";
+import SimpleFacilityForm from "@/components/facility/SimpleFacilityForm";
+import SimplePaymentForm from "@/components/payment/SimplePaymentForm";
+import { useToast } from "@/hooks/use-toast";
 
 interface SimpleFacilityListProps {
   onBack: () => void;
   onAddFacility: () => void;
   onAddPayment: () => void;
 }
 
-const SimpleFacilityList = ({ onBack, onAddFacility, onAddPayment }: SimpleFacilityListProps) => {
+const SimpleFacilityList = ({
+  onBack,
+  onAddFacility,
+  onAddPayment,
+}: SimpleFacilityListProps) => {
+  const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
   const [payments, setPayments] = useState<Payment[]>([]);
+  const [search, setSearch] = useState("");
+  const [editFacility, setEditFacility] = useState<Facility | null>(null);
+  const [editPayment, setEditPayment] = useState<Payment | null>(null);
 
   useEffect(() => {
+    refreshData();
+  }, []);
+
+  const refreshData = () => {
     setFacilities(getFacilities());
     setPayments(getPayments());
-  }, []);
+  };
+
+  const facilityPayments = (id: number) =>
+    payments.filter((p) => p.facilityId === id);
+
+  const filteredFacilities = useMemo(
+    () =>
+      facilities.filter(
+        (f) =>
+          f.bankName.includes(search) || f.contractNumber.includes(search),
+      ),
+    [facilities, search],
+  );
+
+  const handleDelete = (facility: Facility) => {
+    if (facilityPayments(facility.id).length > 0) return;
+    deleteFacility(facility.id);
+    deletePaymentsByFacility(facility.id);
+    toast({ title: "حذف شد" });
+    refreshData();
+  };
 
-  const getFacilityPayments = (facilityId: number) => {
-    return payments.filter(p => p.facilityId === facilityId);
+  const handleFacilitySaved = () => {
+    setEditFacility(null);
+    refreshData();
   };
 
-  const getTotalPaid = (facilityId: number) => {
-    return getFacilityPayments(facilityId).reduce((sum, p) => sum + p.totalAmount, 0);
+  const handlePaymentSaved = () => {
+    setEditPayment(null);
+    refreshData();
   };
 
-  const getPaymentStatus = (facility: Facility) => {
-    const totalPaid = getTotalPaid(facility.id);
-    const percentage = (totalPaid / facility.amount) * 100;
-    
-    if (percentage === 0) return { text: "پرداخت نشده", color: "bg-red-500" };
-    if (percentage < 100) return { text: "در حال پرداخت", color: "bg-yellow-500" };
-    return { text: "تکمیل شده", color: "bg-green-500" };
+  const paidInfo = (id: number) => {
+    const list = facilityPayments(id);
+    const principal = list.reduce((s, p) => s + p.principalAmount, 0);
+    const interest = list.reduce((s, p) => s + p.interestAmount, 0);
+    const penalty = list.reduce((s, p) => s + p.penaltyAmount, 0);
+    return { principal, interest, penalty, count: list.length };
   };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
         <h2 className="text-2xl font-bold">لیست تسهیلات</h2>
         <div className="flex gap-2">
           <Button onClick={onAddPayment} variant="outline" className="flex items-center gap-2">
-            <Plus className="h-4 w-4" />
-            پرداخت جدید
+            <Plus className="h-4 w-4" /> پرداخت جدید
           </Button>
           <Button onClick={onAddFacility} className="flex items-center gap-2">
-            <Plus className="h-4 w-4" />
-            تسهیلات جدید
+            <Plus className="h-4 w-4" /> تسهیلات جدید
           </Button>
           <Button variant="outline" onClick={onBack}>
             بازگشت
           </Button>
         </div>
       </div>
 
-      {facilities.length === 0 ? (
-        <Card>
-          <CardContent className="p-8 text-center">
-            <p className="text-gray-500 mb-4">هیچ تسهیلاتی ثبت نشده است</p>
-            <Button onClick={onAddFacility}>ثبت اولین تسهیلات</Button>
-          </CardContent>
-        </Card>
-      ) : (
-        <div className="space-y-4">
-          {facilities.map((facility) => {
-            const facilityPayments = getFacilityPayments(facility.id);
-            const totalPaid = getTotalPaid(facility.id);
-            const status = getPaymentStatus(facility);
-            
-            return (
-              <Card key={facility.id}>
-                <CardHeader>
-                  <div className="flex justify-between items-start">
-                    <div>
-                      <CardTitle className="text-lg">
-                        {facility.type} - {facility.bankName}
-                      </CardTitle>
-                      <p className="text-sm text-gray-600">
-                        شماره قرارداد: {facility.contractNumber}
-                      </p>
-                    </div>
-                    <Badge className={`${status.color} text-white`}>
-                      {status.text}
-                    </Badge>
-                  </div>
-                </CardHeader>
-                <CardContent>
-                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
-                    <div>
-                      <span className="text-sm text-gray-600">مبلغ کل:</span>
-                      <p className="font-semibold">{formatCurrency(facility.amount)} ریال</p>
-                    </div>
-                    <div>
-                      <span className="text-sm text-gray-600">پرداخت شده:</span>
-                      <p className="font-semibold text-green-600">{formatCurrency(totalPaid)} ریال</p>
-                    </div>
-                    <div>
-                      <span className="text-sm text-gray-600">تعداد اقساط:</span>
-                      <p className="font-semibold">{facility.installmentCount}</p>
-                    </div>
-                    <div>
-                      <span className="text-sm text-gray-600">نرخ سود:</span>
-                      <p className="font-semibold">{facility.interestRate}%</p>
-                    </div>
-                  </div>
-                  
-                  {facilityPayments.length > 0 && (
-                    <div>
-                      <h5 className="font-semibold mb-2">پرداخت‌های اخیر:</h5>
-                      <div className="space-y-2">
-                        {facilityPayments.slice(-3).map((payment) => (
-                          <div key={payment.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
-                            <span className="text-sm">قسط {payment.installmentNumber}</span>
-                            <span className="text-sm font-semibold">{formatCurrency(payment.totalAmount)} ریال</span>
-                            <span className="text-xs text-gray-500">{formatDate(payment.paymentDate)}</span>
-                          </div>
-                        ))}
-                      </div>
+      <Input
+        placeholder="جستجوی بانک یا شماره تسهیلات"
+        value={search}
+        onChange={(e) => setSearch(e.target.value)}
+      />
+
+      <div className="overflow-x-auto">
+        <Table>
+          <TableHeader>
+            <TableRow>
+              <TableHead className="text-right">نام بانک</TableHead>
+              <TableHead className="text-right">شماره تسهیلات</TableHead>
+              <TableHead className="text-right">مبلغ کل</TableHead>
+              <TableHead className="text-right">تعداد اقساط</TableHead>
+              <TableHead className="text-right">اقساط تسویه شده</TableHead>
+              <TableHead className="text-right">اقساط تسویه نشده</TableHead>
+              <TableHead className="text-right">مبلغ تسویه شده</TableHead>
+              <TableHead className="text-right">باقیمانده اصل</TableHead>
+              <TableHead className="text-right">سود پرداخت شده</TableHead>
+              <TableHead className="text-right">جریمه پرداخت شده</TableHead>
+              <TableHead></TableHead>
+            </TableRow>
+          </TableHeader>
+          <TableBody>
+            {filteredFacilities.map((f) => {
+              const info = paidInfo(f.id);
+              const unpaidCount = f.installmentCount - info.count;
+              const unpaidAmount = f.amount - info.principal;
+              return (
+                <TableRow key={f.id} className="hover:bg-gray-50">
+                  <TableCell>{f.bankName}</TableCell>
+                  <TableCell>{f.contractNumber}</TableCell>
+                  <TableCell>{formatCurrency(f.amount)} ریال</TableCell>
+                  <TableCell>{f.installmentCount}</TableCell>
+                  <TableCell>{info.count}</TableCell>
+                  <TableCell>{unpaidCount}</TableCell>
+                  <TableCell>{formatCurrency(info.principal)} ریال</TableCell>
+                  <TableCell>{formatCurrency(unpaidAmount)} ریال</TableCell>
+                  <TableCell>{formatCurrency(info.interest)} ریال</TableCell>
+                  <TableCell>{formatCurrency(info.penalty)} ریال</TableCell>
+                  <TableCell>
+                    <div className="flex gap-2">
+                      <Button
+                        size="sm"
+                        variant="outline"
+                        onClick={() => setEditFacility(f)}
+                      >
+                        <Edit className="h-4 w-4" />
+                      </Button>
+                      <Button
+                        size="sm"
+                        variant="destructive"
+                        onClick={() => handleDelete(f)}
+                        disabled={facilityPayments(f.id).length > 0}
+                      >
+                        <Trash2 className="h-4 w-4" />
+                      </Button>
                     </div>
-                  )}
-                </CardContent>
-              </Card>
-            );
-          })}
-        </div>
-      )}
+                  </TableCell>
+                </TableRow>
+              );
+            })
+          </TableBody>
+        </Table>
+      </div>
+
+      <Dialog open={!!editFacility} onOpenChange={(o) => !o && setEditFacility(null)}>
+        <DialogContent className="w-full h-full max-w-screen-lg overflow-y-auto">
+          {editFacility && (
+            <SimpleFacilityForm
+              facility={editFacility}
+              onBack={() => setEditFacility(null)}
+              onSuccess={handleFacilitySaved}
+            />
+          )}
+        </DialogContent>
+      </Dialog>
+
+      <Dialog open={!!editPayment} onOpenChange={(o) => !o && setEditPayment(null)}>
+        <DialogContent className="w-full h-full max-w-screen-lg overflow-y-auto">
+          {editPayment && (
+            <SimplePaymentForm
+              payment={editPayment}
+              onBack={() => setEditPayment(null)}
+              onSuccess={handlePaymentSaved}
+            />
+          )}
+        </DialogContent>
+      </Dialog>
     </div>
   );
 };
 
 export default SimpleFacilityList;
diff --git a/src/components/lists/SimplePaymentList.tsx b/src/components/lists/SimplePaymentList.tsx
new file mode 100644
index 0000000000000000000000000000000000000000..db33aee800e2c5e072f29f2a4d40426696a7ae92
--- /dev/null
+++ b/src/components/lists/SimplePaymentList.tsx
@@ -0,0 +1,152 @@
+import { useEffect, useMemo, useState } from "react";
+import { Button } from "@/components/ui/button";
+import { Input } from "@/components/ui/input";
+import {
+  Table,
+  TableBody,
+  TableCell,
+  TableHead,
+  TableHeader,
+  TableRow,
+} from "@/components/ui/table";
+import { Dialog, DialogContent } from "@/components/ui/dialog";
+import { Edit, Trash2 } from "lucide-react";
+import { getFacilities, getPayments, deletePayment } from "@/utils/storage";
+import { formatCurrency, formatDate } from "@/utils/formatters";
+import { Facility, Payment } from "@/types/types";
+import SimplePaymentForm from "@/components/payment/SimplePaymentForm";
+import { useToast } from "@/hooks/use-toast";
+
+interface SimplePaymentListProps {
+  onBack: () => void;
+}
+
+const SimplePaymentList = ({ onBack }: SimplePaymentListProps) => {
+  const { toast } = useToast();
+  const [payments, setPayments] = useState<Payment[]>([]);
+  const [facilities, setFacilities] = useState<Facility[]>([]);
+  const [search, setSearch] = useState("");
+  const [editPayment, setEditPayment] = useState<Payment | null>(null);
+
+  useEffect(() => {
+    refreshData();
+  }, []);
+
+  const refreshData = () => {
+    setPayments(getPayments());
+    setFacilities(getFacilities());
+  };
+
+  const facilityMap = useMemo(() => {
+    const map = new Map<number, Facility>();
+    facilities.forEach((f) => map.set(f.id, f));
+    return map;
+  }, [facilities]);
+
+  const filtered = useMemo(
+    () =>
+      payments.filter((p) => {
+        const f = facilityMap.get(p.facilityId);
+        if (!f) return false;
+        const term = search.trim();
+        return (
+          f.bankName.includes(term) || f.contractNumber.includes(term)
+        );
+      }),
+    [payments, facilityMap, search],
+  );
+
+  const handleDelete = (id: number) => {
+    deletePayment(id);
+    toast({ title: "حذف شد" });
+    refreshData();
+  };
+
+  const handleSaved = () => {
+    setEditPayment(null);
+    refreshData();
+  };
+
+  return (
+    <div className="space-y-6">
+      <div className="flex items-center justify-between">
+        <h2 className="text-2xl font-bold">لیست پرداخت‌ها</h2>
+        <Button variant="outline" onClick={onBack}>
+          بازگشت
+        </Button>
+      </div>
+
+      <Input
+        placeholder="جستجوی بانک یا شماره تسهیلات"
+        value={search}
+        onChange={(e) => setSearch(e.target.value)}
+      />
+
+      <div className="overflow-x-auto">
+        <Table>
+          <TableHeader>
+            <TableRow>
+              <TableHead className="text-right">تاریخ پرداخت</TableHead>
+              <TableHead className="text-right">نام بانک</TableHead>
+              <TableHead className="text-right">شماره تسهیلات</TableHead>
+              <TableHead className="text-right">شماره قسط</TableHead>
+              <TableHead className="text-right">مبلغ اصل</TableHead>
+              <TableHead className="text-right">مبلغ سود</TableHead>
+              <TableHead className="text-right">مبلغ جریمه</TableHead>
+              <TableHead></TableHead>
+            </TableRow>
+          </TableHeader>
+          <TableBody>
+            {filtered.map((p) => {
+              const f = facilityMap.get(p.facilityId);
+              if (!f) return null;
+              return (
+                <TableRow key={p.id} className="hover:bg-gray-50">
+                  <TableCell>{formatDate(p.paymentDate)}</TableCell>
+                  <TableCell>{f.bankName}</TableCell>
+                  <TableCell>{f.contractNumber}</TableCell>
+                  <TableCell>{p.installmentNumber}</TableCell>
+                  <TableCell>{formatCurrency(p.principalAmount)} ریال</TableCell>
+                  <TableCell>{formatCurrency(p.interestAmount)} ریال</TableCell>
+                  <TableCell>{formatCurrency(p.penaltyAmount)} ریال</TableCell>
+                  <TableCell>
+                    <div className="flex gap-2">
+                      <Button
+                        size="sm"
+                        variant="outline"
+                        onClick={() => setEditPayment(p)}
+                      >
+                        <Edit className="h-4 w-4" />
+                      </Button>
+                      <Button
+                        size="sm"
+                        variant="destructive"
+                        onClick={() => handleDelete(p.id)}
+                      >
+                        <Trash2 className="h-4 w-4" />
+                      </Button>
+                    </div>
+                  </TableCell>
+                </TableRow>
+              );
+            })
+          </TableBody>
+        </Table>
+      </div>
+
+      <Dialog open={!!editPayment} onOpenChange={(o) => !o && setEditPayment(null)}>
+        <DialogContent className="w-full h-full max-w-screen-lg overflow-y-auto">
+          {editPayment && (
+            <SimplePaymentForm
+              payment={editPayment}
+              onBack={() => setEditPayment(null)}
+              onSuccess={handleSaved}
+            />
+          )}
+        </DialogContent>
+      </Dialog>
+    </div>
+  );
+};
+
+export default SimplePaymentList;
diff --git a/src/components/payment/SimplePaymentForm.tsx b/src/components/payment/SimplePaymentForm.tsx
index 280caa2e06a436ccef7cc523dc7200c67fce4320..49eee3ae3208d920286b1f60b9c70702f9ee75b5 100644
--- a/src/components/payment/SimplePaymentForm.tsx
+++ b/src/components/payment/SimplePaymentForm.tsx
@@ -1,234 +1,354 @@
-
 import { useState, useEffect, useMemo } from "react";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Button } from "@/components/ui/button";
 import { useToast } from "@/hooks/use-toast";
-import SimpleSelect from "@/components/common/SimpleSelect";
+import SearchableSelect from "@/components/common/SearchableSelect";
 import SimpleInput from "@/components/common/SimpleInput";
 import CurrencyInput from "@/components/common/CurrencyInput";
-import { getFacilities, addPayment } from "@/utils/storage";
+import { Input } from "@/components/ui/input";
+import { getFacilities, addPayment, updatePayment } from "@/utils/storage";
 import { formatCurrency } from "@/utils/formatters";
-import { Facility, PaymentFormData } from "@/types/types";
+import { Facility, PaymentFormData, Payment } from "@/types/types";
+import { toEnglishDigits } from "@/utils/formatters";
 
 interface SimplePaymentFormProps {
   onBack: () => void;
   onSuccess?: () => void;
+  payment?: Payment;
 }
 
-const SimplePaymentForm = ({ onBack, onSuccess }: SimplePaymentFormProps) => {
+const SimplePaymentForm = ({
+  onBack,
+  onSuccess,
+  payment,
+}: SimplePaymentFormProps) => {
   const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
-  const [formData, setFormData] = useState<PaymentFormData>({
-    facilityId: "",
-    installmentNumber: "",
-    paymentDate: "",
-    principalAmount: "",
-    interestAmount: "",
-    penaltyAmount: "",
-    notes: ""
-  });
+  const [formData, setFormData] = useState<PaymentFormData>(() => ({
+    facilityId: payment ? `${payment.facilityId}|` : "",
+    installmentNumber: payment?.installmentNumber?.toString() || "",
+    paymentDate: payment?.paymentDate || "",
+    principalAmount: payment?.principalAmount?.toString() || "",
+    interestAmount: payment?.interestAmount?.toString() || "",
+    penaltyAmount: payment?.penaltyAmount?.toString() || "",
+    notes: payment?.notes || "",
+  }));
 
   useEffect(() => {
     setFacilities(getFacilities());
   }, []);
 
-  const facilityOptions = useMemo(() => 
-    facilities.map(f => `${f.id}|${f.type} - ${f.bankName} - ${f.contractNumber}`),
-    [facilities]
+  // پس از بارگذاری لیست تسهیلات، عنوان انتخاب شده را تکمیل کن
+  useEffect(() => {
+    if (payment && facilities.length > 0 && formData.facilityId.endsWith('|')) {
+      const facility = facilities.find((f) => f.id === payment.facilityId);
+      if (facility) {
+        setFormData((prev) => ({
+          ...prev,
+          facilityId: `${facility.id}|${facility.bankName} - ${facility.contractNumber}`,
+        }));
+      }
+    }
+  }, [facilities, payment]);
+
+  const facilityOptions = useMemo(
+    () => facilities.map((f) => `${f.id}|${f.bankName} - ${f.contractNumber}`),
+    [facilities],
   );
 
   const selectedFacility = useMemo(() => {
     if (!formData.facilityId) return null;
-    const facilityId = parseInt(formData.facilityId.split('|')[0]);
-    return facilities.find(f => f.id === facilityId) || null;
+    const facilityId = parseInt(formData.facilityId.split("|")[0]);
+    return facilities.find((f) => f.id === facilityId) || null;
   }, [formData.facilityId, facilities]);
 
   const installmentOptions = useMemo(() => {
     if (!selectedFacility) return [];
-    return Array.from({ length: selectedFacility.installmentCount }, (_, i) => `${i + 1}`);
+    return Array.from(
+      { length: selectedFacility.installmentCount },
+      (_, i) => `${i + 1}`,
+    );
   }, [selectedFacility]);
 
   const updateField = (field: keyof PaymentFormData, value: string) => {
-    setFormData(prev => ({ ...prev, [field]: value }));
+    setFormData((prev) => ({ ...prev, [field]: value }));
   };
 
   const totalAmount = useMemo(() => {
     const principal = parseInt(formData.principalAmount) || 0;
     const interest = parseInt(formData.interestAmount) || 0;
     const penalty = parseInt(formData.penaltyAmount) || 0;
     return principal + interest + penalty;
-  }, [formData.principalAmount, formData.interestAmount, formData.penaltyAmount]);
+  }, [
+    formData.principalAmount,
+    formData.interestAmount,
+    formData.penaltyAmount,
+  ]);
+
+  const parseDate = (value: string): Date | null => {
+    const [y, m, d] = toEnglishDigits(value).split("/").map(Number);
+    if (!y || !m || !d) return null;
+    return new Date(y, m - 1, d);
+  };
+
+  const paymentDiff = useMemo(() => {
+    if (!formData.paymentDate || !selectedFacility || !formData.installmentNumber)
+      return null;
+    const installmentIdx = parseInt(formData.installmentNumber) - 1;
+    const installment = selectedFacility.installments?.[installmentIdx];
+    if (!installment) return null;
+    const payDate = parseDate(formData.paymentDate);
+    const dueDate = parseDate(installment.dueDate);
+    if (!payDate || !dueDate) return null;
+    const diff = Math.floor(
+      (payDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24),
+    );
+    return diff;
+  }, [formData.paymentDate, formData.installmentNumber, selectedFacility]);
 
   const handleSubmit = (e: React.FormEvent) => {
     e.preventDefault();
-    
-    if (!formData.facilityId || !formData.installmentNumber || !formData.paymentDate) {
+
+    if (
+      !formData.facilityId ||
+      !formData.installmentNumber ||
+      !formData.paymentDate
+    ) {
       toast({
         title: "خطا",
         description: "لطفاً تمام فیلدهای ضروری را پر کنید",
         variant: "destructive",
       });
       return;
     }
 
+    if (!/^\d{4}\/\d{2}\/\d{2}$/.test(formData.paymentDate)) {
+      toast({
+        title: "خطا",
+        description: "تاریخ پرداخت باید در قالب YYYY/MM/DD باشد",
+        variant: "destructive",
+      });
+      return;
+    }
+
     if (totalAmount <= 0) {
       toast({
         title: "خطا",
         description: "مبلغ پرداختی باید بیشتر از صفر باشد",
         variant: "destructive",
       });
       return;
     }
 
     try {
-      const facilityId = parseInt(formData.facilityId.split('|')[0]);
-      
-      addPayment({
-        facilityId,
-        installmentNumber: parseInt(formData.installmentNumber),
-        paymentDate: formData.paymentDate,
-        principalAmount: parseInt(formData.principalAmount) || 0,
-        interestAmount: parseInt(formData.interestAmount) || 0,
-        penaltyAmount: parseInt(formData.penaltyAmount) || 0,
-        totalAmount,
-        notes: formData.notes
-      });
+      const facilityId = parseInt(formData.facilityId.split("|")[0]);
 
-      toast({
-        title: "موفقیت",
-        description: "پرداخت با موفقیت ثبت شد",
-      });
+      if (payment) {
+        updatePayment(payment.id, {
+          facilityId,
+          installmentNumber: parseInt(formData.installmentNumber),
+          paymentDate: formData.paymentDate,
+          principalAmount: parseInt(formData.principalAmount) || 0,
+          interestAmount: parseInt(formData.interestAmount) || 0,
+          penaltyAmount: parseInt(formData.penaltyAmount) || 0,
+          totalAmount,
+          notes: formData.notes,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "پرداخت با موفقیت ویرایش شد",
+        });
+      } else {
+        addPayment({
+          facilityId,
+          installmentNumber: parseInt(formData.installmentNumber),
+          paymentDate: formData.paymentDate,
+          principalAmount: parseInt(formData.principalAmount) || 0,
+          interestAmount: parseInt(formData.interestAmount) || 0,
+          penaltyAmount: parseInt(formData.penaltyAmount) || 0,
+          totalAmount,
+          notes: formData.notes,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "پرداخت با موفقیت ثبت شد",
+        });
+      }
 
       if (onSuccess) onSuccess();
       else onBack();
     } catch (error) {
       toast({
         title: "خطا",
         description: "خطا در ثبت پرداخت",
         variant: "destructive",
       });
     }
   };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
-        <h2 className="text-2xl font-bold">ثبت پرداخت</h2>
+        <h2 className="text-2xl font-bold">
+          {payment ? "ویرایش پرداخت" : "ثبت پرداخت"}
+        </h2>
         <Button variant="outline" onClick={onBack}>
           بازگشت
         </Button>
       </div>
 
       <Card>
         <CardHeader>
           <CardTitle>اطلاعات پرداخت</CardTitle>
         </CardHeader>
         <CardContent>
           <form onSubmit={handleSubmit} className="space-y-6">
-            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
-              <SimpleSelect
+            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
+              <SearchableSelect
                 label="انتخاب تسهیلات"
                 value={formData.facilityId}
                 onValueChange={(value) => {
-                  updateField('facilityId', value);
-                  updateField('installmentNumber', '');
+                  updateField("facilityId", value);
+                  updateField("installmentNumber", "");
                 }}
                 options={facilityOptions}
-                placeholder={facilities.length === 0 ? "هیچ تسهیلاتی وجود ندارد" : "انتخاب کنید"}
+                placeholder={
+                  facilities.length === 0
+                    ? "هیچ تسهیلاتی وجود ندارد"
+                    : "انتخاب کنید"
+                }
                 required
               />
 
-              <SimpleSelect
+              <SearchableSelect
                 label="شماره قسط"
                 value={formData.installmentNumber}
-                onValueChange={(value) => updateField('installmentNumber', value)}
+                onValueChange={(value) =>
+                  updateField("installmentNumber", value)
+                }
                 options={installmentOptions}
-                placeholder={!selectedFacility ? "ابتدا تسهیلات را انتخاب کنید" : "انتخاب کنید"}
+                placeholder={
+                  !selectedFacility
+                    ? "ابتدا تسهیلات را انتخاب کنید"
+                    : "انتخاب کنید"
+                }
                 required
               />
 
-              <SimpleInput
-                label="تاریخ پرداخت"
-                value={formData.paymentDate}
-                onChange={(value) => updateField('paymentDate', value)}
-                type="date"
-                required
-              />
+              <div className="space-y-2">
+                <label className="text-sm font-medium">تاریخ پرداخت *</label>
+                <Input
+                  value={formData.paymentDate}
+                  onChange={(e) => updateField("paymentDate", e.target.value)}
+                  placeholder="YYYY/MM/DD"
+                  pattern="\d{4}/\d{2}/\d{2}"
+                  required
+                />
+                {paymentDiff !== null && (
+                  <p
+                    className={`text-sm mt-1 ${
+                      paymentDiff > 0
+                        ? 'text-red-600'
+                        : paymentDiff < 0
+                        ? 'text-green-600'
+                        : 'text-gray-500'
+                    }`}
+                  >
+                    {paymentDiff > 0
+                      ? `${paymentDiff} روز دیرتر`
+                      : paymentDiff < 0
+                      ? `${Math.abs(paymentDiff)} روز زودتر`
+                      : 'در تاریخ مقرر'}
+                  </p>
+                )}
+              </div>
 
               <div></div>
 
               <CurrencyInput
                 label="مبلغ اصل (ریال)"
                 value={formData.principalAmount}
-                onChange={(value) => updateField('principalAmount', value)}
+                onChange={(value) => updateField("principalAmount", value)}
               />
 
               <CurrencyInput
                 label="مبلغ سود (ریال)"
                 value={formData.interestAmount}
-                onChange={(value) => updateField('interestAmount', value)}
+                onChange={(value) => updateField("interestAmount", value)}
               />
 
               <CurrencyInput
                 label="مبلغ جریمه (ریال)"
                 value={formData.penaltyAmount}
-                onChange={(value) => updateField('penaltyAmount', value)}
+                onChange={(value) => updateField("penaltyAmount", value)}
               />
 
               <div className="space-y-2">
                 <label className="text-sm font-medium">مجموع مبلغ</label>
                 <div className="p-3 bg-gray-50 rounded-md text-lg font-semibold">
                   {formatCurrency(totalAmount)} ریال
                 </div>
               </div>
 
               <div className="md:col-span-2">
                 <SimpleInput
                   label="یادداشت"
                   value={formData.notes}
-                  onChange={(value) => updateField('notes', value)}
+                  onChange={(value) => updateField("notes", value)}
                   placeholder="توضیحات اضافی"
                 />
               </div>
             </div>
 
             {selectedFacility && (
               <div className="p-4 bg-blue-50 rounded-lg">
-                <h4 className="font-semibold mb-2">اطلاعات تسهیلات انتخاب شده:</h4>
+                <h4 className="font-semibold mb-2">
+                  اطلاعات تسهیلات انتخاب شده:
+                </h4>
                 <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                   <div>
                     <span className="text-gray-600">نوع: </span>
-                    <span className="font-semibold">{selectedFacility.type}</span>
+                    <span className="font-semibold">
+                      {selectedFacility.type}
+                    </span>
                   </div>
                   <div>
                     <span className="text-gray-600">بانک: </span>
-                    <span className="font-semibold">{selectedFacility.bankName}</span>
+                    <span className="font-semibold">
+                      {selectedFacility.bankName}
+                    </span>
                   </div>
                   <div>
                     <span className="text-gray-600">مبلغ کل: </span>
-                    <span className="font-semibold">{formatCurrency(selectedFacility.amount)} ریال</span>
+                    <span className="font-semibold">
+                      {formatCurrency(selectedFacility.amount)} ریال
+                    </span>
                   </div>
                   <div>
                     <span className="text-gray-600">نرخ سود: </span>
-                    <span className="font-semibold">{selectedFacility.interestRate}%</span>
+                    <span className="font-semibold">
+                      {selectedFacility.interestRate}%
+                    </span>
                   </div>
                 </div>
               </div>
             )}
 
             <div className="flex justify-end space-x-2 space-x-reverse">
               <Button type="button" variant="outline" onClick={onBack}>
                 لغو
               </Button>
               <Button type="submit">
-                ثبت پرداخت
+                {payment ? "ذخیره تغییرات" : "ثبت پرداخت"}
               </Button>
             </div>
           </form>
         </CardContent>
       </Card>
     </div>
   );
 };
 
 export default SimplePaymentForm;
diff --git a/src/components/ui/money-input.tsx b/src/components/ui/money-input.tsx
index 349113eeddd20973e16cef654b033fc8dcb8d6a2..3b2f6ce9cde1f461d8e6f0f38ae0f66dc15c9b5e 100644
--- a/src/components/ui/money-input.tsx
+++ b/src/components/ui/money-input.tsx
@@ -1,53 +1,55 @@
 
 import React from 'react';
 import { Input } from './input';
 import { formatMoney, parseMoney } from '@/utils/formatMoney';
+import { toEnglishDigits } from '@/utils/formatters';
 
 interface MoneyInputProps {
   value: string;
   onChange: (value: string) => void;
   placeholder?: string;
   className?: string;
   id?: string;
 }
 
 const MoneyInput = ({ value, onChange, placeholder, className, id }: MoneyInputProps) => {
   // نمایش فرمت شده مقدار
   const displayValue = value ? formatMoney(value) : '';
 
   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
     const inputValue = e.target.value;
     
     // اگر کاربر همه چیز را پاک کرد
     if (inputValue === '') {
       onChange('');
       return;
     }
     
-    // استخراج فقط اعداد از ورودی
-    const numbersOnly = inputValue.replace(/[^\d]/g, '');
+    // نرمال‌سازی ارقام فارسی و استخراج اعداد
+    const normalized = toEnglishDigits(inputValue);
+    const numbersOnly = normalized.replace(/[^0-9]/g, '');
     
     // ارسال مقدار پاک شده (فقط اعداد)
     onChange(numbersOnly);
   };
 
   const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
     // قرار دادن کرسور در انتها
     const target = e.target;
     setTimeout(() => {
       const length = target.value.length;
       target.setSelectionRange(length, length);
     }, 0);
   };
 
   return (
     <Input
       id={id}
       type="text"
       value={displayValue}
       onChange={handleChange}
       onFocus={handleFocus}
       placeholder={placeholder}
       className={className}
       inputMode="numeric"
     />
diff --git a/src/pages/Index.tsx b/src/pages/Index.tsx
index 9cb1abbe45a8956513ef57dc40a969a739290f7c..5055e13fc77b3872f58d7e7b056034d70c1a25bf 100644
--- a/src/pages/Index.tsx
+++ b/src/pages/Index.tsx
@@ -1,55 +1,65 @@
 
 import { useState } from 'react';
 import Dashboard from '@/components/Dashboard';
 import SimpleFacilityForm from '@/components/facility/SimpleFacilityForm';
 import SimplePaymentForm from '@/components/payment/SimplePaymentForm';
 import SimpleFacilityList from '@/components/lists/SimpleFacilityList';
-import TestDataManager from '@/components/TestDataManager';
+import SimplePaymentList from '@/components/lists/SimplePaymentList';
 import PaymentAllocationReport from '@/components/PaymentAllocationReport';
 
-type ViewType = 'dashboard' | 'facility-form' | 'payment-form' | 'facility-list' | 'test-data' | 'payment-allocation';
+type ViewType =
+  | 'dashboard'
+  | 'facility-form'
+  | 'payment-form'
+  | 'facility-list'
+  | 'payment-list'
+  | 'payment-allocation';
 
 const Index = () => {
   const [currentView, setCurrentView] = useState<ViewType>('dashboard');
 
   const renderCurrentView = () => {
     switch (currentView) {
       case 'facility-form':
         return <SimpleFacilityForm onBack={() => setCurrentView('dashboard')} />;
       case 'payment-form':
         return <SimplePaymentForm onBack={() => setCurrentView('dashboard')} />;
       case 'facility-list':
         return (
-          <SimpleFacilityList 
+          <SimpleFacilityList
             onBack={() => setCurrentView('dashboard')}
             onAddFacility={() => setCurrentView('facility-form')}
             onAddPayment={() => setCurrentView('payment-form')}
           />
         );
-      case 'test-data':
-        return <TestDataManager onBack={() => setCurrentView('dashboard')} />;
+      case 'payment-list':
+        return (
+          <SimplePaymentList
+            onBack={() => setCurrentView('dashboard')}
+          />
+        );
       case 'payment-allocation':
         return <PaymentAllocationReport onBack={() => setCurrentView('dashboard')} />;
       default:
         return (
-          <Dashboard 
+          <Dashboard
             onNavigateToFacilityForm={() => setCurrentView('facility-form')}
             onNavigateToPaymentForm={() => setCurrentView('payment-form')}
             onNavigateToFacilityList={() => setCurrentView('facility-list')}
-            onNavigateToTestData={() => setCurrentView('test-data')}
+            onNavigateToPaymentList={() => setCurrentView('payment-list')}
             onNavigateToPaymentAllocation={() => setCurrentView('payment-allocation')}
           />
         );
     }
   };
 
   return (
     <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
       <div className="container mx-auto px-4 py-8">
         {renderCurrentView()}
       </div>
     </div>
   );
 };
 
 export default Index;
diff --git a/src/types/types.ts b/src/types/types.ts
index 8d5a117eda305ae00e9338c19fb7821fd8e2323b..bc3b0aabc896bfbb671715780591aa4b94370871 100644
--- a/src/types/types.ts
+++ b/src/types/types.ts
@@ -1,52 +1,54 @@
 
 // تعریف انواع داده‌ها
+import { Installment } from './facility';
+
 export interface Facility {
   id: number;
   type: string;
   amount: number;
   bankName: string;
   contractNumber: string;
   installmentCount: number;
   interestRate: number;
   penaltyRate: number;
   installmentAmount: number;
   receivedDate: string;
   firstInstallmentDate: string;
+  installments?: Installment[];
   createdAt: string;
 }
 
 export interface Payment {
   id: number;
   facilityId: number;
   installmentNumber: number;
   paymentDate: string;
   principalAmount: number;
   interestAmount: number;
   penaltyAmount: number;
   totalAmount: number;
   notes?: string;
   createdAt: string;
 }
 
 export interface FacilityFormData {
   type: string;
   amount: string;
   bankName: string;
   contractNumber: string;
   installmentCount: string;
   interestRate: string;
   penaltyRate: string;
-  installmentAmount: string;
   receivedDate: string;
   firstInstallmentDate: string;
 }
 
 export interface PaymentFormData {
   facilityId: string;
   installmentNumber: string;
   paymentDate: string;
   principalAmount: string;
   interestAmount: string;
   penaltyAmount: string;
   notes: string;
 }
diff --git a/src/utils/constants.ts b/src/utils/constants.ts
index e7c24a2bff9d59fc14d60062099d2d0f1cea6c1a..22c1c12bb1675e9e356115b3fc9594626a55acaa 100644
--- a/src/utils/constants.ts
+++ b/src/utils/constants.ts
@@ -1,28 +1,27 @@
-
-export const FACILITY_TYPES = [
-  'مسکن',
-  'خودرو',
-  'کارگشایی',
-  'ودیعه مسکن',
-  'جاری',
-  'تولیدی',
-  'خرد',
-  'تحصیلی',
-  'سایر'
+export const DEFAULT_FACILITY_TYPES = [
+  "مسکن",
+  "خودرو",
+  "کارگشایی",
+  "ودیعه مسکن",
+  "جاری",
+  "تولیدی",
+  "خرد",
+  "تحصیلی",
+  "سایر",
 ] as const;
 
-export const BANK_NAMES = [
-  'بانک ملی ایران',
-  'بانک صادرات ایران',
-  'بانک تجارت',
-  'بانک کشاورزی',
-  'بانک صنعت و معدن',
-  'بانک رفاه کارگران',
-  'بانک توسعه تعاون',
-  'بانک پست بانک',
-  'بانک دی',
-  'بانک پارسیان',
-  'بانک پاسارگاد',
-  'بانک کارآفرین',
-  'سایر'
+export const DEFAULT_BANK_NAMES = [
+  "بانک ملی ایران",
+  "بانک صادرات ایران",
+  "بانک تجارت",
+  "بانک کشاورزی",
+  "بانک صنعت و معدن",
+  "بانک رفاه کارگران",
+  "بانک توسعه تعاون",
+  "بانک پست بانک",
+  "بانک دی",
+  "بانک پارسیان",
+  "بانک پاسارگاد",
+  "بانک کارآفرین",
+  "سایر",
 ] as const;
diff --git a/src/utils/formatMoney.ts b/src/utils/formatMoney.ts
index 65676a9c8a2b769525631e72b5b2eecb7bc389e1..745ec4002d32dcd0b73bf0b0d6df9762537fa24d 100644
--- a/src/utils/formatMoney.ts
+++ b/src/utils/formatMoney.ts
@@ -1,46 +1,46 @@
 
+import { toEnglishDigits } from './formatters';
+
 export const formatMoney = (value: string | number): string => {
-  // بررسی مقادیر خالی یا نامعتبر
-  if (!value || value === '' || value === null || value === undefined) {
+  if (value === null || value === undefined || value === '') {
     return '';
   }
 
-  // تبدیل به رشته و پاک‌سازی
   let cleanValue = '';
   if (typeof value === 'number') {
     cleanValue = value.toString();
   } else {
-    cleanValue = value.toString().replace(/[^\d]/g, '');
+    cleanValue = toEnglishDigits(value).replace(/[^0-9]/g, '');
   }
   
   // اگر هیچ عددی نماند
   if (!cleanValue || cleanValue === '0') {
     return '';
   }
 
   // تبدیل به عدد و فرمت کردن
   const numValue = parseInt(cleanValue, 10);
   
   if (isNaN(numValue) || numValue === 0) {
     return '';
   }
 
   // استفاده از فرمت کننده فارسی
   return new Intl.NumberFormat('fa-IR').format(numValue);
 };
 
 export const parseMoney = (formattedValue: string): string => {
   if (!formattedValue) return '';
-  
-  // حذف تمام کاراکترهای غیرعددی
-  const cleanValue = formattedValue.replace(/[^\d]/g, '');
+
+  const english = toEnglishDigits(formattedValue);
+  const cleanValue = english.replace(/[^0-9]/g, '');
   return cleanValue || '';
 };
 
 export const handleMoneyInput = (
   value: string,
   onChange: (value: string) => void
 ) => {
   const cleanValue = parseMoney(value);
   onChange(cleanValue);
 };
diff --git a/src/utils/formatters.ts b/src/utils/formatters.ts
index 2f5e7eb4069c8e4eef1a6d6a277cb8f4703ccbaf..9d137605d93310f014d1ab3b16cadbb62c3b1582 100644
--- a/src/utils/formatters.ts
+++ b/src/utils/formatters.ts
@@ -1,20 +1,29 @@
 
+export const toEnglishDigits = (value: string): string => {
+  const persianDigits = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
+  return persianDigits.reduce(
+    (acc, d, i) => acc.replace(new RegExp(d, 'g'), i.toString()),
+    value
+  );
+};
+
 export const formatCurrency = (amount: number | string): string => {
-  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
+  const str = typeof amount === 'string' ? toEnglishDigits(amount) : String(amount);
+  const num = parseFloat(str);
   if (isNaN(num)) return '0';
   return new Intl.NumberFormat('fa-IR').format(num);
 };
 
 export const formatDate = (date: string | Date): string => {
   try {
     const dateObj = typeof date === 'string' ? new Date(date) : date;
     return new Intl.DateTimeFormat('fa-IR').format(dateObj);
   } catch {
     return date.toString();
   }
 };
 
 export const parseCurrency = (value: string): number => {
-  const cleaned = value.replace(/[^\d]/g, '');
+  const cleaned = toEnglishDigits(value).replace(/[^0-9]/g, '');
   return parseInt(cleaned) || 0;
 };
diff --git a/src/utils/storage.ts b/src/utils/storage.ts
index 3d4284a44bc1fafadb9bc8462bca8beb274c0563..dc4cb963ef456c1c2a6d919b2dc53d8cae1d2c4e 100644
--- a/src/utils/storage.ts
+++ b/src/utils/storage.ts
@@ -1,57 +1,136 @@
-
-import { Facility, Payment } from '@/types/types';
+import { Facility, Payment } from "@/types/types";
+import { DEFAULT_BANK_NAMES, DEFAULT_FACILITY_TYPES } from "./constants";
 
 export const storageKeys = {
-  facilities: 'facilities',
-  payments: 'payments'
+  facilities: "facilities",
+  payments: "payments",
+  bankNames: "bankNames",
+  facilityTypes: "facilityTypes",
 } as const;
 
 export const getFacilities = (): Facility[] => {
   try {
     const data = localStorage.getItem(storageKeys.facilities);
     return data ? JSON.parse(data) : [];
   } catch {
     return [];
   }
 };
 
 export const saveFacilities = (facilities: Facility[]): void => {
   localStorage.setItem(storageKeys.facilities, JSON.stringify(facilities));
 };
 
 export const getPayments = (): Payment[] => {
   try {
     const data = localStorage.getItem(storageKeys.payments);
     return data ? JSON.parse(data) : [];
   } catch {
     return [];
   }
 };
 
 export const savePayments = (payments: Payment[]): void => {
   localStorage.setItem(storageKeys.payments, JSON.stringify(payments));
 };
 
-export const addFacility = (facility: Omit<Facility, 'id' | 'createdAt'>): Facility => {
+export const getBankNames = (): string[] => {
+  try {
+    const data = localStorage.getItem(storageKeys.bankNames);
+    return data ? JSON.parse(data) : [...DEFAULT_BANK_NAMES];
+  } catch {
+    return [...DEFAULT_BANK_NAMES];
+  }
+};
+
+export const saveBankNames = (names: string[]): void => {
+  localStorage.setItem(storageKeys.bankNames, JSON.stringify(names));
+};
+
+export const getFacilityTypes = (): string[] => {
+  try {
+    const data = localStorage.getItem(storageKeys.facilityTypes);
+    return data ? JSON.parse(data) : [...DEFAULT_FACILITY_TYPES];
+  } catch {
+    return [...DEFAULT_FACILITY_TYPES];
+  }
+};
+
+export const saveFacilityTypes = (types: string[]): void => {
+  localStorage.setItem(storageKeys.facilityTypes, JSON.stringify(types));
+};
+
+export const addFacility = (
+  facility: Omit<Facility, "id" | "createdAt">,
+): Facility => {
   const facilities = getFacilities();
   const newFacility: Facility = {
     ...facility,
     id: Date.now(),
-    createdAt: new Date().toISOString()
+    createdAt: new Date().toISOString(),
+    installments: facility.installments || [],
   };
   facilities.push(newFacility);
   saveFacilities(facilities);
   return newFacility;
 };
 
-export const addPayment = (payment: Omit<Payment, 'id' | 'createdAt'>): Payment => {
+export const addPayment = (
+  payment: Omit<Payment, "id" | "createdAt">,
+): Payment => {
   const payments = getPayments();
   const newPayment: Payment = {
     ...payment,
     id: Date.now(),
-    createdAt: new Date().toISOString()
+    createdAt: new Date().toISOString(),
   };
   payments.push(newPayment);
   savePayments(payments);
   return newPayment;
 };
+
+export const updateFacility = (
+  id: number,
+  updates: Partial<Omit<Facility, "id" | "createdAt">>,
+): Facility | null => {
+  const facilities = getFacilities();
+  const index = facilities.findIndex((f) => f.id === id);
+  if (index === -1) return null;
+  facilities[index] = {
+    ...facilities[index],
+    ...updates,
+    installments: updates.installments ?? facilities[index].installments,
+  };
+  saveFacilities(facilities);
+  return facilities[index];
+};
+
+export const updatePayment = (
+  id: number,
+  updates: Partial<Omit<Payment, "id" | "createdAt">>,
+): Payment | null => {
+  const payments = getPayments();
+  const index = payments.findIndex((p) => p.id === id);
+  if (index === -1) return null;
+  payments[index] = { ...payments[index], ...updates };
+  savePayments(payments);
+  return payments[index];
+};
+
+export const deleteFacility = (id: number): void => {
+  const facilities = getFacilities();
+  const updated = facilities.filter((f) => f.id !== id);
+  saveFacilities(updated);
+};
+
+export const deletePaymentsByFacility = (facilityId: number): void => {
+  const payments = getPayments();
+  const remaining = payments.filter((p) => p.facilityId !== facilityId);
+  savePayments(remaining);
+};
+
+export const deletePayment = (id: number): void => {
+  const payments = getPayments();
+  const updated = payments.filter((p) => p.id !== id);
+  savePayments(updated);
+};
 
EOF
)