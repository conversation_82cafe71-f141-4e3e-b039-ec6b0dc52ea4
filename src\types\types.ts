// تعریف انواع داده‌ها
import { Installment } from './facility';

export interface Facility {
  id: number;
  type: string;
  amount: number;
  bankName: string;
  contractNumber: string;
  installmentCount: number;
  interestRate: number;
  penaltyRate: number;
  installmentAmount: number;
  receivedDate: string;
  firstInstallmentDate: string;
  installments?: Installment[];
  createdAt: string;
  commissionAmount?: number; // مقدار کارمزد
  otherAmount?: number; // مقدار سایر
  description?: string; // توضیحات
}

export interface Payment {
  id: number;
  facilityId: number;
  installmentNumber: number;
  paymentDate: string;
  principalAmount: number;
  interestAmount: number;
  penaltyAmount: number;
  feeAmount: number;
  commissionAmount: number;
  otherAmount: number;
  totalAmount: number;
  notes?: string;
  createdAt: string;
  remainingInterestDeviation?: number;
  remainingPenaltyDeviation?: number;
}

export interface FacilityFormData {
  type: string;
  amount: string;
  bankName: string;
  contractNumber: string;
  installmentCount: string;
  interestRate: string;
  penaltyRate: string;
  receivedDate: string;
  firstInstallmentDate: string;
  commissionAmount: string; // مقدار کارمزد
  otherAmount: string; // مقدار سایر
  description: string; // توضیحات
}

export interface PaymentFormData {
  facilityId: string;
  installmentNumber: string;
  paymentDate: string;
  principalAmount: string;
  interestAmount: string;
  penaltyAmount: string;
  commissionAmount: string;
  otherAmount: string;
  notes: string;
}
