import React, { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Edit, Trash2, Plus, FileText, Printer, ArrowRight, Search, Building2, Eye, AlertTriangle, CheckCircle2, TrendingUp } from "lucide-react";
import {
  getFacilities,
  getPayments,
  deleteFacility,
  deletePaymentsByFacility,
  getSettledInstallmentsCount,
} from "@/utils/storage";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Facility, Payment } from "@/types/types";
import SimpleFacilityForm from "@/components/facility/SimpleFacilityForm";
import SimplePaymentForm from "@/components/payment/SimplePaymentForm";
import PaymentDetailsReport from "@/components/payment/PaymentDetailsReport";
import { useToast } from "@/hooks/use-toast";
import * as XLSX from 'xlsx';

interface SimpleFacilityListProps {
  onBack: () => void;
  onAddFacility: () => void;
  onAddPayment: () => void;
}

const SimpleFacilityList = ({
  onBack,
  onAddFacility,
  onAddPayment,
}: SimpleFacilityListProps) => {
  const { toast } = useToast();
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [search, setSearch] = useState("");
  const [editFacility, setEditFacility] = useState<Facility | null>(null);
  const [editPayment, setEditPayment] = useState<Payment | null>(null);
  const [paymentDetailsFacility, setPaymentDetailsFacility] = useState<Facility | null>(null);
  const [facilityDetails, setFacilityDetails] = useState<Facility | null>(null);

  useEffect(() => {
    refreshData();
  }, []);

  const refreshData = () => {
    setFacilities(getFacilities());
    setPayments(getPayments());
  };

  const facilityPayments = (id: number) =>
    payments.filter((p) => p.facilityId === id);

  const filteredFacilities = useMemo(
    () =>
      facilities.filter((f) => {
        const installmentCount =
          typeof f.installmentCount === "string"
            ? parseInt(f.installmentCount)
            : f.installmentCount;
        const amount =
          typeof f.amount === "string" ? parseInt(f.amount) : f.amount;
        const term = search.trim();

        return (
          f.bankName.includes(term) || f.contractNumber.includes(term)
        );
      }),
    [facilities, search]
  );

  const [deleteConfirmFacility, setDeleteConfirmFacility] = useState<Facility | null>(null);
  
  const handleDelete = (facility: Facility) => {
    // نمایش تأییدیه حذف
    setDeleteConfirmFacility(facility);
  };
  
  const confirmDelete = () => {
    if (!deleteConfirmFacility) return;
    
    if (facilityPayments(deleteConfirmFacility.id).length > 0) {
      toast({ 
        title: "خطا", 
        description: "امکان حذف تسهیلاتی که دارای پرداخت هستند وجود ندارد",
        variant: "destructive" 
      });
      setDeleteConfirmFacility(null);
      return;
    }
    
    deleteFacility(deleteConfirmFacility.id);
    deletePaymentsByFacility(deleteConfirmFacility.id);
    toast({ title: "حذف شد" });
    refreshData();
    setDeleteConfirmFacility(null);
  };

  const handleFacilitySaved = () => {
    setEditFacility(null);
    refreshData();
  };

  const handlePaymentSaved = () => {
    setEditPayment(null);
    refreshData();
  };

  const paidInfo = (id: number) => {
    const list = facilityPayments(id);
    const principal = list.reduce((s, p) => s + (p.principalAmount || 0), 0);
    const interest = list.reduce((s, p) => s + (p.interestAmount || 0), 0);
    const penalty = list.reduce((s, p) => s + (p.penaltyAmount || 0), 0);
    
    // یافتن تسهیلات مربوطه برای دسترسی به مقادیر کارمزد و سایر
    const facility = facilities.find(f => f.id === id);
    const commission = facility?.commissionAmount || 0;
    const other = facility?.otherAmount || 0;
    
    return { principal, interest, penalty, commission, other, count: list.length };
  };

  const exportExcel = () => {
    // Usar XLSX para exportar a formato xlsx en lugar de CSV
    try {
      // Preparar los datos para Excel
      const data = facilities.map((f) => {
        const installmentCount =
          typeof f.installmentCount === "string"
            ? parseInt(f.installmentCount)
            : f.installmentCount;
        const amount =
          typeof f.amount === "string" ? parseInt(f.amount) : f.amount;

        const info = paidInfo(f.id);
        const settledCount = getSettledInstallmentsCount(f.id);
        const unpaidCount = installmentCount - settledCount;
        const unpaidAmount = amount - info.principal;

        return {
          'نام بانک': f.bankName,
          'شماره تسهیلات': f.contractNumber,
          'مبلغ کل': amount,
          'تعداد اقساط': installmentCount,
          'اقساط تسویه شده': settledCount,
          'اقساط تسویه نشده': unpaidCount,
          'مبلغ تسویه شده': info.principal,
          'باقیمانده اصل': unpaidAmount,
          'سود پرداخت شده': info.interest,
          'جریمه پرداخت شده': info.penalty,
          'کارمزد پرداخت شده': info.commission,
          'سایر پرداخت شده': info.other,
          'نرخ سود': f.interestRate,
          'نرخ جریمه': f.penaltyRate,
          'تاریخ اخذ': f.receivedDate,
        };
      });

      // Crear un libro de trabajo y una hoja
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "تسهیلات");
      
      // Ajustar el ancho de las columnas
      const maxWidth = 20;
      const cols = Object.keys(data[0]).map(() => ({ wch: maxWidth }));
      worksheet['!cols'] = cols;
      
      // Guardar el archivo en formato xlsx
      XLSX.writeFile(workbook, "facilities.xlsx");
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({ 
        title: "خطا", 
        description: "خطا در صدور فایل اکسل",
        variant: "destructive" 
      });
    }
  };

  const exportPDF = () => {
    window.print();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6 space-y-8">
      {/* Enhanced Header */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-xl shadow-lg">
              <Building2 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                لیست تسهیلات
              </h1>
              <p className="text-slate-500 mt-1">مدیریت و مشاهده تمام تسهیلات ثبت شده</p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={onAddPayment}
              variant="outline"
              className="flex items-center gap-2 bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-200 shadow-sm"
            >
              <Plus className="h-4 w-4" /> پرداخت جدید
            </Button>
            <Button
              onClick={onAddFacility}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4" /> تسهیلات جدید
            </Button>
            <Button
              variant="outline"
              onClick={exportExcel}
              className="flex items-center gap-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 transition-all duration-200 shadow-sm"
            >
              <FileText className="h-4 w-4" />
              دریافت Excel
            </Button>
            <Button
              variant="outline"
              onClick={exportPDF}
              className="flex items-center gap-2 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-all duration-200 shadow-sm"
            >
              <Printer className="h-4 w-4" />
              دریافت PDF
            </Button>
            <Button
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2 bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
            >
              <ArrowRight className="h-4 w-4" />
              بازگشت
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Search */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Search className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-slate-700">جستجو در تسهیلات</h3>
        </div>
        <Input
          placeholder="جستجوی بانک یا شماره تسهیلات"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="h-12 border-2 border-slate-200 hover:border-purple-300 focus:border-purple-500 transition-all duration-200 bg-white shadow-sm"
        />
      </div>

      {/* Enhanced Table */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-slate-200/60 p-6">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-6 w-6 text-blue-600" />
            <h3 className="text-xl font-semibold text-slate-800">جدول تسهیلات</h3>
          </div>
        </div>
        <div className="overflow-x-auto">
          <Table className="border-collapse">
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 border-b-2 border-slate-200">
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-blue-50">نام بانک</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-indigo-50">شماره تسهیلات</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-purple-50">مبلغ کل</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-pink-50">تعداد اقساط</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-rose-50">اقساط تسویه شده</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-orange-50">اقساط تسویه نشده</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-amber-50">مبلغ تسویه شده</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-yellow-50">باقیمانده اصل</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-lime-50">سود پرداخت شده</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-green-50">جریمه پرداخت شده</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-emerald-50">کارمزد پرداخت شده</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-teal-50">سایر پرداخت شده</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-cyan-50">نرخ سود</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-sky-50">نرخ جریمه</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-blue-50">تاریخ اخذ</TableHead>
                <TableHead className="border-r border-slate-200 p-4 font-semibold text-slate-700 bg-indigo-50">عملیات</TableHead>
              </TableRow>
            </TableHeader>
          <TableBody>
            {filteredFacilities.map((f) => {
              const installmentCount =
                typeof f.installmentCount === "string"
                  ? parseInt(f.installmentCount)
                  : f.installmentCount;
              const amount =
                typeof f.amount === "string" ? parseInt(f.amount) : f.amount;

              const info = paidInfo(f.id);
              // محاسبه تعداد اقساط تسویه شده بر اساس مبلغ اصل پرداخت شده
              const settledCount = getSettledInstallmentsCount(f.id);
              const unpaidCount = installmentCount - settledCount;
              const unpaidAmount = amount - info.principal;

              return (
                <TableRow
                  key={f.id}
                  className={`border-b border-slate-200 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-200 ${
                    (f.id % 2 === 0) ? 'bg-white' : 'bg-slate-50/30'
                  }`}
                >
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-800">{f.bankName}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{f.contractNumber}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-blue-700">{formatCurrency(amount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{installmentCount}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-green-600">{settledCount}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-orange-600">{unpaidCount}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-emerald-700">{formatCurrency(info.principal)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-red-600">{formatCurrency(unpaidAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-purple-600">{formatCurrency(info.interest)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-pink-600">{formatCurrency(info.penalty)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-indigo-600">{formatCurrency(info.commission)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-teal-600">{formatCurrency(info.other)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{f.interestRate}%</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{f.penaltyRate}%</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-600">{formatDate(f.receivedDate)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setPaymentDetailsFacility(f)}
                        title="جزئیات پرداخت"
                        className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 shadow-sm"
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setFacilityDetails(f)}
                        title="جزئیات تسهیلات"
                        className="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 shadow-sm"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditFacility(f)}
                        title="ویرایش"
                        className="bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-200 shadow-sm"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDelete(f)}
                        disabled={facilityPayments(f.id).length > 0}
                        title="حذف"
                        className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 disabled:bg-slate-100 disabled:border-slate-200 disabled:text-slate-400 transition-all duration-200 shadow-sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        </div>
      </div>

      <Dialog open={!!editFacility} onOpenChange={(o) => !o && setEditFacility(null)}>
        <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
          {editFacility && (
            <SimpleFacilityForm
              facility={editFacility}
              onBack={() => setEditFacility(null)}
              onSuccess={handleFacilitySaved}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={!!editPayment} onOpenChange={(o) => !o && setEditPayment(null)}>
        <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
          {editPayment && (
            <SimplePaymentForm
              payment={editPayment}
              onBack={() => setEditPayment(null)}
              onSuccess={handlePaymentSaved}
            />
          )}
        </DialogContent>
      </Dialog>

      {paymentDetailsFacility && (
        <Dialog open={true} onOpenChange={(open) => !open && setPaymentDetailsFacility(null)}>
          <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
            <PaymentDetailsReport
              facility={paymentDetailsFacility}
              onBack={() => setPaymentDetailsFacility(null)}
            />
          </DialogContent>
        </Dialog>
      )}
      
      {/* دیالوگ جزئیات تسهیلات */}
      {/* دیالوگ تأییدیه حذف */}
      {deleteConfirmFacility && (
        <Dialog open={true} onOpenChange={(open) => !open && setDeleteConfirmFacility(null)}>
          <DialogContent className="max-w-md">
            <div className="p-6 space-y-6">
              <h3 className="text-xl font-bold text-center">تأیید حذف</h3>
              <p className="text-center">
                آیا از حذف تسهیلات <span className="font-bold">{deleteConfirmFacility.bankName} - {deleteConfirmFacility.contractNumber}</span> اطمینان دارید؟
              </p>
              <div className="flex justify-center gap-4 pt-4">
                <Button variant="destructive" onClick={confirmDelete}>
                  بله، حذف شود
                </Button>
                <Button variant="outline" onClick={() => setDeleteConfirmFacility(null)}>
                  انصراف
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
      
      {facilityDetails && (
        <Dialog open={true} onOpenChange={(open) => !open && setFacilityDetails(null)}>
          <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-0 rounded-none border-0">
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6 space-y-8">
              {/* Enhanced Header */}
              <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl shadow-lg">
                      <Eye className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                        جزئیات تسهیلات
                      </h1>
                      <p className="text-slate-500 mt-1">مشاهده کامل اطلاعات و اقساط تسهیلات</p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setFacilityDetails(null)}
                    className="flex items-center gap-2 bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
                  >
                    <ArrowRight className="h-4 w-4" />
                    بازگشت
                  </Button>
                </div>
              </div>

              {/* Enhanced Facility Info Card */}
              <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-slate-200/60 p-6">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-6 w-6 text-blue-600" />
                    <h3 className="text-xl font-semibold text-slate-800">اطلاعات کلی تسهیلات</h3>
                  </div>
                </div>
                <div className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">نام بانک</span>
                      <span className="font-semibold text-slate-800 text-lg">{facilityDetails.bankName}</span>
                    </div>
                    <div className="bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-xl border border-emerald-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">شماره تسهیلات</span>
                      <span className="font-semibold text-slate-800 text-lg">{facilityDetails.contractNumber}</span>
                    </div>
                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">مبلغ کل</span>
                      <span className="font-semibold text-purple-700 text-lg">{formatCurrency(facilityDetails.amount)} ریال</span>
                    </div>
                    <div className="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-xl border border-orange-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">تعداد اقساط</span>
                      <span className="font-semibold text-slate-800 text-lg">{facilityDetails.installmentCount}</span>
                    </div>
                    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl border border-yellow-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">نرخ سود</span>
                      <span className="font-semibold text-slate-800 text-lg">{facilityDetails.interestRate}%</span>
                    </div>
                    <div className="bg-gradient-to-r from-red-50 to-rose-50 p-4 rounded-xl border border-red-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">نرخ جریمه</span>
                      <span className="font-semibold text-slate-800 text-lg">{facilityDetails.penaltyRate}%</span>
                    </div>
                    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-xl border border-indigo-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">کارمزد</span>
                      <span className="font-semibold text-slate-800 text-lg">{formatCurrency(facilityDetails.commissionAmount)} ریال</span>
                    </div>
                    <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-4 rounded-xl border border-teal-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">سایر هزینه‌ها</span>
                      <span className="font-semibold text-slate-800 text-lg">{formatCurrency(facilityDetails.otherAmount)} ریال</span>
                    </div>
                    <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-4 rounded-xl border border-slate-200/60 shadow-sm">
                      <span className="text-sm text-slate-600 block mb-1">تاریخ اخذ</span>
                      <span className="font-semibold text-slate-800 text-lg">{formatDate(facilityDetails.receivedDate)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Installments Table */}
              <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
                <div className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-slate-200/60 p-6">
                  <div className="flex items-center gap-3">
                    <FileText className="h-6 w-6 text-emerald-600" />
                    <h3 className="text-xl font-semibold text-slate-800">اقساط تسهیلات</h3>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <Table className="border-collapse">
                    <TableHeader>
                      <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 border-b-2 border-slate-200">
                        <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-blue-50">شماره قسط</TableHead>
                        <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-indigo-50">مبلغ اصل</TableHead>
                        <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-purple-50">مبلغ سود</TableHead>
                        <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-pink-50">تاریخ سررسید</TableHead>
                        <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-emerald-50">وضعیت پرداخت</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {facilityDetails.installments?.map((installment, index) => {
                        // بررسی وضعیت پرداخت قسط
                        const installmentPayments = payments.filter(
                          p => p.facilityId === facilityDetails.id && p.installmentNumber === index + 1
                        );
                        const isPaid = installmentPayments.length > 0;
                        const paidAmount = installmentPayments.reduce((sum, p) => sum + (p.principalAmount || 0), 0);
                        // Calcular el monto total del installment (principal + interés)
                        const installmentTotal = parseInt(installment.principalAmount || "0") + parseInt(installment.interestAmount || "0");
                        const isPaidFully = paidAmount >= installmentTotal;

                        return (
                          <TableRow
                            key={index}
                            className={`border-b border-slate-200 hover:bg-gradient-to-r hover:from-emerald-50/50 hover:to-teal-50/50 transition-all duration-200 ${
                              isPaidFully ? "bg-gradient-to-r from-green-50 to-emerald-50" :
                              isPaid ? "bg-gradient-to-r from-yellow-50 to-orange-50" :
                              (index % 2 === 0) ? 'bg-white' : 'bg-slate-50/30'
                            }`}
                          >
                            <TableCell className="p-4 border-r border-slate-200 font-bold text-blue-600 bg-blue-50/50">{index + 1}</TableCell>
                            <TableCell className="p-4 border-r border-slate-200 font-semibold text-emerald-700">{formatCurrency(parseInt(installment.principalAmount || "0"))}</TableCell>
                            <TableCell className="p-4 border-r border-slate-200 font-semibold text-purple-700">{formatCurrency(parseInt(installment.interestAmount || "0"))}</TableCell>
                            <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-600">{formatDate(installment.dueDate)}</TableCell>
                            <TableCell className="p-4 border-r border-slate-200">
                              {isPaidFully ? (
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                  <CheckCircle2 className="h-3 w-3 mr-1" />
                                  پرداخت شده
                                </span>
                              ) : isPaid ? (
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  پرداخت ناقص ({formatCurrency(paidAmount)})
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  پرداخت نشده
                                </span>
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default SimpleFacilityList;