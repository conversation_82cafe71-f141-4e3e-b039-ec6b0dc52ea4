import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Edit, Trash2, Plus, FileText, Printer } from "lucide-react";
import {
  getFacilities,
  getPayments,
  deleteFacility,
  deletePaymentsByFacility,
  getSettledInstallmentsCount,
} from "@/utils/storage";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Facility, Payment } from "@/types/types";
import SimpleFacilityForm from "@/components/facility/SimpleFacilityForm";
import SimplePaymentForm from "@/components/payment/SimplePaymentForm";
import PaymentDetailsReport from "@/components/payment/PaymentDetailsReport";
import { useToast } from "@/hooks/use-toast";
import * as XLSX from 'xlsx';

interface SimpleFacilityListProps {
  onBack: () => void;
  onAddFacility: () => void;
  onAddPayment: () => void;
}

const SimpleFacilityList = ({
  onBack,
  onAddFacility,
  onAddPayment,
}: SimpleFacilityListProps) => {
  const { toast } = useToast();
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [search, setSearch] = useState("");
  const [editFacility, setEditFacility] = useState<Facility | null>(null);
  const [editPayment, setEditPayment] = useState<Payment | null>(null);
  const [paymentDetailsFacility, setPaymentDetailsFacility] = useState<Facility | null>(null);
  const [facilityDetails, setFacilityDetails] = useState<Facility | null>(null);

  useEffect(() => {
    refreshData();
  }, []);

  const refreshData = () => {
    setFacilities(getFacilities());
    setPayments(getPayments());
  };

  const facilityPayments = (id: number) =>
    payments.filter((p) => p.facilityId === id);

  const filteredFacilities = useMemo(
    () =>
      facilities.filter((f) => {
        const installmentCount =
          typeof f.installmentCount === "string"
            ? parseInt(f.installmentCount)
            : f.installmentCount;
        const amount =
          typeof f.amount === "string" ? parseInt(f.amount) : f.amount;
        const term = search.trim();

        return (
          f.bankName.includes(term) || f.contractNumber.includes(term)
        );
      }),
    [facilities, search]
  );

  const [deleteConfirmFacility, setDeleteConfirmFacility] = useState<Facility | null>(null);
  
  const handleDelete = (facility: Facility) => {
    // نمایش تأییدیه حذف
    setDeleteConfirmFacility(facility);
  };
  
  const confirmDelete = () => {
    if (!deleteConfirmFacility) return;
    
    if (facilityPayments(deleteConfirmFacility.id).length > 0) {
      toast({ 
        title: "خطا", 
        description: "امکان حذف تسهیلاتی که دارای پرداخت هستند وجود ندارد",
        variant: "destructive" 
      });
      setDeleteConfirmFacility(null);
      return;
    }
    
    deleteFacility(deleteConfirmFacility.id);
    deletePaymentsByFacility(deleteConfirmFacility.id);
    toast({ title: "حذف شد" });
    refreshData();
    setDeleteConfirmFacility(null);
  };

  const handleFacilitySaved = () => {
    setEditFacility(null);
    refreshData();
  };

  const handlePaymentSaved = () => {
    setEditPayment(null);
    refreshData();
  };

  const paidInfo = (id: number) => {
    const list = facilityPayments(id);
    const principal = list.reduce((s, p) => s + (p.principalAmount || 0), 0);
    const interest = list.reduce((s, p) => s + (p.interestAmount || 0), 0);
    const penalty = list.reduce((s, p) => s + (p.penaltyAmount || 0), 0);
    
    // یافتن تسهیلات مربوطه برای دسترسی به مقادیر کارمزد و سایر
    const facility = facilities.find(f => f.id === id);
    const commission = facility?.commissionAmount || 0;
    const other = facility?.otherAmount || 0;
    
    return { principal, interest, penalty, commission, other, count: list.length };
  };

  const exportExcel = () => {
    // Usar XLSX para exportar a formato xlsx en lugar de CSV
    try {
      // Preparar los datos para Excel
      const data = facilities.map((f) => {
        const installmentCount =
          typeof f.installmentCount === "string"
            ? parseInt(f.installmentCount)
            : f.installmentCount;
        const amount =
          typeof f.amount === "string" ? parseInt(f.amount) : f.amount;

        const info = paidInfo(f.id);
        const settledCount = getSettledInstallmentsCount(f.id);
        const unpaidCount = installmentCount - settledCount;
        const unpaidAmount = amount - info.principal;

        return {
          'نام بانک': f.bankName,
          'شماره تسهیلات': f.contractNumber,
          'مبلغ کل': amount,
          'تعداد اقساط': installmentCount,
          'اقساط تسویه شده': settledCount,
          'اقساط تسویه نشده': unpaidCount,
          'مبلغ تسویه شده': info.principal,
          'باقیمانده اصل': unpaidAmount,
          'سود پرداخت شده': info.interest,
          'جریمه پرداخت شده': info.penalty,
          'کارمزد پرداخت شده': info.commission,
          'سایر پرداخت شده': info.other,
          'نرخ سود': f.interestRate,
          'نرخ جریمه': f.penaltyRate,
          'تاریخ اخذ': f.receivedDate,
        };
      });

      // Crear un libro de trabajo y una hoja
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "تسهیلات");
      
      // Ajustar el ancho de las columnas
      const maxWidth = 20;
      const cols = Object.keys(data[0]).map(() => ({ wch: maxWidth }));
      worksheet['!cols'] = cols;
      
      // Guardar el archivo en formato xlsx
      XLSX.writeFile(workbook, "facilities.xlsx");
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({ 
        title: "خطا", 
        description: "خطا در صدور فایل اکسل",
        variant: "destructive" 
      });
    }
  };

  const exportPDF = () => {
    window.print();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">لیست تسهیلات</h2>
        <div className="flex gap-2">
          <Button onClick={onAddPayment} variant="outline" className="flex items-center gap-2">
            <Plus className="h-4 w-4" /> پرداخت جدید
          </Button>
          <Button onClick={onAddFacility} className="flex items-center gap-2">
            <Plus className="h-4 w-4" /> تسهیلات جدید
          </Button>
          <Button variant="outline" onClick={exportExcel} className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            دریافت Excel
          </Button>
          <Button variant="outline" onClick={exportPDF} className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            دریافت PDF
          </Button>
          <Button variant="outline" onClick={onBack}>
            بازگشت
          </Button>
        </div>
      </div>

      <Input
        placeholder="جستجوی بانک یا شماره تسهیلات"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className="overflow-x-auto">
        <Table className="border-collapse border border-gray-300">
          <TableHeader>
            <TableRow>
              <TableHead className="text-right border border-gray-300">نام بانک</TableHead>
              <TableHead className="text-right border border-gray-300">شماره تسهیلات</TableHead>
              <TableHead className="text-right border border-gray-300">مبلغ کل</TableHead>
              <TableHead className="text-right border border-gray-300">تعداد اقساط</TableHead>
              <TableHead className="text-right border border-gray-300">اقساط تسویه شده</TableHead>
              <TableHead className="text-right border border-gray-300">اقساط تسویه نشده</TableHead>
              <TableHead className="text-right border border-gray-300">مبلغ تسویه شده</TableHead>
              <TableHead className="text-right border border-gray-300">باقیمانده اصل</TableHead>
              <TableHead className="text-right border border-gray-300">سود پرداخت شده</TableHead>
              <TableHead className="text-right border border-gray-300">جریمه پرداخت شده</TableHead>
              <TableHead className="text-right border border-gray-300">کارمزد پرداخت شده</TableHead>
              <TableHead className="text-right border border-gray-300">سایر پرداخت شده</TableHead>
              <TableHead className="text-right border border-gray-300">نرخ سود</TableHead>
              <TableHead className="text-right border border-gray-300">نرخ جریمه</TableHead>
              <TableHead className="text-right border border-gray-300">تاریخ اخذ</TableHead>
              <TableHead className="border border-gray-300" />
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredFacilities.map((f) => {
              const installmentCount =
                typeof f.installmentCount === "string"
                  ? parseInt(f.installmentCount)
                  : f.installmentCount;
              const amount =
                typeof f.amount === "string" ? parseInt(f.amount) : f.amount;

              const info = paidInfo(f.id);
              // محاسبه تعداد اقساط تسویه شده بر اساس مبلغ اصل پرداخت شده
              const settledCount = getSettledInstallmentsCount(f.id);
              const unpaidCount = installmentCount - settledCount;
              const unpaidAmount = amount - info.principal;

              return (
                <TableRow key={f.id} className="hover:bg-gray-50">
                  <TableCell className="border border-gray-300">{f.bankName}</TableCell>
                  <TableCell className="border border-gray-300">{f.contractNumber}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(amount)}</TableCell>
                  <TableCell className="border border-gray-300">{installmentCount}</TableCell>
                  <TableCell className="border border-gray-300">{settledCount}</TableCell>
                  <TableCell className="border border-gray-300">{unpaidCount}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(info.principal)}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(unpaidAmount)}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(info.interest)}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(info.penalty)}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(info.commission)}</TableCell>
                  <TableCell className="text-xs md:text-sm border border-gray-300">{formatCurrency(info.other)}</TableCell>
                  <TableCell className="border border-gray-300">{f.interestRate}%</TableCell>
                  <TableCell className="border border-gray-300">{f.penaltyRate}%</TableCell>
                  <TableCell className="border border-gray-300">{formatDate(f.receivedDate)}</TableCell>
                  <TableCell className="border border-gray-300">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setPaymentDetailsFacility(f)}
                        title="جزئیات پرداخت"
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setFacilityDetails(f)}
                        title="جزئیات تسهیلات"
                      >
                        <span className="text-xs">جزئیات</span>
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditFacility(f)}
                        title="ویرایش"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDelete(f)}
                        disabled={facilityPayments(f.id).length > 0}
                        title="حذف"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!editFacility} onOpenChange={(o) => !o && setEditFacility(null)}>
        <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
          {editFacility && (
            <SimpleFacilityForm
              facility={editFacility}
              onBack={() => setEditFacility(null)}
              onSuccess={handleFacilitySaved}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={!!editPayment} onOpenChange={(o) => !o && setEditPayment(null)}>
        <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
          {editPayment && (
            <SimplePaymentForm
              payment={editPayment}
              onBack={() => setEditPayment(null)}
              onSuccess={handlePaymentSaved}
            />
          )}
        </DialogContent>
      </Dialog>

      {paymentDetailsFacility && (
        <Dialog open={true} onOpenChange={(open) => !open && setPaymentDetailsFacility(null)}>
          <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
            <PaymentDetailsReport
              facility={paymentDetailsFacility}
              onBack={() => setPaymentDetailsFacility(null)}
            />
          </DialogContent>
        </Dialog>
      )}
      
      {/* دیالوگ جزئیات تسهیلات */}
      {/* دیالوگ تأییدیه حذف */}
      {deleteConfirmFacility && (
        <Dialog open={true} onOpenChange={(open) => !open && setDeleteConfirmFacility(null)}>
          <DialogContent className="max-w-md">
            <div className="p-6 space-y-6">
              <h3 className="text-xl font-bold text-center">تأیید حذف</h3>
              <p className="text-center">
                آیا از حذف تسهیلات <span className="font-bold">{deleteConfirmFacility.bankName} - {deleteConfirmFacility.contractNumber}</span> اطمینان دارید؟
              </p>
              <div className="flex justify-center gap-4 pt-4">
                <Button variant="destructive" onClick={confirmDelete}>
                  بله، حذف شود
                </Button>
                <Button variant="outline" onClick={() => setDeleteConfirmFacility(null)}>
                  انصراف
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
      
      {facilityDetails && (
        <Dialog open={true} onOpenChange={(open) => !open && setFacilityDetails(null)}>
          <DialogContent className="fixed left-0 top-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 overflow-y-auto p-6 rounded-none">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">جزئیات تسهیلات</h2>
                <Button variant="outline" onClick={() => setFacilityDetails(null)}>بازگشت</Button>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-4">اطلاعات کلی تسهیلات</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">نام بانک</p>
                    <p className="font-medium">{facilityDetails.bankName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">شماره تسهیلات</p>
                    <p className="font-medium">{facilityDetails.contractNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">مبلغ کل</p>
                    <p className="font-medium">{formatCurrency(facilityDetails.amount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">تعداد اقساط</p>
                    <p className="font-medium">{facilityDetails.installmentCount}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">نرخ سود</p>
                    <p className="font-medium">{facilityDetails.interestRate}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">نرخ جریمه</p>
                    <p className="font-medium">{facilityDetails.penaltyRate}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">تاریخ اخذ</p>
                    <p className="font-medium">{formatDate(facilityDetails.receivedDate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">کارمزد</p>
                    <p className="font-medium">{formatCurrency(facilityDetails.commissionAmount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">سایر هزینه‌ها</p>
                    <p className="font-medium">{formatCurrency(facilityDetails.otherAmount)}</p>
                  </div>
                </div>
              </div>
              
              {/* نمایش اقساط */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-4">اقساط تسهیلات</h3>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right">شماره قسط</TableHead>
                        <TableHead className="text-right">مبلغ اصل</TableHead>
                        <TableHead className="text-right">مبلغ سود</TableHead>
                        <TableHead className="text-right">تاریخ سررسید</TableHead>
                        <TableHead className="text-right">وضعیت پرداخت</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {facilityDetails.installments?.map((installment, index) => {
                        // بررسی وضعیت پرداخت قسط
                        const installmentPayments = payments.filter(
                          p => p.facilityId === facilityDetails.id && p.installmentNumber === index + 1
                        );
                        const isPaid = installmentPayments.length > 0;
                        const paidAmount = installmentPayments.reduce((sum, p) => sum + (p.principalAmount || 0), 0);
                        // Calcular el monto total del installment (principal + interés)
                        const installmentTotal = parseInt(installment.principalAmount || "0") + parseInt(installment.interestAmount || "0");
                        const isPaidFully = paidAmount >= installmentTotal;
                        
                        return (
                          <TableRow key={index} className={isPaidFully ? "bg-green-50" : (isPaid ? "bg-yellow-50" : "")}>
                            <TableCell>{index + 1}</TableCell>
                            <TableCell className="text-xs md:text-sm">{formatCurrency(parseInt(installment.principalAmount || "0"))}</TableCell>
                            <TableCell className="text-xs md:text-sm">{formatCurrency(parseInt(installment.interestAmount || "0"))}</TableCell>
                            <TableCell>{formatDate(installment.dueDate)}</TableCell>
                            <TableCell>
                              {isPaidFully ? (
                                <span className="text-green-600 font-medium">پرداخت شده</span>
                              ) : isPaid ? (
                                <span className="text-yellow-600 font-medium">پرداخت ناقص ({formatCurrency(paidAmount)})</span>
                              ) : (
                                <span className="text-red-600 font-medium">پرداخت نشده</span>
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default SimpleFacilityList;