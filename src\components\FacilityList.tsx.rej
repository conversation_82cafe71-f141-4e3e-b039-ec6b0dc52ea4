diff a/src/components/FacilityList.tsx b/src/components/FacilityList.tsx	(rejected hunks)
@@ -1,90 +1,94 @@
 
 import { useState, useEffect } from "react";
 import { Button } from "@/components/ui/button";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Badge } from "@/components/ui/badge";
 import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
 import { Edit, Trash2, Plus } from "lucide-react";
 import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
 } from "@/components/ui/dialog";
+import SimpleFacilityForm from "./facility/SimpleFacilityForm";
+import SimplePaymentForm from "./payment/SimplePaymentForm";
 import { useToast } from "@/hooks/use-toast";
 
 interface FacilityListProps {
   onBack: () => void;
   onAddFacility: () => void;
   onAddPayment: () => void;
 }
 
 interface Facility {
   id: number;
   facilityType: string;
   amount: string;
   receivedDate: string;
   bankName: string;
   facilityNumber: string;
   installmentCount: string;
   interestRate: string;
   penaltyRate: string;
   installmentAmount: string;
   firstInstallmentDate: string;
 }
 
 interface Payment {
   id: number;
   facilityId: string;
   installmentNumber: string;
   paymentDate: string;
   principalAmount: string;
   interestAmount: string;
   penaltyAmount: string;
   totalAmount: string;
   notes: string;
   facilityInfo: Facility;
 }
 
 const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps) => {
   const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
   const [payments, setPayments] = useState<Payment[]>([]);
   const [deleteDialog, setDeleteDialog] = useState<{
     isOpen: boolean;
     type: 'facility' | 'payment';
     id: number;
     title: string;
   }>({
     isOpen: false,
     type: 'facility',
     id: 0,
     title: '',
   });
+  const [editFacility, setEditFacility] = useState<Facility | null>(null);
+  const [editPayment, setEditPayment] = useState<Payment | null>(null);
 
   useEffect(() => {
     const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
     const savedPayments = JSON.parse(localStorage.getItem('payments') || '[]');
     setFacilities(savedFacilities);
     setPayments(savedPayments);
   }, []);
 
   const formatMoney = (amount: string) => {
     return new Intl.NumberFormat('fa-IR').format(parseFloat(amount) || 0);
   };
 
   const formatDate = (dateString: string) => {
     try {
       const date = new Date(dateString);
       return new Intl.DateTimeFormat('fa-IR').format(date);
     } catch {
       return dateString;
     }
   };
 
   const getFacilityPayments = (facilityId: number) => {
     return payments.filter(payment => payment.facilityId === facilityId.toString());
   };
 
@@ -130,50 +134,67 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
       localStorage.setItem('facilities', JSON.stringify(updatedFacilities));
       
       // Also remove related payments
       const updatedPayments = payments.filter(p => p.facilityId !== deleteDialog.id.toString());
       setPayments(updatedPayments);
       localStorage.setItem('payments', JSON.stringify(updatedPayments));
       
       toast({
         title: "موفقیت",
         description: "تسهیلات و پرداخت‌های مرتبط حذف شد",
       });
     } else {
       const updatedPayments = payments.filter(p => p.id !== deleteDialog.id);
       setPayments(updatedPayments);
       localStorage.setItem('payments', JSON.stringify(updatedPayments));
       
       toast({
         title: "موفقیت",
         description: "پرداخت حذف شد",
       });
     }
     
     setDeleteDialog({ isOpen: false, type: 'facility', id: 0, title: '' });
   };
 
+  const refreshData = () => {
+    const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
+    const savedPayments = JSON.parse(localStorage.getItem('payments') || '[]');
+    setFacilities(savedFacilities);
+    setPayments(savedPayments);
+  };
+
+  const handleFacilityUpdated = () => {
+    setEditFacility(null);
+    refreshData();
+  };
+
+  const handlePaymentUpdated = () => {
+    setEditPayment(null);
+    refreshData();
+  };
+
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
         <h2 className="text-2xl font-bold text-gray-800">لیست تسهیلات و پرداخت‌ها</h2>
         <Button variant="outline" onClick={onBack}>
           بازگشت
         </Button>
       </div>
 
       <Tabs defaultValue="facilities" className="w-full">
         <TabsList className="grid w-full grid-cols-2">
           <TabsTrigger value="facilities">تسهیلات</TabsTrigger>
           <TabsTrigger value="payments">پرداخت‌ها</TabsTrigger>
         </TabsList>
 
         <TabsContent value="facilities" className="space-y-4">
           <div className="flex justify-end">
             <Button onClick={onAddFacility} className="flex items-center gap-2">
               <Plus className="h-4 w-4" />
               افزودن تسهیلات
             </Button>
           </div>
           
           {facilities.length === 0 ? (
             <Card>
@@ -185,51 +206,51 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
             facilities.map((facility) => {
               const paymentStatus = getPaymentStatus(facility);
               const totalPaid = getTotalPaid(facility.id);
               const facilityPayments = getFacilityPayments(facility.id);
               
               return (
                 <Card key={facility.id} className="bg-white shadow-md">
                   <CardHeader>
                     <div className="flex justify-between items-start">
                       <div>
                         <CardTitle className="text-lg">
                           {facility.facilityType} - {facility.bankName}
                         </CardTitle>
                         <p className="text-sm text-gray-600">
                           شماره: {facility.facilityNumber}
                         </p>
                       </div>
                       <div className="flex items-center gap-2">
                         <Badge className={`${paymentStatus.color} text-white`}>
                           {paymentStatus.status}
                         </Badge>
                         <div className="flex gap-1">
                           <Button
                             variant="outline"
                             size="sm"
-                            onClick={() => {/* TODO: Implement edit */}}
+                            onClick={() => setEditFacility(facility)}
                             className="h-8 w-8 p-0"
                           >
                             <Edit className="h-4 w-4" />
                           </Button>
                           <Button
                             variant="outline"
                             size="sm"
                             onClick={() => handleDeleteFacility(facility.id)}
                             className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                           >
                             <Trash2 className="h-4 w-4" />
                           </Button>
                         </div>
                       </div>
                     </div>
                   </CardHeader>
                   <CardContent>
                     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                       <div>
                         <span className="text-sm text-gray-600">مبلغ کل:</span>
                         <p className="font-semibold">{formatMoney(facility.amount)} ریال</p>
                       </div>
                       <div>
                         <span className="text-sm text-gray-600">پرداخت شده:</span>
                         <p className="font-semibold text-green-600">{formatMoney(totalPaid.toString())} ریال</p>
@@ -281,51 +302,51 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
             </Card>
           ) : (
             <div className="space-y-4">
               {payments
                 .sort((a, b) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime())
                 .map((payment) => (
                   <Card key={payment.id} className="bg-white shadow-md">
                     <CardHeader>
                       <div className="flex justify-between items-start">
                         <div>
                           <CardTitle className="text-lg">
                             {payment.facilityInfo?.facilityType} - قسط {payment.installmentNumber}
                           </CardTitle>
                           <p className="text-sm text-gray-600">
                             {payment.facilityInfo?.bankName} - {payment.facilityInfo?.facilityNumber}
                           </p>
                         </div>
                         <div className="flex items-center gap-2">
                           <Badge variant="outline">
                             {formatDate(payment.paymentDate)}
                           </Badge>
                           <div className="flex gap-1">
                             <Button
                               variant="outline"
                               size="sm"
-                              onClick={() => {/* TODO: Implement edit */}}
+                              onClick={() => setEditPayment(payment)}
                               className="h-8 w-8 p-0"
                             >
                               <Edit className="h-4 w-4" />
                             </Button>
                             <Button
                               variant="outline"
                               size="sm"
                               onClick={() => handleDeletePayment(payment.id)}
                               className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                             >
                               <Trash2 className="h-4 w-4" />
                             </Button>
                           </div>
                         </div>
                       </div>
                     </CardHeader>
                     <CardContent>
                       <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                         <div>
                           <span className="text-sm text-gray-600">اصل:</span>
                           <p className="font-semibold text-blue-600">
                             {formatMoney(payment.principalAmount)} ریال
                           </p>
                         </div>
                         <div>
@@ -362,30 +383,40 @@ const FacilityList = ({ onBack, onAddFacility, onAddPayment }: FacilityListProps
       </Tabs>
 
       <Dialog open={deleteDialog.isOpen} onOpenChange={(open) => !open && setDeleteDialog({ isOpen: false, type: 'facility', id: 0, title: '' })}>
         <DialogContent>
           <DialogHeader>
             <DialogTitle>تأیید حذف</DialogTitle>
             <DialogDescription>
               آیا مطمئن هستید که می‌خواهید "{deleteDialog.title}" را حذف کنید؟
               {deleteDialog.type === 'facility' && (
                 <span className="block mt-2 text-red-600">
                   تمام پرداخت‌های مرتبط با این تسهیلات نیز حذف خواهد شد.
                 </span>
               )}
             </DialogDescription>
           </DialogHeader>
           <DialogFooter>
             <Button variant="outline" onClick={() => setDeleteDialog({ isOpen: false, type: 'facility', id: 0, title: '' })}>
               لغو
             </Button>
             <Button variant="destructive" onClick={confirmDelete}>
               حذف
             </Button>
           </DialogFooter>
         </DialogContent>
       </Dialog>
+      <Dialog open={!!editFacility} onOpenChange={(open) => !open && setEditFacility(null)}>
+        <DialogContent className="max-w-xl">
+          <SimpleFacilityForm onBack={() => setEditFacility(null)} onSuccess={handleFacilityUpdated} facility={editFacility!} />
+        </DialogContent>
+      </Dialog>
+      <Dialog open={!!editPayment} onOpenChange={(open) => !open && setEditPayment(null)}>
+        <DialogContent className="max-w-xl">
+          <SimplePaymentForm onBack={() => setEditPayment(null)} onSuccess={handlePaymentUpdated} payment={editPayment!} />
+        </DialogContent>
+      </Dialog>
     </div>
   );
 };
 
 export default FacilityList;
