diff a/src/utils/formatMoney.ts b/src/utils/formatMoney.ts	(rejected hunks)
@@ -1,46 +1,46 @@
 
+import { toEnglishDigits } from './formatters';
+
 export const formatMoney = (value: string | number): string => {
-  // بررسی مقادیر خالی یا نامعتبر
-  if (!value || value === '' || value === null || value === undefined) {
+  if (value === null || value === undefined || value === '') {
     return '';
   }
 
-  // تبدیل به رشته و پاک‌سازی
   let cleanValue = '';
   if (typeof value === 'number') {
     cleanValue = value.toString();
   } else {
-    cleanValue = value.toString().replace(/[^\d]/g, '');
+    cleanValue = toEnglishDigits(value).replace(/[^0-9]/g, '');
   }
   
   // اگر هیچ عددی نماند
   if (!cleanValue || cleanValue === '0') {
     return '';
   }
 
   // تبدیل به عدد و فرمت کردن
   const numValue = parseInt(cleanValue, 10);
   
   if (isNaN(numValue) || numValue === 0) {
     return '';
   }
 
   // استفاده از فرمت کننده فارسی
   return new Intl.NumberFormat('fa-IR').format(numValue);
 };
 
 export const parseMoney = (formattedValue: string): string => {
   if (!formattedValue) return '';
-  
-  // حذف تمام کاراکترهای غیرعددی
-  const cleanValue = formattedValue.replace(/[^\d]/g, '');
+
+  const english = toEnglishDigits(formattedValue);
+  const cleanValue = english.replace(/[^0-9]/g, '');
   return cleanValue || '';
 };
 
 export const handleMoneyInput = (
   value: string,
   onChange: (value: string) => void
 ) => {
   const cleanValue = parseMoney(value);
   onChange(cleanValue);
 };
