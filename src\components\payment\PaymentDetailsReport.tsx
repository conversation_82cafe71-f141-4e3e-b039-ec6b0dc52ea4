import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getPayments } from "@/utils/storage";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Payment, Facility } from "@/types/types";
import { FileText, Printer } from "lucide-react";

interface PaymentDetailsReportProps {
  facility: Facility;
  onBack: () => void;
}

const PaymentDetailsReport: React.FC<PaymentDetailsReportProps> = ({ facility, onBack }) => {
  const [payments, setPayments] = useState<Payment[]>([]);

  useEffect(() => {
    try {
      const allPayments = getPayments();
      const facilityPayments = allPayments.filter(p => p.facilityId === facility.id);
      setPayments(facilityPayments);
    } catch (error) {
      console.error("Error loading payments:", error);
    }
  }, [facility.id]);

  // محاسبه جمع مقادیر
  const totalPrincipal = payments.reduce((sum, p) => sum + (p.principalAmount || 0), 0);
  const totalInterest = payments.reduce((sum, p) => sum + (p.interestAmount || 0), 0);
  const totalPenalty = payments.reduce((sum, p) => sum + (p.penaltyAmount || 0), 0);
  const totalCommission = payments.reduce((sum, p) => sum + (p.commissionAmount || 0), 0);
  const totalOther = payments.reduce((sum, p) => sum + (p.otherAmount || 0), 0);
  const totalFee = payments.reduce((sum, p) => sum + (p.feeAmount || 0), 0);
  const grandTotal = totalPrincipal + totalInterest + totalPenalty + totalCommission + totalOther + totalFee;

  // صادر کردن به اکسل
  const exportToExcel = () => {
    const header = 'شماره قسط,تاریخ پرداخت,مبلغ اصل,مبلغ سود,مبلغ جریمه,کارمزد,سایر,هزینه,مبلغ کل,یادداشت';
    const rows = payments.map(p => [
      p.installmentNumber,
      p.paymentDate,
      p.principalAmount,
      p.interestAmount,
      p.penaltyAmount,
      p.commissionAmount,
      p.otherAmount,
      p.feeAmount,
      p.totalAmount,
      p.notes || ''
    ].join(','));

    const csv = [header, ...rows].join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `payment-details-${facility.contractNumber}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // چاپ به صورت PDF
  const printAsPDF = () => {
    window.print();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">جزئیات پرداخت تسهیلات</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportToExcel} className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            دریافت Excel
          </Button>
          <Button variant="outline" onClick={printAsPDF} className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            دریافت PDF
          </Button>
          <Button variant="outline" onClick={onBack}>
            بازگشت
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>اطلاعات تسهیلات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <span className="font-semibold">بانک: </span>
              <span>{facility.bankName}</span>
            </div>
            <div>
              <span className="font-semibold">شماره تسهیلات: </span>
              <span>{facility.contractNumber}</span>
            </div>
            <div>
              <span className="font-semibold">مبلغ کل: </span>
              <span>{formatCurrency(facility.amount)} ریال</span>
            </div>
            <div>
              <span className="font-semibold">تعداد اقساط: </span>
              <span>{facility.installmentCount}</span>
            </div>
            <div>
              <span className="font-semibold">نرخ سود: </span>
              <span>{facility.interestRate}%</span>
            </div>
            <div>
              <span className="font-semibold">نرخ جریمه: </span>
              <span>{facility.penaltyRate}%</span>
            </div>
            <div>
              <span className="font-semibold">کارمزد: </span>
              <span>{formatCurrency(facility.commissionAmount || 0)} ریال</span>
            </div>
            <div>
              <span className="font-semibold">سایر: </span>
              <span>{formatCurrency(facility.otherAmount || 0)} ریال</span>
            </div>
            <div>
              <span className="font-semibold">تاریخ اخذ: </span>
              <span>{formatDate(facility.receivedDate)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">شماره قسط</TableHead>
              <TableHead className="text-right">تاریخ پرداخت</TableHead>
              <TableHead className="text-right">مبلغ اصل (ریال)</TableHead>
              <TableHead className="text-right">مبلغ سود (ریال)</TableHead>
              <TableHead className="text-right">مبلغ جریمه (ریال)</TableHead>
              <TableHead className="text-right">کارمزد (ریال)</TableHead>
              <TableHead className="text-right">سایر (ریال)</TableHead>
              <TableHead className="text-right">هزینه (ریال)</TableHead>
              <TableHead className="text-right">مبلغ کل (ریال)</TableHead>
              <TableHead className="text-right">یادداشت</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell>{payment.installmentNumber}</TableCell>
                <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                <TableCell>{formatCurrency(payment.principalAmount)}</TableCell>
                <TableCell>{formatCurrency(payment.interestAmount)}</TableCell>
                <TableCell>{formatCurrency(payment.penaltyAmount)}</TableCell>
                <TableCell>{formatCurrency(payment.commissionAmount)}</TableCell>
                <TableCell>{formatCurrency(payment.otherAmount)}</TableCell>
                <TableCell>{formatCurrency(payment.feeAmount)}</TableCell>
                <TableCell>{formatCurrency(payment.totalAmount)}</TableCell>
                <TableCell>{payment.notes}</TableCell>
              </TableRow>
            ))}
            {/* ردیف جمع */}
            <TableRow className="font-bold bg-gray-50">
              <TableCell colSpan={2}>جمع کل</TableCell>
              <TableCell>{formatCurrency(totalPrincipal)}</TableCell>
              <TableCell>{formatCurrency(totalInterest)}</TableCell>
              <TableCell>{formatCurrency(totalPenalty)}</TableCell>
              <TableCell>{formatCurrency(totalCommission)}</TableCell>
              <TableCell>{formatCurrency(totalOther)}</TableCell>
              <TableCell>{formatCurrency(totalFee)}</TableCell>
              <TableCell>{formatCurrency(grandTotal)}</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>

      {payments.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          هیچ پرداختی برای این تسهیلات ثبت نشده است.
        </div>
      )}
    </div>
  );
};

export default PaymentDetailsReport;