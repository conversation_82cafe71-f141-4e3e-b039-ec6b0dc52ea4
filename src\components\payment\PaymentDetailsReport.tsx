import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getPayments } from "@/utils/storage";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Payment, Facility } from "@/types/types";
import { FileText, Printer, ArrowRight, BarChart3, TrendingUp, Calculator } from "lucide-react";

interface PaymentDetailsReportProps {
  facility: Facility;
  onBack: () => void;
}

const PaymentDetailsReport: React.FC<PaymentDetailsReportProps> = ({ facility, onBack }) => {
  const [payments, setPayments] = useState<Payment[]>([]);

  useEffect(() => {
    try {
      const allPayments = getPayments();
      const facilityPayments = allPayments.filter(p => p.facilityId === facility.id);
      setPayments(facilityPayments);
    } catch (error) {
      console.error("Error loading payments:", error);
    }
  }, [facility.id]);

  // محاسبه جمع مقادیر
  const totalPrincipal = payments.reduce((sum, p) => sum + (p.principalAmount || 0), 0);
  const totalInterest = payments.reduce((sum, p) => sum + (p.interestAmount || 0), 0);
  const totalPenalty = payments.reduce((sum, p) => sum + (p.penaltyAmount || 0), 0);
  const totalCommission = payments.reduce((sum, p) => sum + (p.commissionAmount || 0), 0);
  const totalOther = payments.reduce((sum, p) => sum + (p.otherAmount || 0), 0);
  const totalFee = payments.reduce((sum, p) => sum + (p.feeAmount || 0), 0);
  const grandTotal = totalPrincipal + totalInterest + totalPenalty + totalCommission + totalOther + totalFee;

  // صادر کردن به اکسل
  const exportToExcel = () => {
    const header = 'شماره قسط,تاریخ پرداخت,مبلغ اصل,مبلغ سود,مبلغ جریمه,کارمزد,سایر,هزینه,مبلغ کل,یادداشت';
    const rows = payments.map(p => [
      p.installmentNumber,
      p.paymentDate,
      p.principalAmount,
      p.interestAmount,
      p.penaltyAmount,
      p.commissionAmount,
      p.otherAmount,
      p.feeAmount,
      p.totalAmount,
      p.notes || ''
    ].join(','));

    const csv = [header, ...rows].join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `payment-details-${facility.contractNumber}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // چاپ به صورت PDF
  const printAsPDF = () => {
    window.print();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6 space-y-8">
      {/* Enhanced Header */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-xl shadow-lg">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                جزئیات پرداخت تسهیلات
              </h1>
              <p className="text-slate-500 mt-1">گزارش کامل پرداخت‌های انجام شده</p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={exportToExcel}
              className="flex items-center gap-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 transition-all duration-200 shadow-sm"
            >
              <FileText className="h-4 w-4" />
              دریافت Excel
            </Button>
            <Button
              variant="outline"
              onClick={printAsPDF}
              className="flex items-center gap-2 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-all duration-200 shadow-sm"
            >
              <Printer className="h-4 w-4" />
              دریافت PDF
            </Button>
            <Button
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2 bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
            >
              <ArrowRight className="h-4 w-4" />
              بازگشت
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Facility Info Card */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-slate-200/60 p-6">
          <div className="flex items-center gap-3">
            <Calculator className="h-6 w-6 text-purple-600" />
            <h3 className="text-xl font-semibold text-slate-800">اطلاعات تسهیلات</h3>
          </div>
        </div>
        <div className="p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">بانک</span>
              <span className="font-semibold text-slate-800 text-lg">{facility.bankName}</span>
            </div>
            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-xl border border-emerald-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">شماره تسهیلات</span>
              <span className="font-semibold text-slate-800 text-lg">{facility.contractNumber}</span>
            </div>
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">مبلغ کل</span>
              <span className="font-semibold text-purple-700 text-lg">{formatCurrency(facility.amount)} ریال</span>
            </div>
            <div className="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-xl border border-orange-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">تعداد اقساط</span>
              <span className="font-semibold text-slate-800 text-lg">{facility.installmentCount}</span>
            </div>
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl border border-yellow-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">نرخ سود</span>
              <span className="font-semibold text-slate-800 text-lg">{facility.interestRate}%</span>
            </div>
            <div className="bg-gradient-to-r from-red-50 to-rose-50 p-4 rounded-xl border border-red-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">نرخ جریمه</span>
              <span className="font-semibold text-slate-800 text-lg">{facility.penaltyRate}%</span>
            </div>
            <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-xl border border-indigo-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">کارمزد</span>
              <span className="font-semibold text-slate-800 text-lg">{formatCurrency(facility.commissionAmount || 0)} ریال</span>
            </div>
            <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-4 rounded-xl border border-teal-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">سایر</span>
              <span className="font-semibold text-slate-800 text-lg">{formatCurrency(facility.otherAmount || 0)} ریال</span>
            </div>
            <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-4 rounded-xl border border-slate-200/60 shadow-sm">
              <span className="text-sm text-slate-600 block mb-1">تاریخ اخذ</span>
              <span className="font-semibold text-slate-800 text-lg">{formatDate(facility.receivedDate)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Payments Table */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
        <div className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-slate-200/60 p-6">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-6 w-6 text-emerald-600" />
            <h3 className="text-xl font-semibold text-slate-800">جدول پرداخت‌ها</h3>
          </div>
        </div>
        <div className="overflow-x-auto">
          <Table className="border-collapse">
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 border-b-2 border-slate-200">
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-blue-50">شماره قسط</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-indigo-50">تاریخ پرداخت</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-purple-50">مبلغ اصل (ریال)</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-pink-50">مبلغ سود (ریال)</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-rose-50">مبلغ جریمه (ریال)</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-orange-50">کارمزد (ریال)</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-amber-50">سایر (ریال)</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-yellow-50">هزینه (ریال)</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-emerald-50">مبلغ کل (ریال)</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-teal-50">یادداشت</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map((payment, index) => (
                <TableRow
                  key={payment.id}
                  className={`border-b border-slate-200 hover:bg-gradient-to-r hover:from-emerald-50/50 hover:to-teal-50/50 transition-all duration-200 ${
                    (index % 2 === 0) ? 'bg-white' : 'bg-slate-50/30'
                  }`}
                >
                  <TableCell className="p-4 border-r border-slate-200 font-bold text-blue-600 bg-blue-50/50">{payment.installmentNumber}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{formatDate(payment.paymentDate)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-emerald-700">{formatCurrency(payment.principalAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-purple-700">{formatCurrency(payment.interestAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-red-700">{formatCurrency(payment.penaltyAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-orange-700">{formatCurrency(payment.commissionAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-amber-700">{formatCurrency(payment.otherAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-yellow-700">{formatCurrency(payment.feeAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-bold text-teal-700 bg-teal-50/50">{formatCurrency(payment.totalAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 text-slate-600 text-sm">{payment.notes}</TableCell>
                </TableRow>
              ))}
              {/* Enhanced Total Row */}
              <TableRow className="bg-gradient-to-r from-slate-100 to-slate-200 border-t-2 border-slate-300">
                <TableCell colSpan={2} className="p-4 border-r border-slate-200 font-bold text-slate-800 text-lg bg-gradient-to-r from-blue-50 to-indigo-50">
                  جمع کل
                </TableCell>
                <TableCell className="p-4 border-r border-slate-200 font-bold text-emerald-800 bg-emerald-50">{formatCurrency(totalPrincipal)}</TableCell>
                <TableCell className="p-4 border-r border-slate-200 font-bold text-purple-800 bg-purple-50">{formatCurrency(totalInterest)}</TableCell>
                <TableCell className="p-4 border-r border-slate-200 font-bold text-red-800 bg-red-50">{formatCurrency(totalPenalty)}</TableCell>
                <TableCell className="p-4 border-r border-slate-200 font-bold text-orange-800 bg-orange-50">{formatCurrency(totalCommission)}</TableCell>
                <TableCell className="p-4 border-r border-slate-200 font-bold text-amber-800 bg-amber-50">{formatCurrency(totalOther)}</TableCell>
                <TableCell className="p-4 border-r border-slate-200 font-bold text-yellow-800 bg-yellow-50">{formatCurrency(totalFee)}</TableCell>
                <TableCell className="p-4 border-r border-slate-200 font-bold text-teal-800 bg-teal-100 text-xl">{formatCurrency(grandTotal)}</TableCell>
                <TableCell className="p-4 border-r border-slate-200"></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>

      {payments.length === 0 && (
        <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-12">
          <div className="text-center">
            <div className="bg-gradient-to-r from-gray-100 to-slate-100 p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
              <FileText className="h-12 w-12 text-slate-400" />
            </div>
            <h3 className="text-xl font-semibold text-slate-600 mb-2">هیچ پرداختی ثبت نشده</h3>
            <p className="text-slate-500">برای این تسهیلات هنوز هیچ پرداختی ثبت نشده است.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentDetailsReport;