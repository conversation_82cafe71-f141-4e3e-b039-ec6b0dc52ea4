diff a/src/components/Dashboard.tsx b/src/components/Dashboard.tsx	(rejected hunks)
@@ -1,111 +1,204 @@
 
+import { useEffect, useState } from "react";
 import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
-import { Calculator, FileText, List, Database, TrendingUp } from "lucide-react";
+import { Calculator, FileText, List, TrendingUp } from "lucide-react";
+import { getFacilities, getPayments } from "@/utils/storage";
+import { formatCurrency } from "@/utils/formatters";
+import {
+  Bar<PERSON><PERSON>,
+  Bar,
+  XAxis,
+  YAxis,
+  Tooltip,
+  ResponsiveContainer,
+  CartesianGrid,
+} from "recharts";
 
 interface DashboardProps {
   onNavigateToFacilityForm: () => void;
   onNavigateToPaymentForm: () => void;
   onNavigateToFacilityList: () => void;
-  onNavigateToTestData: () => void;
+  onNavigateToPaymentList: () => void;
   onNavigateToPaymentAllocation: () => void;
 }
 
-const Dashboard = ({ 
-  onNavigateToFacilityForm, 
-  onNavigateToPaymentForm, 
+const Dashboard = ({
+  onNavigateToFacilityForm,
+  onNavigateToPaymentForm,
   onNavigateToFacilityList,
-  onNavigateToTestData,
-  onNavigateToPaymentAllocation
+  onNavigateToPaymentList,
+  onNavigateToPaymentAllocation,
 }: DashboardProps) => {
+  const [facilityCount, setFacilityCount] = useState(0);
+  const [paymentSum, setPaymentSum] = useState(0);
+  const [interestPenaltySum, setInterestPenaltySum] = useState(0);
+  const [chartData, setChartData] = useState<{ month: number; count: number }[]>([]);
+
+  useEffect(() => {
+    const facilities = getFacilities();
+    const payments = getPayments();
+    setFacilityCount(facilities.length);
+    setPaymentSum(payments.reduce((a, p) => a + (p.totalAmount || 0), 0));
+    setInterestPenaltySum(
+      payments.reduce((a, p) => a + (p.interestAmount || 0) + (p.penaltyAmount || 0), 0),
+    );
+
+    const monthCounts: Record<number, number> = {};
+    const year = new Date().getFullYear();
+    facilities.forEach((f) => {
+      (f.installments || []).forEach((inst) => {
+        const [y, m] = inst.dueDate.split('/').map(Number);
+        if (y === year) {
+          monthCounts[m] = (monthCounts[m] || 0) + 1;
+        }
+      });
+    });
+    const data = Object.entries(monthCounts).map(([m, c]) => ({
+      month: Number(m),
+      count: c,
+    }));
+    setChartData(data);
+  }, []);
   return (
     <div className="space-y-8">
       <div className="text-center">
         <h1 className="text-4xl font-bold text-gray-800 mb-4">
           سیستم مدیریت تسهیلات بانکی
         </h1>
         <p className="text-lg text-gray-600 max-w-2xl mx-auto">
           مدیریت ساده و کارآمد تسهیلات بانکی و پرداخت‌ها
         </p>
       </div>
 
+      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>تعداد تسهیلات</CardTitle>
+          </CardHeader>
+          <CardContent className="text-2xl font-bold text-blue-600">
+            {facilityCount}
+          </CardContent>
+        </Card>
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>مجموع پرداختی</CardTitle>
+          </CardHeader>
+          <CardContent className="text-2xl font-bold text-green-600">
+            {formatCurrency(paymentSum)} ریال
+          </CardContent>
+        </Card>
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>سود و جریمه پرداختی</CardTitle>
+          </CardHeader>
+          <CardContent className="text-2xl font-bold text-orange-600">
+            {formatCurrency(interestPenaltySum)} ریال
+          </CardContent>
+        </Card>
+      </div>
+
+      {chartData.length > 0 ? (
+        <Card className="bg-white shadow">
+          <CardHeader>
+            <CardTitle>تعداد اقساط سررسید هر ماه</CardTitle>
+          </CardHeader>
+          <CardContent style={{ height: 300 }}>
+            <ResponsiveContainer width="100%" height="100%">
+              <BarChart data={chartData} layout="vertical">
+                <CartesianGrid strokeDasharray="3 3" />
+                <XAxis type="number" allowDecimals={false} />
+                <YAxis dataKey="month" type="category" />
+                <Tooltip />
+                <Bar dataKey="count" fill="#3b82f6" />
+              </BarChart>
+            </ResponsiveContainer>
+          </CardContent>
+        </Card>
+      ) : (
+        <Card className="bg-white shadow">
+          <CardContent className="p-6 text-center text-gray-500">
+            هیچ داده‌ای ثبت نشده
+          </CardContent>
+        </Card>
+      )}
+
       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
         <Card 
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
           onClick={onNavigateToFacilityForm}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
             <CardTitle className="text-lg font-semibold">ثبت تسهیلات</CardTitle>
             <FileText className="h-5 w-5 text-blue-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
               ثبت اطلاعات تسهیلات جدید
             </CardDescription>
           </CardContent>
         </Card>
 
         <Card 
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
           onClick={onNavigateToPaymentForm}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
             <CardTitle className="text-lg font-semibold">ثبت پرداخت</CardTitle>
             <Calculator className="h-5 w-5 text-green-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
               ثبت پرداخت اقساط
             </CardDescription>
           </CardContent>
         </Card>
 
-        <Card 
+        <Card
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
           onClick={onNavigateToFacilityList}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
             <CardTitle className="text-lg font-semibold">لیست تسهیلات</CardTitle>
             <List className="h-5 w-5 text-purple-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
               مشاهده تمام تسهیلات و پرداخت‌ها
             </CardDescription>
           </CardContent>
         </Card>
 
-        <Card 
+        <Card
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
-          onClick={onNavigateToPaymentAllocation}
+          onClick={onNavigateToPaymentList}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
-            <CardTitle className="text-lg font-semibold">گزارش تخصیص</CardTitle>
-            <TrendingUp className="h-5 w-5 text-orange-600 mr-auto" />
+            <CardTitle className="text-lg font-semibold">لیست پرداخت‌ها</CardTitle>
+            <List className="h-5 w-5 text-purple-600 mr-auto" />
           </CardHeader>
           <CardContent>
-            <CardDescription>
-              مقایسه روش‌های تخصیص پرداخت
-            </CardDescription>
+            <CardDescription>مشاهده تمام پرداخت‌ها</CardDescription>
           </CardContent>
         </Card>
 
         <Card 
           className="cursor-pointer hover:shadow-lg transition-shadow bg-white"
-          onClick={onNavigateToTestData}
+          onClick={onNavigateToPaymentAllocation}
         >
           <CardHeader className="flex flex-row items-center space-y-0 pb-2">
-            <CardTitle className="text-lg font-semibold">داده‌های آزمایشی</CardTitle>
-            <Database className="h-5 w-5 text-teal-600 mr-auto" />
+            <CardTitle className="text-lg font-semibold">گزارش تخصیص</CardTitle>
+            <TrendingUp className="h-5 w-5 text-orange-600 mr-auto" />
           </CardHeader>
           <CardContent>
             <CardDescription>
-              بارگذاری داده‌های نمونه
+              مقایسه روش‌های تخصیص پرداخت
             </CardDescription>
           </CardContent>
         </Card>
+
       </div>
     </div>
   );
 };
 
 export default Dashboard;
