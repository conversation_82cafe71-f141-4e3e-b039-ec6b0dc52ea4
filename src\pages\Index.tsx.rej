diff a/src/pages/Index.tsx b/src/pages/Index.tsx	(rejected hunks)
@@ -1,55 +1,65 @@
 
 import { useState } from 'react';
 import Dashboard from '@/components/Dashboard';
 import SimpleFacilityForm from '@/components/facility/SimpleFacilityForm';
 import SimplePaymentForm from '@/components/payment/SimplePaymentForm';
 import SimpleFacilityList from '@/components/lists/SimpleFacilityList';
-import TestDataManager from '@/components/TestDataManager';
+import SimplePaymentList from '@/components/lists/SimplePaymentList';
 import PaymentAllocationReport from '@/components/PaymentAllocationReport';
 
-type ViewType = 'dashboard' | 'facility-form' | 'payment-form' | 'facility-list' | 'test-data' | 'payment-allocation';
+type ViewType =
+  | 'dashboard'
+  | 'facility-form'
+  | 'payment-form'
+  | 'facility-list'
+  | 'payment-list'
+  | 'payment-allocation';
 
 const Index = () => {
   const [currentView, setCurrentView] = useState<ViewType>('dashboard');
 
   const renderCurrentView = () => {
     switch (currentView) {
       case 'facility-form':
         return <SimpleFacilityForm onBack={() => setCurrentView('dashboard')} />;
       case 'payment-form':
         return <SimplePaymentForm onBack={() => setCurrentView('dashboard')} />;
       case 'facility-list':
         return (
-          <SimpleFacilityList 
+          <SimpleFacilityList
             onBack={() => setCurrentView('dashboard')}
             onAddFacility={() => setCurrentView('facility-form')}
             onAddPayment={() => setCurrentView('payment-form')}
           />
         );
-      case 'test-data':
-        return <TestDataManager onBack={() => setCurrentView('dashboard')} />;
+      case 'payment-list':
+        return (
+          <SimplePaymentList
+            onBack={() => setCurrentView('dashboard')}
+          />
+        );
       case 'payment-allocation':
         return <PaymentAllocationReport onBack={() => setCurrentView('dashboard')} />;
       default:
         return (
-          <Dashboard 
+          <Dashboard
             onNavigateToFacilityForm={() => setCurrentView('facility-form')}
             onNavigateToPaymentForm={() => setCurrentView('payment-form')}
             onNavigateToFacilityList={() => setCurrentView('facility-list')}
-            onNavigateToTestData={() => setCurrentView('test-data')}
+            onNavigateToPaymentList={() => setCurrentView('payment-list')}
             onNavigateToPaymentAllocation={() => setCurrentView('payment-allocation')}
           />
         );
     }
   };
 
   return (
     <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
       <div className="container mx-auto px-4 py-8">
         {renderCurrentView()}
       </div>
     </div>
   );
 };
 
 export default Index;
