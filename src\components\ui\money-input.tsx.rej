diff a/src/components/ui/money-input.tsx b/src/components/ui/money-input.tsx	(rejected hunks)
@@ -1,53 +1,55 @@
 
 import React from 'react';
 import { Input } from './input';
 import { formatMoney, parseMoney } from '@/utils/formatMoney';
+import { toEnglishDigits } from '@/utils/formatters';
 
 interface MoneyInputProps {
   value: string;
   onChange: (value: string) => void;
   placeholder?: string;
   className?: string;
   id?: string;
 }
 
 const MoneyInput = ({ value, onChange, placeholder, className, id }: MoneyInputProps) => {
   // نمایش فرمت شده مقدار
   const displayValue = value ? formatMoney(value) : '';
 
   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
     const inputValue = e.target.value;
     
     // اگر کاربر همه چیز را پاک کرد
     if (inputValue === '') {
       onChange('');
       return;
     }
     
-    // استخراج فقط اعداد از ورودی
-    const numbersOnly = inputValue.replace(/[^\d]/g, '');
+    // نرمال‌سازی ارقام فارسی و استخراج اعداد
+    const normalized = toEnglishDigits(inputValue);
+    const numbersOnly = normalized.replace(/[^0-9]/g, '');
     
     // ارسال مقدار پاک شده (فقط اعداد)
     onChange(numbersOnly);
   };
 
   const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
     // قرار دادن کرسور در انتها
     const target = e.target;
     setTimeout(() => {
       const length = target.value.length;
       target.setSelectionRange(length, length);
     }, 0);
   };
 
   return (
     <Input
       id={id}
       type="text"
       value={displayValue}
       onChange={handleChange}
       onFocus={handleFocus}
       placeholder={placeholder}
       className={className}
       inputMode="numeric"
     />
