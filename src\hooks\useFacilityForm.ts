
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Installment, FacilityData } from "@/types/facility";

export const useFacilityForm = (onBack: () => void) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FacilityData>({
    facilityType: "",
    amount: "",
    receivedDate: "",
    installmentCount: "",
    interestRate: "",
    penaltyRate: "",
    bankName: "",
    facilityNumber: "",
    installmentAmount: "",
    firstInstallmentDate: "",
  });

  const [installments, setInstallments] = useState<Installment[]>([]);
  const [showInstallments, setShowInstallments] = useState(false);

  const handleUpdateFormData = (data: Partial<FacilityData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const handleAddInstallments = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    
    const count = parseInt(formData.installmentCount);
    if (count > 0) {
      const newInstallments: Installment[] = [];
      for (let i = 1; i <= count; i++) {
        newInstallments.push({
          id: i,
          dueDate: "",
          principalAmount: "",
          interestAmount: "",
        });
      }
      setInstallments(newInstallments);
      setShowInstallments(true);
    }
  };

  const updateInstallment = (id: number, field: keyof Installment, value: any) => {
    setInstallments(prev => prev.map(installment => 
      installment.id === id ? { ...installment, [field]: value } : installment
    ));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const requiredFields = ['facilityType', 'amount', 'receivedDate', 'installmentCount', 'interestRate', 'bankName', 'facilityNumber'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);
    
    if (missingFields.length > 0) {
      toast({
        title: "خطا",
        description: "لطفا تمام فیلدهای ضروری را پر کنید",
        variant: "destructive",
      });
      return;
    }

    try {
      const facilities = JSON.parse(localStorage.getItem('facilities') || '[]');
      const newFacility = {
        id: Date.now(),
        ...formData,
        installments: installments,
        createdAt: new Date(),
      };
      facilities.push(newFacility);
      localStorage.setItem('facilities', JSON.stringify(facilities));

      toast({
        title: "موفقیت",
        description: "تسهیلات با موفقیت ثبت شد",
      });

      onBack();
    } catch (error) {
      console.error('Error saving facility:', error);
      toast({
        title: "خطا",
        description: "خطا در ذخیره تسهیلات",
        variant: "destructive",
      });
    }
  };

  const handleCancel = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    onBack();
  };

  return {
    formData,
    installments,
    showInstallments,
    handleUpdateFormData,
    handleAddInstallments,
    updateInstallment,
    handleSubmit,
    handleCancel,
  };
};
