import { Facility, Payment } from "@/types/types";
import { Installment } from "@/types/facility";
import { persianToDate } from "./jalali";

// تبدیل تاریخ شمسی به تعداد روز
export const persianDateToDays = (date: string): number => {
  const [year, month, day] = date.split('/').map(Number);
  const dateObj = persianToDate(year, month, day);
  return Math.floor(dateObj.getTime() / (24 * 60 * 60 * 1000));
};

// محاسبه تعداد روزهای تاخیر یا تعجیل
export const calculateDelayDays = (dueDate: string, paymentDate: string): number => {
  const dueDays = persianDateToDays(dueDate);
  const paymentDays = persianDateToDays(paymentDate);
  return paymentDays - dueDays; // مثبت = تاخیر، منفی = تعجیل
};

// محاسبه سود روزانه
export const calculateDailyInterest = (
  principalAmount: number,
  interestRate: number
): number => {
  // نرخ سود سالانه به روزانه
  const dailyRate = interestRate / 36500; // تقسیم بر 365 روز و 100 (درصد)
  return principalAmount * dailyRate;
};

// محاسبه جریمه تاخیر
export const calculatePenalty = (
  principalAmount: number,
  penaltyRate: number,
  delayDays: number
): number => {
  if (delayDays <= 0) return 0;
  // نرخ جریمه سالانه به روزانه
  const dailyRate = penaltyRate / 36500; // تقسیم بر 365 روز و 100 (درصد)
  return principalAmount * dailyRate * delayDays;
};

// محاسبه تخصیص پرداخت به روش اول (اولویت: سود، جریمه، اصل)
export const calculateMethod1Allocation = (
  payment: Payment,
  installment: Installment,
  facility: Facility,
  delayDays: number
): {
  principal: number;
  interest: number;
  penalty: number;
  standardPrincipal: number;
  standardInterest: number;
  standardPenalty: number;
  diffPrincipal: number;
  diffInterest: number;
  diffPenalty: number;
} => {
  // مقادیر واقعی پرداخت شده
  const actualPrincipal = payment.principalAmount || 0;
  const actualInterest = payment.interestAmount || 0;
  const actualPenalty = payment.penaltyAmount || 0;
  
  // محاسبه مقادیر استاندارد
  const principalAmount = parseInt(installment.principalAmount) || 0;
  const interestAmount = parseInt(installment.interestAmount) || 0;
  
  // محاسبه جریمه استاندارد
  const standardPenalty = calculatePenalty(
    principalAmount,
    facility.penaltyRate,
    delayDays
  );
  
  // کل مبلغ پرداختی
  const totalPayment = payment.totalAmount;
  
  // تخصیص به روش اول (اولویت: سود، جریمه، اصل)
  let remaining = totalPayment;
  
  // ابتدا سود
  const standardInterest = interestAmount;
  const allocatedInterest = Math.min(remaining, standardInterest);
  remaining -= allocatedInterest;
  
  // سپس جریمه
  const allocatedPenalty = Math.min(remaining, standardPenalty);
  remaining -= allocatedPenalty;
  
  // در نهایت اصل
  const standardPrincipal = principalAmount;
  const allocatedPrincipal = Math.min(remaining, standardPrincipal);
  
  // محاسبه انحراف
  const diffPrincipal = actualPrincipal - allocatedPrincipal;
  const diffInterest = actualInterest - allocatedInterest;
  const diffPenalty = actualPenalty - allocatedPenalty;
  
  return {
    principal: allocatedPrincipal,
    interest: allocatedInterest,
    penalty: allocatedPenalty,
    standardPrincipal,
    standardInterest,
    standardPenalty,
    diffPrincipal,
    diffInterest,
    diffPenalty
  };
};

// محاسبه مجموع انحراف
export const calculateTotalDeviation = (
  diffPrincipal: number,
  diffInterest: number,
  diffPenalty: number
): number => {
  return Math.abs(diffPrincipal) + Math.abs(diffInterest) + Math.abs(diffPenalty);
};