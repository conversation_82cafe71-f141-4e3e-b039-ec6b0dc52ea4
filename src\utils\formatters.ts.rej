diff a/src/utils/formatters.ts b/src/utils/formatters.ts	(rejected hunks)
@@ -1,20 +1,29 @@
 
+export const toEnglishDigits = (value: string): string => {
+  const persianDigits = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
+  return persianDigits.reduce(
+    (acc, d, i) => acc.replace(new RegExp(d, 'g'), i.toString()),
+    value
+  );
+};
+
 export const formatCurrency = (amount: number | string): string => {
-  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
+  const str = typeof amount === 'string' ? toEnglishDigits(amount) : String(amount);
+  const num = parseFloat(str);
   if (isNaN(num)) return '0';
   return new Intl.NumberFormat('fa-IR').format(num);
 };
 
 export const formatDate = (date: string | Date): string => {
   try {
     const dateObj = typeof date === 'string' ? new Date(date) : date;
     return new Intl.DateTimeFormat('fa-IR').format(dateObj);
   } catch {
     return date.toString();
   }
 };
 
 export const parseCurrency = (value: string): number => {
-  const cleaned = value.replace(/[^\d]/g, '');
+  const cleaned = toEnglishDigits(value).replace(/[^0-9]/g, '');
   return parseInt(cleaned) || 0;
 };
