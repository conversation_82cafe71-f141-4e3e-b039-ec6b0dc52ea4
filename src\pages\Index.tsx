import { useState } from 'react';
import Dashboard from '@/components/Dashboard';
import SimpleFacilityForm from '@/components/facility/SimpleFacilityForm';
import SimplePaymentForm from '@/components/payment/SimplePaymentForm';
import SimpleFacilityList from '@/components/lists/SimpleFacilityList';
import SimplePaymentList from '@/components/lists/SimplePaymentList';
import ControlMethod1 from '@/components/controls/ControlMethod1';
import ControlMethod2 from '@/components/controls/ControlMethod2';
import ControlMethod3 from '@/components/controls/ControlMethod3';
import InstallmentList from '@/components/lists/InstallmentList';
import SidebarMenu from '@/components/SidebarMenu';

type ViewType =
  | 'dashboard'
  | 'facility-form'
  | 'payment-form'
  | 'facility-list'
  | 'payment-list'
  | 'installment-list'
  | 'control-method-1'
  | 'control-method-2'
  | 'control-method-3';

const SIDEBAR_WIDTH = 'w-48'; // 12rem ~ 192px (Tailwind w-48)

const Index = () => {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard');
  const [refreshTrigger, setRefreshTrigger] = useState(0); // تریگر برای به‌روزرسانی داده‌ها

  const renderCurrentView = () => {
    switch (currentView) {
      case 'facility-form':
        return <SimpleFacilityForm onBack={() => setCurrentView('dashboard')} />;
      case 'payment-form':
        return <SimplePaymentForm onBack={() => setCurrentView('dashboard')} />;
      case 'facility-list':
        return (
          <SimpleFacilityList
            onBack={() => setCurrentView('dashboard')}
            onAddFacility={() => setCurrentView('facility-form')}
            onAddPayment={() => setCurrentView('payment-form')}
          />
        );
      case 'payment-list':
        return (
          <SimplePaymentList
            onBack={() => setCurrentView('dashboard')}
            onPaymentUpdated={() => setRefreshTrigger(prev => prev + 1)} // افزودن تابع برای به‌روزرسانی
          />
        );
      case 'installment-list':
        return <InstallmentList onBack={() => setCurrentView('dashboard')} />;
      case 'control-method-1':
        return <ControlMethod1 onBack={() => setCurrentView('dashboard')} refreshTrigger={refreshTrigger} />;
      case 'control-method-2':
        return <ControlMethod2 onBack={() => setCurrentView('dashboard')} />;
      case 'control-method-3':
        return <ControlMethod3 onBack={() => setCurrentView('dashboard')} />;
      default:
        return (
          <Dashboard
            onNavigateToFacilityForm={() => setCurrentView('facility-form')}
            onNavigateToPaymentForm={() => setCurrentView('payment-form')}
            onNavigateToFacilityList={() => setCurrentView('facility-list')}
            onNavigateToPaymentList={() => setCurrentView('payment-list')}
            onNavigateToInstallmentList={() => setCurrentView('installment-list')}
          />
        );
    }
  };

  const containerClass =
    currentView === 'facility-form'
      ? 'w-full px-4 py-8'
      : 'container mx-auto px-4 py-8';

  // تابع خروج از سیستم
  const handleLogout = () => {
    // در این نسخه فقط به صفحه اصلی برمی‌گردیم
    setCurrentView('dashboard');
    // می‌توان در آینده منطق خروج از سیستم را اینجا اضافه کرد
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* بخش اصلی با margin مناسب از سمت راست به اندازه سایدوار */}
      <div className={`flex-1 mr-48 relative`}>
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/30 pointer-events-none"></div>
        <div className="relative z-10">
          <div className={containerClass}>{renderCurrentView()}</div>
        </div>
      </div>

      {/* استفاده از کامپوننت SidebarMenu برای نمایش منوها */}
      <SidebarMenu onNavigate={(view) => setCurrentView(view as ViewType)} />

      {/* Enhanced Logout Button */}
      <div className="fixed bottom-6 right-6 w-36 z-50">
        <button
          onClick={handleLogout}
          className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white p-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 font-medium"
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          خروج
        </button>
      </div>
    </div>
  );
};

export default Index;