diff a/src/components/facility/SimpleFacilityForm.tsx b/src/components/facility/SimpleFacilityForm.tsx	(rejected hunks)
@@ -1,190 +1,367 @@
-
 import { useState } from "react";
 import { <PERSON>, <PERSON>Content, <PERSON>Header, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
 import { But<PERSON> } from "@/components/ui/button";
 import { useToast } from "@/hooks/use-toast";
-import SimpleSelect from "@/components/common/SimpleSelect";
+import SearchableSelect from "@/components/common/SearchableSelect";
+import ManageListDialog from "@/components/common/ManageListDialog";
 import SimpleInput from "@/components/common/SimpleInput";
+import { Input } from "@/components/ui/input";
 import CurrencyInput from "@/components/common/CurrencyInput";
-import { FACILITY_TYPES, BANK_NAMES } from "@/utils/constants";
-import { addFacility } from "@/utils/storage";
-import { FacilityFormData } from "@/types/types";
+import InstallmentTable from "./InstallmentTable";
+import { Installment } from "@/types/facility";
+import {
+  addFacility,
+  updateFacility,
+  getBankNames,
+  saveBankNames,
+  getFacilityTypes,
+  saveFacilityTypes,
+} from "@/utils/storage";
+import { FacilityFormData, Facility } from "@/types/types";
 
 interface SimpleFacilityFormProps {
   onBack: () => void;
   onSuccess?: () => void;
+  facility?: Facility;
 }
 
-const SimpleFacilityForm = ({ onBack, onSuccess }: SimpleFacilityFormProps) => {
+const SimpleFacilityForm = ({
+  onBack,
+  onSuccess,
+  facility,
+}: SimpleFacilityFormProps) => {
   const { toast } = useToast();
-  const [formData, setFormData] = useState<FacilityFormData>({
-    type: "",
-    amount: "",
-    bankName: "",
-    contractNumber: "",
-    installmentCount: "",
-    interestRate: "",
-    penaltyRate: "",
-    installmentAmount: "",
-    receivedDate: "",
-    firstInstallmentDate: ""
-  });
+  const [formData, setFormData] = useState<FacilityFormData>(() => ({
+    type: facility?.type || "",
+    amount: facility?.amount?.toString() || "",
+    bankName: facility?.bankName || "",
+    contractNumber: facility?.contractNumber || "",
+    installmentCount: facility?.installmentCount?.toString() || "",
+    interestRate: facility?.interestRate?.toString() || "",
+    penaltyRate: facility?.penaltyRate?.toString() || "",
+    receivedDate: facility?.receivedDate || "",
+    firstInstallmentDate: facility?.firstInstallmentDate || "",
+  }));
+
+  const [installments, setInstallments] = useState<Installment[]>(
+    () => facility?.installments || [],
+  );
+  const [showInstallments, setShowInstallments] = useState(
+    () => (facility?.installments?.length ?? 0) > 0,
+  );
+  const [bankOptions, setBankOptions] = useState<string[]>(getBankNames());
+  const [typeOptions, setTypeOptions] = useState<string[]>(getFacilityTypes());
+  const [bankDialog, setBankDialog] = useState(false);
+  const [typeDialog, setTypeDialog] = useState(false);
 
   const updateField = (field: keyof FacilityFormData, value: string) => {
-    setFormData(prev => ({ ...prev, [field]: value }));
+    setFormData((prev) => ({ ...prev, [field]: value }));
+  };
+
+  const handleAddInstallments = (e: React.MouseEvent<HTMLButtonElement>) => {
+    e.preventDefault();
+    const count = parseInt(formData.installmentCount);
+    if (count > 0) {
+      const list: Installment[] = Array.from({ length: count }, (_, i) => ({
+        id: i + 1,
+        dueDate: "",
+        principalAmount: "",
+        interestAmount: "",
+      }));
+      setInstallments(list);
+      setShowInstallments(true);
+    }
+  };
+
+  const updateInstallment = (
+    id: number,
+    field: keyof Installment,
+    value: string,
+  ) => {
+    setInstallments((prev) =>
+      prev.map((inst) => (inst.id === id ? { ...inst, [field]: value } : inst)),
+    );
+  };
+
+  const handleBankListChange = (list: string[]) => {
+    setBankOptions(list);
+    saveBankNames(list);
+  };
+
+  const handleTypeListChange = (list: string[]) => {
+    setTypeOptions(list);
+    saveFacilityTypes(list);
   };
 
   const handleSubmit = (e: React.FormEvent) => {
     e.preventDefault();
-    
+
     // بررسی فیلدهای ضروری
-    const requiredFields = ['type', 'amount', 'bankName', 'contractNumber', 'installmentCount', 'interestRate'];
-    const missing = requiredFields.filter(field => !formData[field as keyof FacilityFormData]);
-    
+    const requiredFields = [
+      "type",
+      "amount",
+      "bankName",
+      "contractNumber",
+      "installmentCount",
+      "interestRate",
+    ];
+    const missing = requiredFields.filter(
+      (field) => !formData[field as keyof FacilityFormData],
+    );
+
     if (missing.length > 0) {
       toast({
         title: "خطا",
         description: "لطفاً تمام فیلدهای ضروری را پر کنید",
         variant: "destructive",
       });
       return;
     }
 
-    try {
-      addFacility({
-        type: formData.type,
-        amount: parseInt(formData.amount),
-        bankName: formData.bankName,
-        contractNumber: formData.contractNumber,
-        installmentCount: parseInt(formData.installmentCount),
-        interestRate: parseFloat(formData.interestRate),
-        penaltyRate: parseFloat(formData.penaltyRate || "0"),
-        installmentAmount: parseInt(formData.installmentAmount || "0"),
-        receivedDate: formData.receivedDate,
-        firstInstallmentDate: formData.firstInstallmentDate
-      });
-
+    const totalPrincipal = installments.reduce(
+      (sum, i) => sum + (parseInt(i.principalAmount) || 0),
+      0,
+    );
+    if (showInstallments && totalPrincipal !== parseInt(formData.amount)) {
       toast({
-        title: "موفقیت",
-        description: "تسهیلات با موفقیت ثبت شد",
+        title: "خطا",
+        description: "جمع مبالغ اصل اقساط با مبلغ تسهیلات برابر نیست",
+        variant: "destructive",
       });
+      return;
+    }
+
+    try {
+      if (facility) {
+        updateFacility(facility.id, {
+          type: formData.type,
+          amount: parseInt(formData.amount),
+          bankName: formData.bankName,
+          contractNumber: formData.contractNumber,
+          installmentCount: parseInt(formData.installmentCount),
+          interestRate: parseFloat(formData.interestRate),
+          penaltyRate: parseFloat(formData.penaltyRate || "0"),
+          installmentAmount:
+            parseInt(formData.amount) /
+            parseInt(formData.installmentCount || "1"),
+          receivedDate: formData.receivedDate,
+          firstInstallmentDate: formData.firstInstallmentDate,
+          installments,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "تسهیلات با موفقیت ویرایش شد",
+        });
+      } else {
+        addFacility({
+          type: formData.type,
+          amount: parseInt(formData.amount),
+          bankName: formData.bankName,
+          contractNumber: formData.contractNumber,
+          installmentCount: parseInt(formData.installmentCount),
+          interestRate: parseFloat(formData.interestRate),
+          penaltyRate: parseFloat(formData.penaltyRate || "0"),
+          installmentAmount:
+            parseInt(formData.amount) /
+            parseInt(formData.installmentCount || "1"),
+          receivedDate: formData.receivedDate,
+          firstInstallmentDate: formData.firstInstallmentDate,
+          installments,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "تسهیلات با موفقیت ثبت شد",
+        });
+      }
 
       if (onSuccess) onSuccess();
       else onBack();
     } catch (error) {
       toast({
         title: "خطا",
         description: "خطا در ثبت تسهیلات",
         variant: "destructive",
       });
     }
   };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
-        <h2 className="text-2xl font-bold">ثبت تسهیلات جدید</h2>
+        <h2 className="text-2xl font-bold">
+          {facility ? "ویرایش تسهیلات" : "ثبت تسهیلات جدید"}
+        </h2>
         <Button variant="outline" onClick={onBack}>
           بازگشت
         </Button>
       </div>
 
       <Card>
         <CardHeader>
           <CardTitle>اطلاعات تسهیلات</CardTitle>
         </CardHeader>
         <CardContent>
           <form onSubmit={handleSubmit} className="space-y-6">
-            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
-              <SimpleSelect
-                label="نوع تسهیلات"
-                value={formData.type}
-                onValueChange={(value) => updateField('type', value)}
-                options={FACILITY_TYPES}
-                required
-              />
+            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
+              <div className="flex items-end gap-2">
+                <div className="flex-1">
+                  <SearchableSelect
+                    label="نوع تسهیلات"
+                    value={formData.type}
+                    onValueChange={(value) => updateField("type", value)}
+                    options={typeOptions}
+                    required
+                  />
+                </div>
+                <Button
+                  type="button"
+                  variant="outline"
+                  size="sm"
+                  onClick={() => setTypeDialog(true)}
+                >
+                  افزودن نوع
+                </Button>
+              </div>
 
               <CurrencyInput
                 label="مبلغ تسهیلات (ریال)"
                 value={formData.amount}
-                onChange={(value) => updateField('amount', value)}
+                onChange={(value) => updateField("amount", value)}
                 required
               />
 
-              <SimpleSelect
-                label="نام بانک"
-                value={formData.bankName}
-                onValueChange={(value) => updateField('bankName', value)}
-                options={BANK_NAMES}
-                required
-              />
+              <div className="flex items-end gap-2">
+                <div className="flex-1">
+                  <SearchableSelect
+                    label="نام بانک"
+                    value={formData.bankName}
+                    onValueChange={(value) => updateField("bankName", value)}
+                    options={bankOptions}
+                    required
+                  />
+                </div>
+                <Button
+                  type="button"
+                  variant="outline"
+                  size="sm"
+                  onClick={() => setBankDialog(true)}
+                >
+                  افزودن بانک
+                </Button>
+              </div>
 
               <SimpleInput
                 label="شماره قرارداد"
                 value={formData.contractNumber}
-                onChange={(value) => updateField('contractNumber', value)}
+                onChange={(value) => updateField("contractNumber", value)}
                 required
               />
 
-              <SimpleInput
-                label="تعداد اقساط"
-                value={formData.installmentCount}
-                onChange={(value) => updateField('installmentCount', value)}
-                type="number"
-                required
-              />
+              <div className="space-y-2">
+                <label
+                  htmlFor="installmentCount"
+                  className="text-sm font-medium"
+                >
+                  تعداد اقساط *
+                </label>
+                <div className="flex gap-2">
+                  <Input
+                    id="installmentCount"
+                    type="number"
+                    value={formData.installmentCount}
+                    onChange={(e) =>
+                      updateField("installmentCount", e.target.value)
+                    }
+                    className="flex-1"
+                  />
+                  {formData.installmentCount && (
+                    <Button
+                      type="button"
+                      variant="outline"
+                      onClick={handleAddInstallments}
+                    >
+                      افزودن اقساط
+                    </Button>
+                  )}
+                </div>
+              </div>
 
               <SimpleInput
                 label="نرخ سود (درصد)"
                 value={formData.interestRate}
-                onChange={(value) => updateField('interestRate', value)}
+                onChange={(value) => updateField("interestRate", value)}
                 type="number"
                 step="0.1"
                 required
               />
 
               <SimpleInput
                 label="نرخ جریمه (درصد)"
                 value={formData.penaltyRate}
-                onChange={(value) => updateField('penaltyRate', value)}
+                onChange={(value) => updateField("penaltyRate", value)}
                 type="number"
                 step="0.1"
               />
 
-              <CurrencyInput
-                label="مبلغ هر قسط (ریال)"
-                value={formData.installmentAmount}
-                onChange={(value) => updateField('installmentAmount', value)}
-              />
+              <div className="space-y-2">
+                <label className="text-sm font-medium">تاریخ اخذ</label>
+                <Input
+                  value={formData.receivedDate}
+                  onChange={(e) => updateField("receivedDate", e.target.value)}
+                  placeholder="YYYY/MM/DD"
+                  pattern="\d{4}/\d{2}/\d{2}"
+                />
+              </div>
 
-              <SimpleInput
-                label="تاریخ اخذ"
-                value={formData.receivedDate}
-                onChange={(value) => updateField('receivedDate', value)}
-                type="date"
-              />
+              <div className="space-y-2">
+                <label className="text-sm font-medium">تاریخ اولین قسط</label>
+                <Input
+                  value={formData.firstInstallmentDate}
+                  onChange={(e) =>
+                    updateField("firstInstallmentDate", e.target.value)
+                  }
+                  placeholder="YYYY/MM/DD"
+                  pattern="\d{4}/\d{2}/\d{2}"
+                />
+              </div>
+            </div>
 
-              <SimpleInput
-                label="تاریخ اولین قسط"
-                value={formData.firstInstallmentDate}
-                onChange={(value) => updateField('firstInstallmentDate', value)}
-                type="date"
+            {showInstallments && installments.length > 0 && (
+              <InstallmentTable
+                installments={installments}
+                onUpdateInstallment={updateInstallment}
               />
-            </div>
+            )}
 
             <div className="flex justify-end space-x-2 space-x-reverse">
               <Button type="button" variant="outline" onClick={onBack}>
                 لغو
               </Button>
               <Button type="submit">
-                ثبت تسهیلات
+                {facility ? "ذخیره تغییرات" : "ثبت تسهیلات"}
               </Button>
             </div>
           </form>
         </CardContent>
       </Card>
+      <ManageListDialog
+        open={bankDialog}
+        onOpenChange={setBankDialog}
+        items={bankOptions}
+        onChange={handleBankListChange}
+        title="مدیریت بانک‌ها"
+      />
+      <ManageListDialog
+        open={typeDialog}
+        onOpenChange={setTypeDialog}
+        items={typeOptions}
+        onChange={handleTypeListChange}
+        title="مدیریت نوع تسهیلات"
+      />
     </div>
   );
 };
 
 export default SimpleFacilityForm;
