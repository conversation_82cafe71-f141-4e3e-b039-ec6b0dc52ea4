
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import FacilityFormFields from "./facility/FacilityFormFields";
import InstallmentTable from "./facility/InstallmentTable";
import FacilityFormActions from "./facility/FacilityFormActions";
import FacilityFormHeader from "./facility/FacilityFormHeader";
import { useFacilityForm } from "@/hooks/useFacilityForm";

interface FacilityFormProps {
  onBack: () => void;
}

const FacilityForm = ({ onBack }: FacilityFormProps) => {
  const {
    formData,
    installments,
    showInstallments,
    handleUpdateFormData,
    handleAddInstallments,
    updateInstallment,
    handleSubmit,
    handleCancel,
  } = useFacilityForm(onBack);

  return (
    <div className="space-y-6">
      <FacilityFormHeader onBack={onBack} />

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>اطلاعات تسهیلات</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <FacilityFormFields 
              formData={formData}
              onUpdateFormData={handleUpdateFormData}
              onAddInstallments={handleAddInstallments}
            />

            {showInstallments && installments.length > 0 && (
              <InstallmentTable
                installments={installments}
                onUpdateInstallment={updateInstallment}
              />
            )}

            <FacilityFormActions onCancel={handleCancel} />
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default FacilityForm;
