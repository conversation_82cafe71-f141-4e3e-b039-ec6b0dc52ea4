diff a/src/components/facility/FacilityFormFields.tsx b/src/components/facility/FacilityFormFields.tsx	(rejected hunks)
@@ -24,117 +24,112 @@ const facilityTypes = [
   "تحصیلی",
   "سایر"
 ];
 
 const banks = [
   "بانک ملی ایران",
   "بانک صادرات ایران",
   "بانک تجارت",
   "بانک کشاورزی",
   "بانک صنعت و معدن",
   "بانک رفاه کارگران",
   "بانک توسعه تعاون",
   "بانک پست بانک",
   "بانک دی",
   "بانک پارسیان",
   "بانک پاسارگاد",
   "بانک کارآفرین",
   "سایر"
 ];
 
 const FacilityFormFields = ({ formData, onUpdateFormData, onAddInstallments }: FacilityFormFieldsProps) => {
   // Filter out any empty or invalid facility types and banks
   const validFacilityTypes = facilityTypes.filter(type => type && type.trim() !== "");
   const validBanks = banks.filter(bank => bank && bank.trim() !== "");
 
-  console.log("FacilityFormFields - formData:", formData);
-  console.log("FacilityFormFields - validFacilityTypes:", validFacilityTypes);
-  console.log("FacilityFormFields - validBanks:", validBanks);
 
   return (
     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
       {/* نوع تسهیلات */}
       <div className="space-y-2">
         <Label htmlFor="facilityType">نوع تسهیلات *</Label>
         <Select 
           value={formData.facilityType || ""} 
           onValueChange={(value) => onUpdateFormData({facilityType: value})}
         >
           <SelectTrigger>
             <SelectValue placeholder="انتخاب کنید" />
           </SelectTrigger>
           <SelectContent>
             {validFacilityTypes.map((type, index) => {
               const key = `facility_type_${index}`;
-              console.log("Rendering facility type SelectItem:", { key, value: type });
               return (
                 <SelectItem key={key} value={type}>
                   {type}
                 </SelectItem>
               );
             })}
           </SelectContent>
         </Select>
       </div>
 
       {/* مبلغ تسهیلات با فرمت مبلغ */}
       <div className="space-y-2">
         <Label htmlFor="amount">مبلغ تسهیلات (ریال) *</Label>
         <MoneyInput
           id="amount"
           value={formData.amount}
           onChange={(value) => onUpdateFormData({amount: value})}
           placeholder="مثال: 500000000"
         />
       </div>
 
       {/* تاریخ اخذ */}
       <div className="space-y-2">
         <Label htmlFor="receivedDate">تاریخ اخذ *</Label>
         <Input
           id="receivedDate"
           type="text"
           placeholder="yyyy/mm/dd"
           value={formData.receivedDate}
           onChange={(e) => onUpdateFormData({receivedDate: e.target.value})}
         />
       </div>
 
       {/* نام بانک */}
       <div className="space-y-2">
         <Label htmlFor="bankName">نام بانک *</Label>
         <Select 
           value={formData.bankName || ""} 
           onValueChange={(value) => onUpdateFormData({bankName: value})}
         >
           <SelectTrigger>
             <SelectValue placeholder="انتخاب کنید" />
           </SelectTrigger>
           <SelectContent>
             {validBanks.map((bank, index) => {
               const key = `bank_${index}`;
-              console.log("Rendering bank SelectItem:", { key, value: bank });
               return (
                 <SelectItem key={key} value={bank}>
                   {bank}
                 </SelectItem>
               );
             })}
           </SelectContent>
         </Select>
       </div>
 
       {/* شماره تسهیلات */}
       <div className="space-y-2">
         <Label htmlFor="facilityNumber">شماره تسهیلات *</Label>
         <Input
           id="facilityNumber"
           placeholder="شماره قرارداد تسهیلات"
           value={formData.facilityNumber}
           onChange={(e) => onUpdateFormData({facilityNumber: e.target.value})}
         />
       </div>
 
       {/* تعداد اقساط */}
       <div className="space-y-2">
         <Label htmlFor="installmentCount">تعداد اقساط *</Label>
         <div className="flex gap-2">
