diff a/src/components/PaymentForm.tsx b/src/components/PaymentForm.tsx	(rejected hunks)
@@ -19,75 +19,70 @@ interface Facility {
   amount: string;
   bankName: string;
   facilityNumber: string;
   installmentCount: string;
   installmentAmount: string;
   installments?: Installment[];
 }
 
 const PaymentForm = ({ onBack }: PaymentFormProps) => {
   const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
   const [formData, setFormData] = useState({
     facilityId: "",
     installmentNumber: "",
     paymentDate: "",
     principalAmount: "",
     interestAmount: "",
     penaltyAmount: "",
     totalAmount: "",
     notes: "",
   });
 
   useEffect(() => {
     try {
       const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
-      console.log("PaymentForm - loaded facilities:", savedFacilities);
       setFacilities(savedFacilities);
-    } catch (error) {
-      console.error('Error loading facilities:', error);
+    } catch {
       setFacilities([]);
     }
   }, []);
 
   useEffect(() => {
     const principal = parseFloat(formData.principalAmount) || 0;
     const interest = parseFloat(formData.interestAmount) || 0;
     const penalty = parseFloat(formData.penaltyAmount) || 0;
     const total = principal + interest + penalty;
     setFormData(prev => ({ ...prev, totalAmount: total.toString() }));
   }, [formData.principalAmount, formData.interestAmount, formData.penaltyAmount]);
 
   const selectedFacility = facilities.find(f => f.id.toString() === formData.facilityId);
   const availableInstallments = selectedFacility?.installments || [];
   const selectedInstallment = availableInstallments.find(
     inst => inst.id.toString() === formData.installmentNumber
   );
 
-  console.log("PaymentForm - selectedFacility:", selectedFacility);
-  console.log("PaymentForm - availableInstallments:", availableInstallments);
-  console.log("PaymentForm - formData:", formData);
 
   // Filter out any invalid facilities or installments
   const validFacilities = facilities.filter(facility => 
     facility && 
     facility.id !== null && 
     facility.id !== undefined && 
     facility.id.toString().trim() !== ""
   );
 
   const validInstallments = availableInstallments.filter(installment => 
     installment && 
     installment.id !== null && 
     installment.id !== undefined && 
     installment.id.toString().trim() !== ""
   );
 
   const calculateSettlementDays = () => {
     if (!selectedInstallment?.dueDate || !formData.paymentDate) return null;
     
     const dueDate = new Date(selectedInstallment.dueDate);
     const paymentDate = new Date(formData.paymentDate);
     
     const diffTime = paymentDate.getTime() - dueDate.getTime();
     const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
     
@@ -167,51 +162,50 @@ const PaymentForm = ({ onBack }: PaymentFormProps) => {
         </Button>
       </div>
 
       <Card className="bg-white shadow-lg">
         <CardHeader>
           <CardTitle>اطلاعات پرداخت</CardTitle>
         </CardHeader>
         <CardContent>
           <form onSubmit={handleSubmit} className="space-y-6">
             <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
               {/* Facility Selection */}
               <div className="space-y-2 md:col-span-2">
                 <Label htmlFor="facilityId">انتخاب تسهیلات *</Label>
                 {validFacilities.length > 0 ? (
                   <Select 
                     value={formData.facilityId || ""} 
                     onValueChange={(value) => setFormData({...formData, facilityId: value, installmentNumber: ""})}
                   >
                     <SelectTrigger>
                       <SelectValue placeholder="انتخاب کنید" />
                     </SelectTrigger>
                     <SelectContent>
                       {validFacilities.map((facility) => {
                         const key = `facility_${facility.id}`;
                         const value = facility.id.toString();
-                        console.log("Rendering facility SelectItem:", { key, value, facility });
                         return (
                           <SelectItem key={key} value={value}>
                             {`${facility.facilityType} - ${facility.bankName} - ${facility.facilityNumber}`}
                           </SelectItem>
                         );
                       })}
                     </SelectContent>
                   </Select>
                 ) : (
                   <div className="p-3 border rounded-md bg-gray-50 text-gray-500">
                     هیچ تسهیلاتی یافت نشد
                   </div>
                 )}
               </div>
 
               {/* Selected Facility Info */}
               {selectedFacility && (
                 <div className="md:col-span-2 p-4 bg-blue-50 rounded-lg">
                   <h4 className="font-semibold text-blue-800 mb-2">اطلاعات تسهیلات انتخاب شده:</h4>
                   <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                     <div>
                       <span className="text-gray-600">نوع: </span>
                       <span className="font-semibold">{selectedFacility.facilityType}</span>
                     </div>
                     <div>
@@ -223,51 +217,50 @@ const PaymentForm = ({ onBack }: PaymentFormProps) => {
                       <span className="font-semibold">{selectedFacility.facilityNumber}</span>
                     </div>
                     <div>
                       <span className="text-gray-600">مبلغ قسط: </span>
                       <span className="font-semibold">{formatMoney(selectedFacility.installmentAmount)} ریال</span>
                     </div>
                   </div>
                 </div>
               )}
 
               {/* Installment Number Dropdown */}
               <div className="space-y-2">
                 <Label htmlFor="installmentNumber">شماره قسط *</Label>
                 {selectedFacility && validInstallments.length > 0 ? (
                   <Select 
                     value={formData.installmentNumber || ""} 
                     onValueChange={handleInstallmentChange}
                   >
                     <SelectTrigger>
                       <SelectValue placeholder="انتخاب کنید" />
                     </SelectTrigger>
                     <SelectContent>
                       {validInstallments.map((installment) => {
                         const key = `installment_${installment.id}`;
                         const value = installment.id.toString();
-                        console.log("Rendering installment SelectItem:", { key, value, installment });
                         return (
                           <SelectItem key={key} value={value}>
                             {`قسط ${installment.id}`}
                           </SelectItem>
                         );
                       })}
                     </SelectContent>
                   </Select>
                 ) : (
                   <div className="p-3 border rounded-md bg-gray-50 text-gray-500">
                     {!selectedFacility ? "ابتدا تسهیلات را انتخاب کنید" : "هیچ قسطی یافت نشد"}
                   </div>
                 )}
               </div>
 
               {/* Selected Installment Info */}
               {selectedInstallment && (
                 <div className="space-y-2">
                   <Label>اطلاعات قسط انتخاب شده</Label>
                   <div className="p-3 bg-green-50 rounded-lg text-sm space-y-1">
                     <div>
                       <span className="text-gray-600">تاریخ سررسید: </span>
                       <span className="font-semibold">{selectedInstallment.dueDate || 'تعریف نشده'}</span>
                     </div>
                     <div>
