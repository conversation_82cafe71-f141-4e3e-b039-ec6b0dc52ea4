import { useEffect, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { getFacilities, getPayments } from "@/utils/storage";
import { formatCurrency } from "@/utils/formatters";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";

interface DashboardProps {
  onNavigateToFacilityForm: () => void;
  onNavigateToPaymentForm: () => void;
  onNavigateToFacilityList: () => void;
  onNavigateToPaymentList: () => void;
  onNavigateToInstallmentList: () => void;
}

const Dashboard = ({
  onNavigateToFacilityForm,
  onNavigateToPaymentForm,
  onNavigateToFacilityList,
  onNavigateToPaymentList,
  onNavigateToInstallmentList,
}: DashboardProps) => {
  const [facilityCount, setFacilityCount] = useState(0);
  const [paymentSum, setPaymentSum] = useState(0);
  const [interestPenaltySum, setInterestPenaltySum] = useState(0);
  const [paidPrincipal, setPaidPrincipal] = useState(0);
  const [paidInterest, setPaidInterest] = useState(0);
  const [paidPenalty, setPaidPenalty] = useState(0);
  const [remainPrincipal, setRemainPrincipal] = useState(0);
  const [remainInterest, setRemainInterest] = useState(0);
  const [remainPenalty, setRemainPenalty] = useState(0);
  const [totalDebt, setTotalDebt] = useState(0);
  const [chartData, setChartData] = useState<{ month: number; count: number }[]>([]);

  useEffect(() => {
    const facilities = getFacilities();
    const payments = getPayments();
    setFacilityCount(facilities.length);
    setPaymentSum(payments.reduce((a, p) => a + (p.totalAmount || 0), 0));
    setInterestPenaltySum(
      payments.reduce((a, p) => a + (p.interestAmount || 0) + (p.penaltyAmount || 0), 0),
    );

    setPaidPrincipal(payments.reduce((a, p) => a + (p.principalAmount || 0), 0));
    setPaidInterest(payments.reduce((a, p) => a + (p.interestAmount || 0), 0));
    setPaidPenalty(payments.reduce((a, p) => a + (p.penaltyAmount || 0), 0));

    const totalPrincipal = facilities.reduce(
      (s, f) => s + (typeof f.amount === 'string' ? parseInt(f.amount) : f.amount),
      0,
    );
    const totalInterest = facilities.reduce(
      (s, f) =>
        s + (f.installments || []).reduce((si, i) => si + parseInt(i.interestAmount || '0'), 0),
      0,
    );

    const remainP = Math.max(totalPrincipal - payments.reduce((a, p) => a + (p.principalAmount || 0), 0), 0);
    const remainI = Math.max(totalInterest - payments.reduce((a, p) => a + (p.interestAmount || 0), 0), 0);
    setRemainPrincipal(remainP);
    setRemainInterest(remainI);
    setRemainPenalty(0);
    setTotalDebt(remainP + remainI);

    const monthCounts: Record<number, number> = {};
    const year = new Date().getFullYear();
    facilities.forEach((f) => {
      (f.installments || []).forEach((inst) => {
        const [y, m] = inst.dueDate.split('/').map(Number);
        if (y === year) {
          monthCounts[m] = (monthCounts[m] || 0) + 1;
        }
      });
    });
    const data = Object.entries(monthCounts).map(([m, c]) => ({
      month: Number(m),
      count: c,
    }));
    setChartData(data);
  }, []);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          سیستم مدیریت تسهیلات بانکی
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          مدیریت ساده و کارآمد تسهیلات بانکی و پرداخت‌ها
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>تعداد تسهیلات</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-blue-600">
            {facilityCount}
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>مجموع پرداختی</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-green-600">
            {formatCurrency(paymentSum)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>سود و جریمه پرداختی</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-orange-600">
            {formatCurrency(interestPenaltySum)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>اصل پرداختی</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-purple-600">
            {formatCurrency(paidPrincipal)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>سود پرداختی</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-fuchsia-600">
            {formatCurrency(paidInterest)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>جریمه پرداختی</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-red-600">
            {formatCurrency(paidPenalty)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>باقی‌مانده بدهی</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-emerald-600">
            {formatCurrency(totalDebt)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>باقی‌مانده اصل</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-blue-600">
            {formatCurrency(remainPrincipal)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>باقی‌مانده سود</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-orange-600">
            {formatCurrency(remainInterest)} ریال
          </CardContent>
        </Card>
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>باقی‌مانده جریمه</CardTitle>
          </CardHeader>
          <CardContent className="text-2xl font-bold text-red-600">
            {formatCurrency(remainPenalty)} ریال
          </CardContent>
        </Card>
      </div>

      {chartData.length > 0 ? (
        <Card className="bg-white shadow">
          <CardHeader>
            <CardTitle>تعداد اقساط سررسید هر ماه</CardTitle>
          </CardHeader>
          <CardContent style={{ height: 300 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" allowDecimals={false} />
                <YAxis dataKey="month" type="category" />
                <Tooltip />
                <Bar dataKey="count" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      ) : (
        <Card className="bg-white shadow">
          <CardContent className="p-6 text-center text-gray-500">
            هیچ داده‌ای ثبت نشده
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Dashboard;