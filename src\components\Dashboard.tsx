import { useEffect, useState } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { getFacilities, getPayments } from "@/utils/storage";
import { formatCurrency } from "@/utils/formatters";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import {
  Building2,
  CreditCard,
  TrendingUp,
  TrendingDown,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Sparkles,
  BarChart3
} from "lucide-react";

interface DashboardProps {
  onNavigateToFacilityForm: () => void;
  onNavigateToPaymentForm: () => void;
  onNavigateToFacilityList: () => void;
  onNavigateToPaymentList: () => void;
  onNavigateToInstallmentList: () => void;
}

const Dashboard = ({
  onNavigateToFacilityForm,
  onNavigateToPaymentForm,
  onNavigateToFacilityList,
  onNavigateToPaymentList,
  onNavigateToInstallmentList,
}: DashboardProps) => {
  const [facilityCount, setFacilityCount] = useState(0);
  const [paymentSum, setPaymentSum] = useState(0);
  const [interestPenaltySum, setInterestPenaltySum] = useState(0);
  const [paidPrincipal, setPaidPrincipal] = useState(0);
  const [paidInterest, setPaidInterest] = useState(0);
  const [paidPenalty, setPaidPenalty] = useState(0);
  const [remainPrincipal, setRemainPrincipal] = useState(0);
  const [remainInterest, setRemainInterest] = useState(0);
  const [remainPenalty, setRemainPenalty] = useState(0);
  const [totalDebt, setTotalDebt] = useState(0);
  const [chartData, setChartData] = useState<{ month: number; count: number }[]>([]);

  useEffect(() => {
    const facilities = getFacilities();
    const payments = getPayments();
    setFacilityCount(facilities.length);
    setPaymentSum(payments.reduce((a, p) => a + (p.totalAmount || 0), 0));
    setInterestPenaltySum(
      payments.reduce((a, p) => a + (p.interestAmount || 0) + (p.penaltyAmount || 0), 0),
    );

    setPaidPrincipal(payments.reduce((a, p) => a + (p.principalAmount || 0), 0));
    setPaidInterest(payments.reduce((a, p) => a + (p.interestAmount || 0), 0));
    setPaidPenalty(payments.reduce((a, p) => a + (p.penaltyAmount || 0), 0));

    const totalPrincipal = facilities.reduce(
      (s, f) => s + (typeof f.amount === 'string' ? parseInt(f.amount) : f.amount),
      0,
    );
    const totalInterest = facilities.reduce(
      (s, f) =>
        s + (f.installments || []).reduce((si, i) => si + parseInt(i.interestAmount || '0'), 0),
      0,
    );

    const remainP = Math.max(totalPrincipal - payments.reduce((a, p) => a + (p.principalAmount || 0), 0), 0);
    const remainI = Math.max(totalInterest - payments.reduce((a, p) => a + (p.interestAmount || 0), 0), 0);
    setRemainPrincipal(remainP);
    setRemainInterest(remainI);
    setRemainPenalty(0);
    setTotalDebt(remainP + remainI);

    const monthCounts: Record<number, number> = {};
    const year = new Date().getFullYear();
    facilities.forEach((f) => {
      (f.installments || []).forEach((inst) => {
        const [y, m] = inst.dueDate.split('/').map(Number);
        if (y === year) {
          monthCounts[m] = (monthCounts[m] || 0) + 1;
        }
      });
    });
    const data = Object.entries(monthCounts).map(([m, c]) => ({
      month: Number(m),
      count: c,
    }));
    setChartData(data);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6 space-y-8">
      {/* Enhanced Header */}
      <div className="text-center bg-white rounded-3xl shadow-2xl border border-slate-200/60 p-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-indigo-50/50 to-purple-50/50"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-4 rounded-2xl shadow-xl">
              <Building2 className="h-12 w-12 text-white" />
            </div>
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 rounded-2xl shadow-xl">
              <Sparkles className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-slate-800 via-blue-700 to-indigo-700 bg-clip-text text-transparent mb-4">
            سیستم مدیریت تسهیلات بانکی
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            مدیریت ساده و کارآمد تسهیلات بانکی و پرداخت‌ها با امکانات پیشرفته محاسباتی
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <Building2 className="h-5 w-5 text-white" />
              </div>
              تعداد تسهیلات
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-3xl font-bold text-blue-600">{facilityCount}</div>
            <p className="text-sm text-slate-500 mt-1">تسهیلات ثبت شده</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-green-600 to-emerald-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              مجموع پرداختی
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-green-600">{formatCurrency(paymentSum)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-orange-600 to-amber-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              سود و جریمه پرداختی
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-orange-600">{formatCurrency(interestPenaltySum)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-purple-600 to-violet-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <DollarSign className="h-5 w-5 text-white" />
              </div>
              اصل پرداختی
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(paidPrincipal)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-fuchsia-50 to-pink-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-fuchsia-600 to-pink-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              سود پرداختی
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-fuchsia-600">{formatCurrency(paidInterest)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>
        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-red-50 to-rose-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-red-600 to-rose-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              جریمه پرداختی
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-red-600">{formatCurrency(paidPenalty)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-emerald-600 to-teal-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <Clock className="h-5 w-5 text-white" />
              </div>
              باقی‌مانده بدهی
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-emerald-600">{formatCurrency(totalDebt)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-blue-600 to-cyan-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <TrendingDown className="h-5 w-5 text-white" />
              </div>
              باقی‌مانده اصل
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(remainPrincipal)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-yellow-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-orange-600 to-yellow-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <TrendingDown className="h-5 w-5 text-white" />
              </div>
              باقی‌مانده سود
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-orange-600">{formatCurrency(remainInterest)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 hover:shadow-2xl transition-all duration-300 group overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-red-50 to-pink-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-red-600 to-pink-600 p-2 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-200">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              باقی‌مانده جریمه
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-red-600">{formatCurrency(remainPenalty)}</div>
            <p className="text-sm text-slate-500 mt-1">ریال</p>
          </CardContent>
        </Card>
      </div>

      {chartData.length > 0 ? (
        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-b border-slate-200/60">
            <CardTitle className="flex items-center gap-3 text-slate-700">
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-2 rounded-lg shadow-md">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <span className="text-xl">تعداد اقساط سررسید هر ماه</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6" style={{ height: 350 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis type="number" allowDecimals={false} stroke="#64748b" />
                <YAxis dataKey="month" type="category" stroke="#64748b" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '12px',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Bar
                  dataKey="count"
                  fill="url(#colorGradient)"
                  radius={[0, 8, 8, 0]}
                />
                <defs>
                  <linearGradient id="colorGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#3b82f6" />
                    <stop offset="100%" stopColor="#8b5cf6" />
                  </linearGradient>
                </defs>
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      ) : (
        <Card className="bg-white rounded-2xl shadow-xl border border-slate-200/60">
          <CardContent className="p-12 text-center">
            <div className="bg-gradient-to-r from-slate-100 to-slate-200 p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
              <BarChart3 className="h-12 w-12 text-slate-400" />
            </div>
            <h3 className="text-xl font-semibold text-slate-700 mb-2">هیچ داده‌ای یافت نشد</h3>
            <p className="text-slate-500">هیچ داده‌ای برای نمایش نمودار ثبت نشده است</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Dashboard;