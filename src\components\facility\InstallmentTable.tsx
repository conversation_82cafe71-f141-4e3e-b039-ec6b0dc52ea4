import MoneyInput from "@/components/ui/money-input";
import { Input } from "@/components/ui/input";
import PersianDatePicker from "@/components/common/PersianDatePicker";

interface Installment {
  id: number;
  dueDate: string;
  principalAmount: string;
  interestAmount: string;
}

interface InstallmentTableProps {
  installments: Installment[];
  onUpdateInstallment: (id: number, field: keyof Installment, value: any) => void;
}

const InstallmentTable = ({ installments, onUpdateInstallment }: InstallmentTableProps) => {
  return (
    <div className="mt-8">
      <h3 className="text-lg font-semibold mb-4">جزئیات اقساط</h3>
      <div className="border rounded-lg overflow-visible">
        <div className="overflow-visible">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">شماره قسط</th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">تاریخ سررسید</th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">مبلغ اصل (ریال)</th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">مبلغ سود (ریال)</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {installments.map((installment) => (
                <tr key={installment.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm text-gray-900">{installment.id}</td>
                  <td className="px-4 py-3">
                    <PersianDatePicker
                      label=""
                      value={installment.dueDate}
                      onChange={(value) => onUpdateInstallment(installment.id, 'dueDate', value)}
                    />
                  </td>
                  <td className="px-4 py-3">
                    <MoneyInput
                      value={installment.principalAmount || ''}
                      onChange={(value) => onUpdateInstallment(installment.id, 'principalAmount', value)}
                      placeholder="مبلغ اصل"
                      className="w-full"
                    />
                  </td>
                  <td className="px-4 py-3">
                    <MoneyInput
                      value={installment.interestAmount || ''}
                      onChange={(value) => onUpdateInstallment(installment.id, 'interestAmount', value)}
                      placeholder="مبلغ سود"
                      className="w-full"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default InstallmentTable;
