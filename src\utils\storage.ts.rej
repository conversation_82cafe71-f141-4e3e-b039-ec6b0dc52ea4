diff a/src/utils/storage.ts b/src/utils/storage.ts	(rejected hunks)
@@ -1,57 +1,136 @@
-
-import { Facility, Payment } from '@/types/types';
+import { Facility, Payment } from "@/types/types";
+import { DEFAULT_BANK_NAMES, DEFAULT_FACILITY_TYPES } from "./constants";
 
 export const storageKeys = {
-  facilities: 'facilities',
-  payments: 'payments'
+  facilities: "facilities",
+  payments: "payments",
+  bankNames: "bankNames",
+  facilityTypes: "facilityTypes",
 } as const;
 
 export const getFacilities = (): Facility[] => {
   try {
     const data = localStorage.getItem(storageKeys.facilities);
     return data ? JSON.parse(data) : [];
   } catch {
     return [];
   }
 };
 
 export const saveFacilities = (facilities: Facility[]): void => {
   localStorage.setItem(storageKeys.facilities, JSON.stringify(facilities));
 };
 
 export const getPayments = (): Payment[] => {
   try {
     const data = localStorage.getItem(storageKeys.payments);
     return data ? JSON.parse(data) : [];
   } catch {
     return [];
   }
 };
 
 export const savePayments = (payments: Payment[]): void => {
   localStorage.setItem(storageKeys.payments, JSON.stringify(payments));
 };
 
-export const addFacility = (facility: Omit<Facility, 'id' | 'createdAt'>): Facility => {
+export const getBankNames = (): string[] => {
+  try {
+    const data = localStorage.getItem(storageKeys.bankNames);
+    return data ? JSON.parse(data) : [...DEFAULT_BANK_NAMES];
+  } catch {
+    return [...DEFAULT_BANK_NAMES];
+  }
+};
+
+export const saveBankNames = (names: string[]): void => {
+  localStorage.setItem(storageKeys.bankNames, JSON.stringify(names));
+};
+
+export const getFacilityTypes = (): string[] => {
+  try {
+    const data = localStorage.getItem(storageKeys.facilityTypes);
+    return data ? JSON.parse(data) : [...DEFAULT_FACILITY_TYPES];
+  } catch {
+    return [...DEFAULT_FACILITY_TYPES];
+  }
+};
+
+export const saveFacilityTypes = (types: string[]): void => {
+  localStorage.setItem(storageKeys.facilityTypes, JSON.stringify(types));
+};
+
+export const addFacility = (
+  facility: Omit<Facility, "id" | "createdAt">,
+): Facility => {
   const facilities = getFacilities();
   const newFacility: Facility = {
     ...facility,
     id: Date.now(),
-    createdAt: new Date().toISOString()
+    createdAt: new Date().toISOString(),
+    installments: facility.installments || [],
   };
   facilities.push(newFacility);
   saveFacilities(facilities);
   return newFacility;
 };
 
-export const addPayment = (payment: Omit<Payment, 'id' | 'createdAt'>): Payment => {
+export const addPayment = (
+  payment: Omit<Payment, "id" | "createdAt">,
+): Payment => {
   const payments = getPayments();
   const newPayment: Payment = {
     ...payment,
     id: Date.now(),
-    createdAt: new Date().toISOString()
+    createdAt: new Date().toISOString(),
   };
   payments.push(newPayment);
   savePayments(payments);
   return newPayment;
 };
+
+export const updateFacility = (
+  id: number,
+  updates: Partial<Omit<Facility, "id" | "createdAt">>,
+): Facility | null => {
+  const facilities = getFacilities();
+  const index = facilities.findIndex((f) => f.id === id);
+  if (index === -1) return null;
+  facilities[index] = {
+    ...facilities[index],
+    ...updates,
+    installments: updates.installments ?? facilities[index].installments,
+  };
+  saveFacilities(facilities);
+  return facilities[index];
+};
+
+export const updatePayment = (
+  id: number,
+  updates: Partial<Omit<Payment, "id" | "createdAt">>,
+): Payment | null => {
+  const payments = getPayments();
+  const index = payments.findIndex((p) => p.id === id);
+  if (index === -1) return null;
+  payments[index] = { ...payments[index], ...updates };
+  savePayments(payments);
+  return payments[index];
+};
+
+export const deleteFacility = (id: number): void => {
+  const facilities = getFacilities();
+  const updated = facilities.filter((f) => f.id !== id);
+  saveFacilities(updated);
+};
+
+export const deletePaymentsByFacility = (facilityId: number): void => {
+  const payments = getPayments();
+  const remaining = payments.filter((p) => p.facilityId !== facilityId);
+  savePayments(remaining);
+};
+
+export const deletePayment = (id: number): void => {
+  const payments = getPayments();
+  const updated = payments.filter((p) => p.id !== id);
+  savePayments(updated);
+};
