import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash } from "lucide-react";

interface ManageListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  items: string[];
  onChange: (items: string[]) => void;
  title: string;
}

const ManageListDialog = ({
  open,
  onOpenChange,
  items,
  onChange,
  title,
}: ManageListDialogProps) => {
  const [newItem, setNewItem] = useState("");
  const [localItems, setLocalItems] = useState<string[]>([]);

  // همگام‌سازی state داخلی با props
  useEffect(() => {
    if (open) {
      setLocalItems([...items]);
    }
  }, [items, open]);

  const handleAdd = () => {
    const value = newItem.trim();
    if (!value) return;
    
    // اضافه کردن آیتم جدید به لیست
    const updatedItems = [...localItems, value];
    setLocalItems(updatedItems);
    onChange(updatedItems);
    setNewItem("");
  };

  const handleUpdate = (index: number, value: string) => {
    const updatedItems = [...localItems];
    updatedItems[index] = value;
    setLocalItems(updatedItems);
    onChange(updatedItems);
  };

  const handleDelete = (index: number) => {
    const updatedItems = [...localItems];
    updatedItems.splice(index, 1);
    setLocalItems(updatedItems);
    onChange(updatedItems);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAdd();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="space-y-4 max-w-sm">
        <DialogTitle>{title}</DialogTitle>
        
        <div className="max-h-[60vh] overflow-y-auto space-y-4 py-2">
          {localItems.map((item, idx) => (
            <div key={idx} className="flex gap-2 items-center">
              <Input
                value={item}
                onChange={(e) => handleUpdate(idx, e.target.value)}
                className="flex-1"
              />
              <Button
                size="icon"
                variant="outline"
                onClick={() => handleDelete(idx)}
                type="button"
              >
                <Trash className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
        
        <div className="flex gap-2">
          <Input
            placeholder="افزودن مورد جدید"
            value={newItem}
            onChange={(e) => setNewItem(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1"
          />
          <Button onClick={handleAdd} type="button">
            افزودن
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ManageListDialog;