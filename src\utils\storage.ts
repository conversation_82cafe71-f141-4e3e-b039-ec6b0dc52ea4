import { Facility, Payment } from "@/types/types";
import { DEFAULT_BANK_NAMES, DEFAULT_FACILITY_TYPES } from "./constants";

export const storageKeys = {
  facilities: "facilities",
  payments: "payments",
  bankNames: "bankNames",
  facilityTypes: "facilityTypes",
} as const;

export const getFacilities = (): Facility[] => {
  try {
    const data = localStorage.getItem(storageKeys.facilities);
    return data ? JSON.parse(data) : [];
  } catch {
    return [];
  }
};

export const saveFacilities = (facilities: Facility[]): void => {
  localStorage.setItem(storageKeys.facilities, JSON.stringify(facilities));
};

export const getPayments = (): Payment[] => {
  try {
    const data = localStorage.getItem(storageKeys.payments);
    return data ? JSON.parse(data) : [];
  } catch {
    return [];
  }
};

export const savePayments = (payments: Payment[]): void => {
  localStorage.setItem(storageKeys.payments, JSON.stringify(payments));
};

export const getBankNames = (): string[] => {
  try {
    const data = localStorage.getItem(storageKeys.bankNames);
    return data ? JSON.parse(data) : [...DEFAULT_BANK_NAMES];
  } catch {
    return [...DEFAULT_BANK_NAMES];
  }
};

export const saveBankNames = (names: string[]): void => {
  localStorage.setItem(storageKeys.bankNames, JSON.stringify(names));
};

export const getFacilityTypes = (): string[] => {
  try {
    const data = localStorage.getItem(storageKeys.facilityTypes);
    return data ? JSON.parse(data) : [...DEFAULT_FACILITY_TYPES];
  } catch {
    return [...DEFAULT_FACILITY_TYPES];
  }
};

export const saveFacilityTypes = (types: string[]): void => {
  localStorage.setItem(storageKeys.facilityTypes, JSON.stringify(types));
};

export const addFacility = (
  facility: Omit<Facility, "id" | "createdAt">
): Facility => {
  const facilities = getFacilities();
  const newFacility: Facility = {
    ...facility,
    id: Date.now(),
    createdAt: new Date().toISOString(),
    installments: facility.installments || [],
  };
  facilities.push(newFacility);
  saveFacilities(facilities);
  return newFacility;
};

export const addPayment = (
  payment: Omit<Payment, "id" | "createdAt">
): Payment => {
  const payments = getPayments();
  const newPayment: Payment = {
    ...payment,
    id: Date.now(),
    createdAt: new Date().toISOString(),
  };
  payments.push(newPayment);
  savePayments(payments);
  return newPayment;
};

export const updateFacility = (
  id: number,
  updates: Partial<Omit<Facility, "id" | "createdAt">>
): Facility | null => {
  const facilities = getFacilities();
  const index = facilities.findIndex((f) => f.id === id);
  if (index === -1) return null;
  facilities[index] = {
    ...facilities[index],
    ...updates,
    installments: updates.installments ?? facilities[index].installments,
  };
  saveFacilities(facilities);
  return facilities[index];
};

export const updatePayment = (
  id: number,
  updates: Partial<Omit<Payment, "id" | "createdAt">>
): Payment | null => {
  const payments = getPayments();
  const index = payments.findIndex((p) => p.id === id);
  if (index === -1) return null;
  payments[index] = { ...payments[index], ...updates };
  savePayments(payments);
  return payments[index];
};

export const deleteFacility = (id: number): void => {
  const facilities = getFacilities();
  const updated = facilities.filter((f) => f.id !== id);
  saveFacilities(updated);
};

export const deletePaymentsByFacility = (facilityId: number): void => {
  const payments = getPayments();
  const remaining = payments.filter((p) => p.facilityId !== facilityId);
  savePayments(remaining);
};

export const deletePayment = (id: number): void => {
  const payments = getPayments();
  const updated = payments.filter((p) => p.id !== id);
  savePayments(updated);
};

// محاسبه مبلغ باقیمانده برای یک قسط خاص
export const getRemainingInstallmentAmount = (
  facilityId: number,
  installmentNumber: number
): {
  principalRemaining: number;
  commissionRemaining: number;
  otherRemaining: number;
} => {
  try {
    // دریافت تسهیلات
    const facilities = getFacilities();
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility || !facility.installments) {
      return { principalRemaining: 0, commissionRemaining: 0, otherRemaining: 0 };
    }

    // دریافت اطلاعات قسط
    const installment = facility.installments[installmentNumber - 1];
    if (!installment) {
      return { principalRemaining: 0, commissionRemaining: 0, otherRemaining: 0 };
    }

    // محاسبه مبلغ اصل قسط
    const principalAmount = parseInt(installment.principalAmount) || 0;
    
    // دریافت پرداخت‌های قبلی برای این قسط
    const payments = getPayments();
    const installmentPayments = payments.filter(
      p => p.facilityId === facilityId && p.installmentNumber === installmentNumber
    );

    // محاسبه مجموع پرداخت‌های قبلی برای این قسط
    const paidPrincipal = installmentPayments.reduce((sum, p) => sum + (p.principalAmount || 0), 0);
    
    // محاسبه مبلغ باقیمانده اصل قسط
    const principalRemaining = Math.max(0, principalAmount - paidPrincipal);
    
    // محاسبه مجموع پرداخت‌های کارمزد و سایر برای کل تسهیلات
    const allFacilityPayments = payments.filter(p => p.facilityId === facilityId);
    const totalPaidCommission = allFacilityPayments.reduce((sum, p) => sum + (p.commissionAmount || 0), 0);
    const totalPaidOther = allFacilityPayments.reduce((sum, p) => sum + (p.otherAmount || 0), 0);
    
    // محاسبه مبالغ باقیمانده کارمزد و سایر در سطح تسهیلات
    const commissionRemaining = Math.max(0, (facility.commissionAmount || 0) - totalPaidCommission);
    const otherRemaining = Math.max(0, (facility.otherAmount || 0) - totalPaidOther);

    return { principalRemaining, commissionRemaining, otherRemaining };
  } catch (error) {
    console.error("Error calculating remaining amount:", error);
    return { principalRemaining: 0, commissionRemaining: 0, otherRemaining: 0 };
  }
};

// محاسبه تعداد اقساط تسویه شده برای یک تسهیلات
export const getSettledInstallmentsCount = (facilityId: number): number => {
  try {
    // دریافت تسهیلات
    const facilities = getFacilities();
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility || !facility.installments) {
      return 0;
    }

    // دریافت پرداخت‌های مربوط به این تسهیلات
    const payments = getPayments();
    const facilityPayments = payments.filter(p => p.facilityId === facilityId);

    // محاسبه مجموع پرداخت‌های هر قسط
    let settledCount = 0;
    
    for (let i = 1; i <= facility.installmentCount; i++) {
      const installment = facility.installments[i - 1];
      if (!installment) continue;
      
      // مبلغ اصل قسط
      const principalAmount = parseInt(installment.principalAmount) || 0;
      
      // مجموع پرداخت‌های اصل برای این قسط
      const installmentPayments = facilityPayments.filter(p => p.installmentNumber === i);
      const paidPrincipal = installmentPayments.reduce((sum, p) => sum + (p.principalAmount || 0), 0);
      
      // اگر مبلغ پرداختی بیشتر یا مساوی مبلغ اصل قسط باشد، قسط تسویه شده است
      if (paidPrincipal >= principalAmount) {
        settledCount++;
      }
    }
    
    return settledCount;
  } catch (error) {
    console.error("Error calculating settled installments:", error);
    return 0;
  }
};

// --- Deviation Status Management (for انحراف بررسی شده) ---

const deviationStatusKey = "deviationStatus";

// خواندن وضعیت بررسی انحراف پرداخت‌ها
export const getDeviationStatus = (): Record<
  number,
  { [method: string]: boolean }
> => {
  try {
    const data = localStorage.getItem(deviationStatusKey);
    return data ? JSON.parse(data) : {};
  } catch {
    return {};
  }
};

// ثبت وضعیت بررسی انحراف (یک پرداخت و یک روش)
export const setDeviationReviewed = (
  paymentId: number,
  method: number,
  reviewed: boolean
): void => {
  const status = getDeviationStatus();
  if (!status[paymentId]) status[paymentId] = {};
  status[paymentId][`method${method}`] = reviewed;
  localStorage.setItem(deviationStatusKey, JSON.stringify(status));
};