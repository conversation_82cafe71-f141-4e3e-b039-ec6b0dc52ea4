import DatePicker from "react-multi-date-picker";
import persian from "react-date-object/calendars/persian";
import persian_fa from "react-date-object/locales/persian_fa";
import DateObject from "react-date-object";
import "react-multi-date-picker/styles/colors/green.css";
import "./datepicker.css";
import { Label } from "@/components/ui/label";
import { useEffect, useState } from "react";
import { Calendar } from "lucide-react";

interface PersianDatePickerProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
}

const PersianDatePicker = ({
  label,
  value,
  onChange,
  required = false,
}: PersianDatePickerProps) => {
  const [selectedDay, setSelectedDay] = useState<DateObject | null>(() => {
    if (value) {
      return new DateObject({ date: value, calendar: persian, locale: persian_fa, format: "YYYY/MM/DD" });
    }
    return null;
  });

  useEffect(() => {
    if (value) {
      setSelectedDay(new DateObject({ date: value, calendar: persian, locale: persian_fa, format: "YYYY/MM/DD" }));
    } else {
      setSelectedDay(null);
    }
  }, [value]);

  const handleDayChange = (date: DateObject | DateObject[] | null) => {
    if (date instanceof DateObject) {
      // اطمینان از فرمت صحیح تاریخ (yyyy/mm/dd)
      const year = date.year.toString();
      const month = date.month.number.toString().padStart(2, '0');
      const day = date.day.toString().padStart(2, '0');
      onChange(`${year}/${month}/${day}`);
    } else {
      onChange("");
    }
  };

  return (
    <div className="space-y-2">
      <Label>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      <div className="relative">
        <DatePicker
          value={selectedDay}
          onChange={handleDayChange}
          calendar={persian}
          locale={persian_fa}
          format="YYYY/MM/DD"
          calendarPosition="bottom-left"
          className="rmdp-prime green"
          inputClass="w-full p-2 border rounded-md"
          containerClassName="w-full"
          placeholder="انتخاب تاریخ"
        />
        <div className="absolute left-2 top-1/2 -translate-y-1/2 pointer-events-none">
          <Calendar className="h-4 w-4 text-gray-500" />
        </div>
      </div>
    </div>
  );
};

export default PersianDatePicker;