import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface SearchableSelectProps {
  label: string;
  options: string[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
}

const SearchableSelect = ({
  label,
  options,
  value,
  onValueChange,
  placeholder = "انتخاب کنید",
  required = false,
}: SearchableSelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);

  // اگر گزینه شامل | باشد، بخش دوم را به عنوان برچسب نمایش می‌دهیم
  const getLabel = (opt: string) =>
    opt.includes("|") ? opt.split("|")[1] : opt;

  // برچسب گزینه انتخاب شده
  const selectedLabel = value ? getLabel(value) : "";

  // فیلتر کردن گزینه‌ها بر اساس جستجو
  const filteredOptions = searchQuery
    ? options.filter((option) =>
        getLabel(option).toLowerCase().includes(searchQuery.toLowerCase())
      )
    : options;

  // بستن دراپ‌داون با کلیک خارج از آن
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelect = (option: string) => {
    onValueChange(option);
    setIsOpen(false);
    setSearchQuery("");
  };

  return (
    <div className="space-y-2 relative" ref={dropdownRef}>
      <label className="text-sm font-medium">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <Button
        type="button"
        variant="outline"
        className="w-full justify-between"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
      >
        {selectedLabel || placeholder}
        <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
      </Button>

      {isOpen && (
        <div
          className="absolute z-[9999] mt-1 w-full rounded-md border border-gray-200 bg-white shadow-lg"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-2">
            <Input
              placeholder="جستجو..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onClick={(e) => e.stopPropagation()}
              className="mb-2"
            />
          </div>
          <div className="max-h-[200px] overflow-y-auto">
            {filteredOptions.length === 0 ? (
              <div className="py-2 px-3 text-sm text-gray-500 text-center">
                موردی یافت نشد.
              </div>
            ) : (
              filteredOptions.map((option) => (
                <div
                  key={option}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-gray-100",
                    value === option ? "bg-gray-100" : ""
                  )}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleSelect(option);
                  }}
                >
                  <Check
                    className={cn(
                      "ml-2 h-4 w-4",
                      value === option ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span>{getLabel(option)}</span>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchableSelect;