import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface SearchableSelectProps {
  label: string;
  options: string[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
}

const SearchableSelect = ({
  label,
  options,
  value,
  onValueChange,
  placeholder = "انتخاب کنید",
  required = false,
}: SearchableSelectProps) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // اگر گزینه شامل | باشد، بخش دوم را به عنوان برچسب نمایش می‌دهیم
  const getLabel = (opt: string) =>
    opt.includes("|") ? opt.split("|")[1] : opt;

  // برچسب گزینه انتخاب شده
  const selectedLabel = value ? getLabel(value) : "";

  // فیلتر کردن گزینه‌ها بر اساس جستجو
  const filteredOptions = searchQuery
    ? options.filter((option) =>
        getLabel(option).toLowerCase().includes(searchQuery.toLowerCase())
      )
    : options;

  const handleSelect = (option: string) => {
    onValueChange(option);
    setOpen(false);
    setSearchQuery("");
  };

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            type="button"
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedLabel || placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="p-2">
            <Input
              placeholder="جستجو..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="mb-2"
            />
          </div>
          <div className="max-h-[200px] overflow-y-auto">
            {filteredOptions.length === 0 ? (
              <div className="py-2 px-3 text-sm text-gray-500 text-center">
                موردی یافت نشد.
              </div>
            ) : (
              filteredOptions.map((option) => (
                <div
                  key={option}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-gray-100",
                    value === option ? "bg-gray-100" : ""
                  )}
                  onClick={() => handleSelect(option)}
                >
                  <Check
                    className={cn(
                      "ml-2 h-4 w-4",
                      value === option ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span>{getLabel(option)}</span>
                </div>
              ))
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default SearchableSelect;