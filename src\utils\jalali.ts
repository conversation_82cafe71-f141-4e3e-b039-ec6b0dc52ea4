export interface GregorianDate {
  gy: number;
  gm: number;
  gd: number;
}

const breaks = [
  -61, 9, 38, 199, 426, 686, 756, 818, 1111, 1181,
  1210, 1635, 2060, 2097, 2192, 2262, 2324, 2394, 2456, 3178
];

function jalCal(jy: number) {
  let bl = breaks.length;
  let gy = jy + 621;
  let leapJ = -14;
  let jp = breaks[0];
  let jm: number;
  let jump = 0;
  for (let i = 1; i < bl; i++) {
    jm = breaks[i];
    jump = jm - jp;
    if (jy < jm) break;
    leapJ = leapJ + Math.floor(jump / 33) * 8 + Math.floor((jump % 33) / 4);
    jp = jm;
  }
  let n = jy - jp;
  leapJ = leapJ + Math.floor(n / 33) * 8 + Math.floor(((n % 33) + 3) / 4);
  if (Math.floor((jump % 33) / 4) === 0 && jump % 33 === 4) leapJ++;
  let leapG = Math.floor(gy / 4) - Math.floor(((Math.floor(gy / 100) + 1) * 3) / 4) - 150;
  let march = 20 + leapJ - leapG;
  if (jump - n < 6) n = n - jump + Math.floor((jump + 4) / 33) * 33;
  let leap = (((n + 1) % 33) - 1) % 4;
  if (leap === -1) leap = 4;
  return { leap, gy, march };
}

export function toGregorian(jy: number, jm: number, jd: number): GregorianDate {
  jy = parseInt(String(jy));
  jm = parseInt(String(jm));
  jd = parseInt(String(jd));
  let r = jalCal(jy);
  let gy = r.gy;
  let march = r.march;
  gy += jm > 10 || (jm === 10 && jd > march) ? 1 : 0;
  let gDayNo = 365 * (gy - 1) + Math.floor((gy - 1) / 4) - Math.floor((gy - 1) / 100) + Math.floor((gy - 1) / 400);
  gDayNo += jd + (jm < 7 ? (jm - 1) * 31 : ((jm - 7) * 30 + 186)) + march - 1;
  gy = 400 * Math.floor(gDayNo / 146097) + 1;
  gDayNo %= 146097;
  if (gDayNo >= 36525) {
    gDayNo--;
    gy += 100 * Math.floor(gDayNo / 36524);
    gDayNo %= 36524;
    if (gDayNo >= 365) gDayNo++;
  }
  gy += 4 * Math.floor(gDayNo / 1461);
  gDayNo %= 1461;
  if (gDayNo >= 366) {
    gy += Math.floor((gDayNo - 1) / 365);
    gDayNo = (gDayNo - 1) % 365;
  }
  let gd = gDayNo + 1;
  const sal_a = [0,31,28,31,30,31,30,31,31,30,31,30,31];
  let leap = (gy % 4 === 0 && gy % 100 !== 0) || gy % 400 === 0;
  if (leap) sal_a[2] = 29;
  let gm = 0;
  for (gm = 0; gm <= 12 && gd > sal_a[gm]; gm++) {
    gd -= sal_a[gm];
  }
  return { gy, gm, gd };
}

export function persianToDate(jy: number, jm: number, jd: number): Date {
  const g = toGregorian(jy, jm, jd);
  return new Date(g.gy, g.gm - 1, g.gd);
}
