import { useEffect, useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Edit, Trash2, FileText, Printer, ArrowRight, Search, Calendar, TrendingUp, Save } from "lucide-react";
import PersianDatePicker from "@/components/common/PersianDatePicker";
import MoneyInput from "@/components/ui/money-input";
import { getFacilities, saveFacilities } from "@/utils/storage";
import { Facility } from "@/types/types";
import { Installment } from "@/types/facility";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { useToast } from "@/hooks/use-toast";
import * as XLSX from 'xlsx';

interface InstallmentListProps {
  onBack: () => void;
}

interface Row {
  facilityId: number;
  facility: Facility;
  installment: Installment;
}

const InstallmentList = ({ onBack }: InstallmentListProps) => {
  const { toast } = useToast();
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [search, setSearch] = useState("");
  const [editRow, setEditRow] = useState<Row | null>(null);

  useEffect(() => {
    setFacilities(getFacilities());
  }, []);

  const rows = useMemo(() => {
    const list: Row[] = [];
    facilities.forEach((f) => {
      (f.installments || []).forEach((inst) => {
        list.push({ facilityId: f.id, facility: f, installment: { ...inst } });
      });
    });
    return list;
  }, [facilities]);

  const filtered = useMemo(() => {
    const term = search.trim();
    if (!term) return rows;
    return rows.filter((r) =>
      r.facility.bankName.includes(term) ||
      r.facility.contractNumber.includes(term)
    );
  }, [rows, search]);

  const updateFacilities = (updated: Facility[]) => {
    saveFacilities(updated);
    setFacilities(updated);
  };

  const handleDelete = (row: Row) => {
    const updated = facilities.map((f) =>
      f.id === row.facilityId
        ? { ...f, installments: (f.installments || []).filter((i) => i.id !== row.installment.id) }
        : f
    );
    updateFacilities(updated);
    toast({ title: "حذف شد" });
  };

  const handleEditChange = (field: keyof Installment, value: string) => {
    if (editRow) {
      setEditRow({ ...editRow, installment: { ...editRow.installment, [field]: value } });
    }
  };

  const handleEditSave = () => {
    if (!editRow) return;
    const updated = facilities.map((f) => {
      if (f.id !== editRow.facilityId) return f;
      const insts = (f.installments || []).map((i) =>
        i.id === editRow.installment.id ? editRow.installment : i
      );
      return { ...f, installments: insts };
    });
    updateFacilities(updated);
    toast({ title: "ذخیره شد" });
    setEditRow(null);
  };

  const exportExcel = () => {
    try {
      // تبدیل داده‌ها به فرمت مناسب برای اکسل
      const data = filtered.map((r) => ({
        'نام بانک': r.facility.bankName,
        'شماره قرارداد': r.facility.contractNumber,
        'مبلغ تسهیلات': typeof r.facility.amount === 'string' ? parseInt(r.facility.amount) : r.facility.amount,
        'شماره قسط': r.installment.id,
        'تاریخ سررسید': r.installment.dueDate,
        'مبلغ اصل': typeof r.installment.principalAmount === 'string' ? parseInt(r.installment.principalAmount) : r.installment.principalAmount,
        'مبلغ سود': typeof r.installment.interestAmount === 'string' ? parseInt(r.installment.interestAmount) : r.installment.interestAmount,
      }));
      
      // ایجاد کاربرگ و کتاب کار
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "اقساط");
      
      // تنظیم عرض ستون‌ها
      const maxWidth = 20;
      const cols = Object.keys(data[0]).map(() => ({ wch: maxWidth }));
      worksheet['!cols'] = cols;
      
      // ذخیره فایل با فرمت xlsx
      XLSX.writeFile(workbook, "installments.xlsx");
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({ 
        title: "خطا", 
        description: "خطا در صدور فایل اکسل",
        variant: "destructive" 
      });
    }
  };

  const exportPDF = () => {
    window.print();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6 space-y-8">
      {/* Enhanced Header */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl shadow-lg">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                لیست اقساط
              </h1>
              <p className="text-slate-500 mt-1">مدیریت و مشاهده تمام اقساط تسهیلات</p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={exportExcel}
              className="flex items-center gap-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 transition-all duration-200 shadow-sm"
            >
              <FileText className="h-4 w-4" />
              دریافت Excel
            </Button>
            <Button
              variant="outline"
              onClick={exportPDF}
              className="flex items-center gap-2 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-all duration-200 shadow-sm"
            >
              <Printer className="h-4 w-4" />
              دریافت PDF
            </Button>
            <Button
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2 bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
            >
              <ArrowRight className="h-4 w-4" />
              بازگشت
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Search */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Search className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-slate-700">جستجو در اقساط</h3>
        </div>
        <Input
          placeholder="جستجوی بانک یا شماره قرارداد"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="h-12 border-2 border-slate-200 hover:border-purple-300 focus:border-purple-500 transition-all duration-200 bg-white shadow-sm"
        />
      </div>

      {/* Enhanced Table */}
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-slate-200/60 p-6">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-6 w-6 text-purple-600" />
            <h3 className="text-xl font-semibold text-slate-800">جدول اقساط</h3>
          </div>
        </div>
        <div className="overflow-x-auto">
          <Table className="border-collapse">
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 border-b-2 border-slate-200">
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-blue-50">نام بانک</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-indigo-50">شماره قرارداد</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-purple-50">مبلغ تسهیلات</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-pink-50">شماره قسط</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-rose-50">تاریخ سررسید</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-emerald-50">مبلغ اصل</TableHead>
                <TableHead className="text-right border-r border-slate-200 p-4 font-semibold text-slate-700 bg-teal-50">مبلغ سود</TableHead>
                <TableHead className="border-r border-slate-200 p-4 font-semibold text-slate-700 bg-slate-50">عملیات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filtered.map((r, index) => (
                <TableRow
                  key={`${r.facilityId}_${r.installment.id}`}
                  className={`border-b border-slate-200 hover:bg-gradient-to-r hover:from-purple-50/50 hover:to-pink-50/50 transition-all duration-200 ${
                    (index % 2 === 0) ? 'bg-white' : 'bg-slate-50/30'
                  }`}
                >
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-800">{r.facility.bankName}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-700">{r.facility.contractNumber}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-blue-700">{formatCurrency(r.facility.amount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-bold text-purple-600 bg-purple-50/50">{r.installment.id}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-medium text-slate-600">{formatDate(r.installment.dueDate)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-emerald-700">{formatCurrency(r.installment.principalAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200 font-semibold text-teal-700">{formatCurrency(r.installment.interestAmount)}</TableCell>
                  <TableCell className="p-4 border-r border-slate-200">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditRow(r)}
                        className="bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-200 shadow-sm"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDelete(r)}
                        className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-all duration-200 shadow-sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Enhanced Edit Dialog */}
      <Dialog open={!!editRow} onOpenChange={(o) => !o && setEditRow(null)}>
        <DialogContent className="max-w-md bg-white rounded-2xl shadow-2xl border border-slate-200/60">
          {editRow && (
            <div className="p-6 space-y-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl shadow-lg">
                  <Edit className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-slate-800">ویرایش قسط</h3>
                  <p className="text-slate-500 text-sm">قسط شماره {editRow.installment.id}</p>
                </div>
              </div>

              <div className="space-y-4">
                <PersianDatePicker
                  label="تاریخ سررسید"
                  value={editRow.installment.dueDate}
                  onChange={(v) => handleEditChange('dueDate', v)}
                  required
                />
                <MoneyInput
                  value={editRow.installment.principalAmount}
                  onChange={(v) => handleEditChange('principalAmount', v)}
                  placeholder="مبلغ اصل"
                />
                <MoneyInput
                  value={editRow.installment.interestAmount}
                  onChange={(v) => handleEditChange('interestAmount', v)}
                  placeholder="مبلغ سود"
                />
              </div>

              <div className="flex justify-end gap-4 pt-6 border-t border-slate-200">
                <Button
                  variant="outline"
                  onClick={() => setEditRow(null)}
                  className="bg-slate-50 border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 shadow-sm"
                >
                  <ArrowRight className="h-4 w-4 mr-2" />
                  لغو
                </Button>
                <Button
                  onClick={handleEditSave}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  ذخیره
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InstallmentList;