import { useEffect, useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Edit, Trash2, FileText, Printer } from "lucide-react";
import PersianDatePicker from "@/components/common/PersianDatePicker";
import MoneyInput from "@/components/ui/money-input";
import { getFacilities, saveFacilities } from "@/utils/storage";
import { Facility } from "@/types/types";
import { Installment } from "@/types/facility";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { useToast } from "@/hooks/use-toast";
import * as XLSX from 'xlsx';

interface InstallmentListProps {
  onBack: () => void;
}

interface Row {
  facilityId: number;
  facility: Facility;
  installment: Installment;
}

const InstallmentList = ({ onBack }: InstallmentListProps) => {
  const { toast } = useToast();
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [search, setSearch] = useState("");
  const [editRow, setEditRow] = useState<Row | null>(null);

  useEffect(() => {
    setFacilities(getFacilities());
  }, []);

  const rows = useMemo(() => {
    const list: Row[] = [];
    facilities.forEach((f) => {
      (f.installments || []).forEach((inst) => {
        list.push({ facilityId: f.id, facility: f, installment: { ...inst } });
      });
    });
    return list;
  }, [facilities]);

  const filtered = useMemo(() => {
    const term = search.trim();
    if (!term) return rows;
    return rows.filter((r) =>
      r.facility.bankName.includes(term) ||
      r.facility.contractNumber.includes(term)
    );
  }, [rows, search]);

  const updateFacilities = (updated: Facility[]) => {
    saveFacilities(updated);
    setFacilities(updated);
  };

  const handleDelete = (row: Row) => {
    const updated = facilities.map((f) =>
      f.id === row.facilityId
        ? { ...f, installments: (f.installments || []).filter((i) => i.id !== row.installment.id) }
        : f
    );
    updateFacilities(updated);
    toast({ title: "حذف شد" });
  };

  const handleEditChange = (field: keyof Installment, value: string) => {
    if (editRow) {
      setEditRow({ ...editRow, installment: { ...editRow.installment, [field]: value } });
    }
  };

  const handleEditSave = () => {
    if (!editRow) return;
    const updated = facilities.map((f) => {
      if (f.id !== editRow.facilityId) return f;
      const insts = (f.installments || []).map((i) =>
        i.id === editRow.installment.id ? editRow.installment : i
      );
      return { ...f, installments: insts };
    });
    updateFacilities(updated);
    toast({ title: "ذخیره شد" });
    setEditRow(null);
  };

  const exportExcel = () => {
    try {
      // تبدیل داده‌ها به فرمت مناسب برای اکسل
      const data = filtered.map((r) => ({
        'نام بانک': r.facility.bankName,
        'شماره قرارداد': r.facility.contractNumber,
        'مبلغ تسهیلات': typeof r.facility.amount === 'string' ? parseInt(r.facility.amount) : r.facility.amount,
        'شماره قسط': r.installment.id,
        'تاریخ سررسید': r.installment.dueDate,
        'مبلغ اصل': typeof r.installment.principalAmount === 'string' ? parseInt(r.installment.principalAmount) : r.installment.principalAmount,
        'مبلغ سود': typeof r.installment.interestAmount === 'string' ? parseInt(r.installment.interestAmount) : r.installment.interestAmount,
      }));
      
      // ایجاد کاربرگ و کتاب کار
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "اقساط");
      
      // تنظیم عرض ستون‌ها
      const maxWidth = 20;
      const cols = Object.keys(data[0]).map(() => ({ wch: maxWidth }));
      worksheet['!cols'] = cols;
      
      // ذخیره فایل با فرمت xlsx
      XLSX.writeFile(workbook, "installments.xlsx");
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({ 
        title: "خطا", 
        description: "خطا در صدور فایل اکسل",
        variant: "destructive" 
      });
    }
  };

  const exportPDF = () => {
    window.print();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">لیست اقساط</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportExcel} className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            دریافت Excel
          </Button>
          <Button variant="outline" onClick={exportPDF} className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            دریافت PDF
          </Button>
          <Button variant="outline" onClick={onBack}>بازگشت</Button>
        </div>
      </div>

      <Input
        placeholder="جستجوی بانک یا شماره قرارداد"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">نام بانک</TableHead>
              <TableHead className="text-right">شماره قرارداد</TableHead>
              <TableHead className="text-right">مبلغ تسهیلات</TableHead>
              <TableHead className="text-right">شماره قسط</TableHead>
              <TableHead className="text-right">تاریخ سررسید</TableHead>
              <TableHead className="text-right">مبلغ اصل</TableHead>
              <TableHead className="text-right">مبلغ سود</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            {filtered.map((r) => (
              <TableRow key={`${r.facilityId}_${r.installment.id}`} className="hover:bg-gray-50">
                <TableCell>{r.facility.bankName}</TableCell>
                <TableCell>{r.facility.contractNumber}</TableCell>
                <TableCell>{formatCurrency(r.facility.amount)} ریال</TableCell>
                <TableCell>{r.installment.id}</TableCell>
                <TableCell>{formatDate(r.installment.dueDate)}</TableCell>
                <TableCell>{formatCurrency(r.installment.principalAmount)} ریال</TableCell>
                <TableCell>{formatCurrency(r.installment.interestAmount)} ریال</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setEditRow(r)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="destructive" onClick={() => handleDelete(r)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!editRow} onOpenChange={(o) => !o && setEditRow(null)}>
        <DialogContent className="max-w-sm">
          {editRow && (
            <div className="space-y-4">
              <PersianDatePicker
                label="تاریخ سررسید"
                value={editRow.installment.dueDate}
                onChange={(v) => handleEditChange('dueDate', v)}
                required
              />
              <MoneyInput
                value={editRow.installment.principalAmount}
                onChange={(v) => handleEditChange('principalAmount', v)}
                placeholder="مبلغ اصل"
              />
              <MoneyInput
                value={editRow.installment.interestAmount}
                onChange={(v) => handleEditChange('interestAmount', v)}
                placeholder="مبلغ سود"
              />
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditRow(null)}>لغو</Button>
                <Button onClick={handleEditSave}>ذخیره</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InstallmentList;