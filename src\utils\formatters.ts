import { persianToDate } from "@/utils/jalali";

// تبدیل ارقام فارسی به انگلیسی
export const toEnglishDigits = (value: string): string => {
  const persianDigits = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
  return persianDigits.reduce(
    (acc, d, i) => acc.replace(new RegExp(d, 'g'), i.toString()),
    value
  );
};

// فرمت عدد به صورت پولی
export const formatCurrency = (amount: number | string): string => {
  const str = typeof amount === 'string' ? toEnglishDigits(amount) : String(amount);
  const num = parseFloat(str);
  if (isNaN(num)) return '0';
  return new Intl.NumberFormat('en-US').format(num);
};

// فرمت تاریخ به شمسی (با ارقام انگلیسی)
export const formatDate = (date: string | Date): string => {
  try {
    // اگر تاریخ به صورت رشته و در فرمت شمسی باشد، مستقیماً آن را برمی‌گردانیم
    if (typeof date === 'string' && /^\d{4}\/\d{2}\/\d{2}$/.test(date)) {
      return date;
    }
    
    // در غیر این صورت، اگر تاریخ میلادی است، آن را به شمسی تبدیل می‌کنیم
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    // استفاده از فرمت en-US برای نمایش اعداد به صورت انگلیسی
    const persianDate = new Intl.DateTimeFormat('fa-IR').format(dateObj);
    // تبدیل اعداد فارسی به انگلیسی
    return toEnglishDigits(persianDate);
  } catch {
    return date.toString();
  }
};

// پارس کردن رشته ورودی به عدد (حذف نمادها و تبدیل ارقام فارسی)
export const parseCurrency = (value: string): number => {
  const cleaned = toEnglishDigits(value).replace(/[^0-9]/g, '');
  return parseInt(cleaned) || 0;
};