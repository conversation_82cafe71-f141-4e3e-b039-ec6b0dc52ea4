diff a/src/components/lists/SimpleFacilityList.tsx b/src/components/lists/SimpleFacilityList.tsx	(rejected hunks)
@@ -1,137 +1,201 @@
-
-import { useState, useEffect } from "react";
-import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
+import { useEffect, useMemo, useState } from "react";
 import { Button } from "@/components/ui/button";
-import { Badge } from "@/components/ui/badge";
-import { getFacilities, getPayments } from "@/utils/storage";
-import { formatCurrency, formatDate } from "@/utils/formatters";
+import { Input } from "@/components/ui/input";
+import {
+  Table,
+  TableBody,
+  TableCell,
+  TableHead,
+  TableHeader,
+  TableRow,
+} from "@/components/ui/table";
+import { Dialog, DialogContent } from "@/components/ui/dialog";
+import { Edit, Trash2, Plus } from "lucide-react";
+import {
+  getFacilities,
+  getPayments,
+  deleteFacility,
+  deletePaymentsByFacility,
+} from "@/utils/storage";
+import { formatCurrency } from "@/utils/formatters";
 import { Facility, Payment } from "@/types/types";
-import { Plus } from "lucide-react";
+import SimpleFacilityForm from "@/components/facility/SimpleFacilityForm";
+import SimplePaymentForm from "@/components/payment/SimplePaymentForm";
+import { useToast } from "@/hooks/use-toast";
 
 interface SimpleFacilityListProps {
   onBack: () => void;
   onAddFacility: () => void;
   onAddPayment: () => void;
 }
 
-const SimpleFacilityList = ({ onBack, onAddFacility, onAddPayment }: SimpleFacilityListProps) => {
+const SimpleFacilityList = ({
+  onBack,
+  onAddFacility,
+  onAddPayment,
+}: SimpleFacilityListProps) => {
+  const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
   const [payments, setPayments] = useState<Payment[]>([]);
+  const [search, setSearch] = useState("");
+  const [editFacility, setEditFacility] = useState<Facility | null>(null);
+  const [editPayment, setEditPayment] = useState<Payment | null>(null);
 
   useEffect(() => {
+    refreshData();
+  }, []);
+
+  const refreshData = () => {
     setFacilities(getFacilities());
     setPayments(getPayments());
-  }, []);
+  };
+
+  const facilityPayments = (id: number) =>
+    payments.filter((p) => p.facilityId === id);
+
+  const filteredFacilities = useMemo(
+    () =>
+      facilities.filter(
+        (f) =>
+          f.bankName.includes(search) || f.contractNumber.includes(search),
+      ),
+    [facilities, search],
+  );
+
+  const handleDelete = (facility: Facility) => {
+    if (facilityPayments(facility.id).length > 0) return;
+    deleteFacility(facility.id);
+    deletePaymentsByFacility(facility.id);
+    toast({ title: "حذف شد" });
+    refreshData();
+  };
 
-  const getFacilityPayments = (facilityId: number) => {
-    return payments.filter(p => p.facilityId === facilityId);
+  const handleFacilitySaved = () => {
+    setEditFacility(null);
+    refreshData();
   };
 
-  const getTotalPaid = (facilityId: number) => {
-    return getFacilityPayments(facilityId).reduce((sum, p) => sum + p.totalAmount, 0);
+  const handlePaymentSaved = () => {
+    setEditPayment(null);
+    refreshData();
   };
 
-  const getPaymentStatus = (facility: Facility) => {
-    const totalPaid = getTotalPaid(facility.id);
-    const percentage = (totalPaid / facility.amount) * 100;
-    
-    if (percentage === 0) return { text: "پرداخت نشده", color: "bg-red-500" };
-    if (percentage < 100) return { text: "در حال پرداخت", color: "bg-yellow-500" };
-    return { text: "تکمیل شده", color: "bg-green-500" };
+  const paidInfo = (id: number) => {
+    const list = facilityPayments(id);
+    const principal = list.reduce((s, p) => s + p.principalAmount, 0);
+    const interest = list.reduce((s, p) => s + p.interestAmount, 0);
+    const penalty = list.reduce((s, p) => s + p.penaltyAmount, 0);
+    return { principal, interest, penalty, count: list.length };
   };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
         <h2 className="text-2xl font-bold">لیست تسهیلات</h2>
         <div className="flex gap-2">
           <Button onClick={onAddPayment} variant="outline" className="flex items-center gap-2">
-            <Plus className="h-4 w-4" />
-            پرداخت جدید
+            <Plus className="h-4 w-4" /> پرداخت جدید
           </Button>
           <Button onClick={onAddFacility} className="flex items-center gap-2">
-            <Plus className="h-4 w-4" />
-            تسهیلات جدید
+            <Plus className="h-4 w-4" /> تسهیلات جدید
           </Button>
           <Button variant="outline" onClick={onBack}>
             بازگشت
           </Button>
         </div>
       </div>
 
-      {facilities.length === 0 ? (
-        <Card>
-          <CardContent className="p-8 text-center">
-            <p className="text-gray-500 mb-4">هیچ تسهیلاتی ثبت نشده است</p>
-            <Button onClick={onAddFacility}>ثبت اولین تسهیلات</Button>
-          </CardContent>
-        </Card>
-      ) : (
-        <div className="space-y-4">
-          {facilities.map((facility) => {
-            const facilityPayments = getFacilityPayments(facility.id);
-            const totalPaid = getTotalPaid(facility.id);
-            const status = getPaymentStatus(facility);
-            
-            return (
-              <Card key={facility.id}>
-                <CardHeader>
-                  <div className="flex justify-between items-start">
-                    <div>
-                      <CardTitle className="text-lg">
-                        {facility.type} - {facility.bankName}
-                      </CardTitle>
-                      <p className="text-sm text-gray-600">
-                        شماره قرارداد: {facility.contractNumber}
-                      </p>
-                    </div>
-                    <Badge className={`${status.color} text-white`}>
-                      {status.text}
-                    </Badge>
-                  </div>
-                </CardHeader>
-                <CardContent>
-                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
-                    <div>
-                      <span className="text-sm text-gray-600">مبلغ کل:</span>
-                      <p className="font-semibold">{formatCurrency(facility.amount)} ریال</p>
-                    </div>
-                    <div>
-                      <span className="text-sm text-gray-600">پرداخت شده:</span>
-                      <p className="font-semibold text-green-600">{formatCurrency(totalPaid)} ریال</p>
-                    </div>
-                    <div>
-                      <span className="text-sm text-gray-600">تعداد اقساط:</span>
-                      <p className="font-semibold">{facility.installmentCount}</p>
-                    </div>
-                    <div>
-                      <span className="text-sm text-gray-600">نرخ سود:</span>
-                      <p className="font-semibold">{facility.interestRate}%</p>
-                    </div>
-                  </div>
-                  
-                  {facilityPayments.length > 0 && (
-                    <div>
-                      <h5 className="font-semibold mb-2">پرداخت‌های اخیر:</h5>
-                      <div className="space-y-2">
-                        {facilityPayments.slice(-3).map((payment) => (
-                          <div key={payment.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
-                            <span className="text-sm">قسط {payment.installmentNumber}</span>
-                            <span className="text-sm font-semibold">{formatCurrency(payment.totalAmount)} ریال</span>
-                            <span className="text-xs text-gray-500">{formatDate(payment.paymentDate)}</span>
-                          </div>
-                        ))}
-                      </div>
+      <Input
+        placeholder="جستجوی بانک یا شماره تسهیلات"
+        value={search}
+        onChange={(e) => setSearch(e.target.value)}
+      />
+
+      <div className="overflow-x-auto">
+        <Table>
+          <TableHeader>
+            <TableRow>
+              <TableHead className="text-right">نام بانک</TableHead>
+              <TableHead className="text-right">شماره تسهیلات</TableHead>
+              <TableHead className="text-right">مبلغ کل</TableHead>
+              <TableHead className="text-right">تعداد اقساط</TableHead>
+              <TableHead className="text-right">اقساط تسویه شده</TableHead>
+              <TableHead className="text-right">اقساط تسویه نشده</TableHead>
+              <TableHead className="text-right">مبلغ تسویه شده</TableHead>
+              <TableHead className="text-right">باقیمانده اصل</TableHead>
+              <TableHead className="text-right">سود پرداخت شده</TableHead>
+              <TableHead className="text-right">جریمه پرداخت شده</TableHead>
+              <TableHead></TableHead>
+            </TableRow>
+          </TableHeader>
+          <TableBody>
+            {filteredFacilities.map((f) => {
+              const info = paidInfo(f.id);
+              const unpaidCount = f.installmentCount - info.count;
+              const unpaidAmount = f.amount - info.principal;
+              return (
+                <TableRow key={f.id} className="hover:bg-gray-50">
+                  <TableCell>{f.bankName}</TableCell>
+                  <TableCell>{f.contractNumber}</TableCell>
+                  <TableCell>{formatCurrency(f.amount)} ریال</TableCell>
+                  <TableCell>{f.installmentCount}</TableCell>
+                  <TableCell>{info.count}</TableCell>
+                  <TableCell>{unpaidCount}</TableCell>
+                  <TableCell>{formatCurrency(info.principal)} ریال</TableCell>
+                  <TableCell>{formatCurrency(unpaidAmount)} ریال</TableCell>
+                  <TableCell>{formatCurrency(info.interest)} ریال</TableCell>
+                  <TableCell>{formatCurrency(info.penalty)} ریال</TableCell>
+                  <TableCell>
+                    <div className="flex gap-2">
+                      <Button
+                        size="sm"
+                        variant="outline"
+                        onClick={() => setEditFacility(f)}
+                      >
+                        <Edit className="h-4 w-4" />
+                      </Button>
+                      <Button
+                        size="sm"
+                        variant="destructive"
+                        onClick={() => handleDelete(f)}
+                        disabled={facilityPayments(f.id).length > 0}
+                      >
+                        <Trash2 className="h-4 w-4" />
+                      </Button>
                     </div>
-                  )}
-                </CardContent>
-              </Card>
-            );
-          })}
-        </div>
-      )}
+                  </TableCell>
+                </TableRow>
+              );
+            })
+          </TableBody>
+        </Table>
+      </div>
+
+      <Dialog open={!!editFacility} onOpenChange={(o) => !o && setEditFacility(null)}>
+        <DialogContent className="w-full h-full max-w-screen-lg overflow-y-auto">
+          {editFacility && (
+            <SimpleFacilityForm
+              facility={editFacility}
+              onBack={() => setEditFacility(null)}
+              onSuccess={handleFacilitySaved}
+            />
+          )}
+        </DialogContent>
+      </Dialog>
+
+      <Dialog open={!!editPayment} onOpenChange={(o) => !o && setEditPayment(null)}>
+        <DialogContent className="w-full h-full max-w-screen-lg overflow-y-auto">
+          {editPayment && (
+            <SimplePaymentForm
+              payment={editPayment}
+              onBack={() => setEditPayment(null)}
+              onSuccess={handlePaymentSaved}
+            />
+          )}
+        </DialogContent>
+      </Dialog>
     </div>
   );
 };
 
 export default SimpleFacilityList;
