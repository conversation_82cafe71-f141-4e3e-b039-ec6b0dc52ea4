import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { getFacilities, getPayments } from "@/utils/storage";
import { Facility, Payment } from "@/types/types";
import { formatCurrency } from "@/utils/formatters";
import { persianToDate } from "@/utils/jalali";
import { FileText, Printer, Search, Calculator, Building2 } from "lucide-react";
import * as XLSX from 'xlsx';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ControlMethod1Props {
  onBack: () => void;
  refreshTrigger?: number; // پارامتر جدید برای تریگر به‌روزرسانی
}

const ControlMethod1 = ({ onBack, refreshTrigger = 0 }: ControlMethod1Props) => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // انتخاب تسهیلات
  const [selectedFacilityId, setSelectedFacilityId] = useState<number | null>(null);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [calculationStarted, setCalculationStarted] = useState<boolean>(false);

  // جستجو
  const [searchTerm, setSearchTerm] = useState<string>("");
  
  // صادر کردن به اکسل
  const exportToExcel = () => {
    if (!filteredPayments || filteredPayments.length === 0) return;

    // تبدیل داده‌های پرداخت به فرمت مناسب برای اکسل - با استفاده از همان محاسبات جدول
    const excelData = filteredPayments.map(payment => {
      const { bankName, contractNumber, interestRate, penaltyRate, dueDate, receivedDate } =
        getFacilityInfo(payment.facilityId, payment.installmentNumber);

      // محاسبه تاخیر/تعجیل
      const delayDays = calculateDelayDays(dueDate, payment.paymentDate);

      // محاسبه مدت زمان تسویه (همان محاسبه جدول)
      const settlementDays = calculateDaysBetween(receivedDate, payment.paymentDate);

      // محاسبه سود استاندارد (همان محاسبه جدول)
      let standardInterest = 0;
      if (payment.principalAmount && payment.principalAmount > 0) {
        // فرمول: سود استاندارد = (اصل پرداختی * نرخ سود / 100) / 365 * مدت زمان تسویه
        standardInterest = Math.round((payment.principalAmount * (interestRate / 100) / 365) * settlementDays);
      } else {
        // اگر مبلغ اصل صفر است، از مانده در خط انحراف سود استفاده می‌کنیم
        const remainingDeviation = payment.remainingInterestDeviation || 0;
        standardInterest = Math.abs(remainingDeviation);
      }

      // محاسبه انحراف سود
      const interestDeviation = (payment.interestAmount || 0) - standardInterest;

      // محاسبه جریمه استاندارد (همان محاسبه جدول)
      let standardPenalty = 0;
      if (payment.principalAmount && payment.principalAmount > 0) {
        // فرمول: جریمه استاندارد = (اصل پرداختی * نرخ جریمه / 100) / 365 * تاخیر
        if (delayDays > 0) {
          standardPenalty = Math.round((payment.principalAmount * (penaltyRate / 100) / 365) * delayDays);
        } else {
          standardPenalty = 0; // تعجیل: جریمه استاندارد صفر
        }
      } else {
        // اگر مبلغ اصل صفر است، از مانده در خط انحراف جریمه استفاده می‌کنیم
        const remainingDeviation = payment.remainingPenaltyDeviation || 0;
        standardPenalty = Math.abs(remainingDeviation);
      }

      // محاسبه انحراف جریمه
      const penaltyDeviation = (payment.penaltyAmount || 0) - standardPenalty;

      return {
        'نام بانک': bankName,
        'شماره تسهیلات': contractNumber,
        'شماره قسط': payment.installmentNumber,
        'تاریخ اخذ تسهیلات': receivedDate,
        'تاریخ سررسید': dueDate,
        'تاریخ پرداخت': payment.paymentDate,
        'تاخیر/تعجیل': delayDays,
        'مدت زمان تسویه': settlementDays,
        'نرخ سود': `${interestRate}%`,
        'نرخ جریمه': `${penaltyRate}%`,
        'اصل پرداختی': payment.principalAmount || 0,
        'سود پرداختی': payment.interestAmount || 0,
        'سود استاندارد': standardInterest,
        'انحراف سود': interestDeviation,
        'جریمه پرداختی': payment.penaltyAmount || 0,
        'جریمه استاندارد': standardPenalty,
        'انحراف جریمه': penaltyDeviation,
        'مجموع پرداختی': payment.totalAmount || 0,
        'مانده انحراف سود': payment.remainingInterestDeviation || 0,
        'مانده انحراف جریمه': payment.remainingPenaltyDeviation || 0
      };
    });
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "کنترل تسهیلات");
    const maxWidth = 20;
    const cols = Object.keys(excelData[0]).map(() => ({ wch: maxWidth }));
    worksheet['!cols'] = cols;
    XLSX.writeFile(workbook, "کنترل-تسهیلات-روش-اول.xlsx");
  };
  
  // چاپ به صورت PDF
  const printAsPDF = () => {
    window.print();
  };

  // انتخاب تسهیلات
  const handleFacilitySelect = (facilityId: string) => {
    const id = parseInt(facilityId);
    setSelectedFacilityId(id);
    setCalculationStarted(false);
    console.log("Selected facility:", id);
  };

  // شروع محاسبات
  const startCalculations = async () => {
    if (!selectedFacilityId) return;

    setIsCalculating(true);
    setCalculationStarted(false);

    try {
      // شبیه‌سازی زمان پردازش
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log("Starting calculations for facility:", selectedFacilityId);
      setCalculationStarted(true);

      // اجرای تست محاسبه روزها
      testDaysCalculation();

    } catch (error) {
      console.error("Error during calculations:", error);
    } finally {
      setIsCalculating(false);
    }
  };

  // پاک کردن انتخاب تسهیلات
  const clearFacilitySelection = () => {
    setSelectedFacilityId(null);
    setCalculationStarted(false);
    setSearchTerm("");
  };
  
  // تبدیل تاریخ شمسی به تعداد روز
  const persianDateToDays = (date: string): number => {
    try {
      if (!date || date === "نامشخص") return 0;
      
      const [year, month, day] = date.split('/').map(Number);
      // استفاده از تابع persianToDate برای تبدیل صحیح تاریخ شمسی به میلادی
      const dateObj = persianToDate(year, month, day);
      return Math.floor(dateObj.getTime() / (24 * 60 * 60 * 1000));
    } catch (error) {
      console.error("Error converting date:", error, date);
      return 0;
    }
  };
  
  // بررسی سال کبیسه شمسی - نسخه اصلاح شده
  const isPersianLeapYear = (year: number): boolean => {
    // الگوی صحیح سال‌های کبیسه شمسی
    // 1402: NOT leap year (Esfand 29 days)
    // 1403: IS leap year (Esfand 30 days)
    // 1404: NOT leap year (Esfand 29 days)
    // 1405: NOT leap year (Esfand 29 days)
    // 1406: NOT leap year (Esfand 29 days)
    // 1407: IS leap year (Esfand 30 days)

    // الگوی 33 ساله: در هر 33 سال، 8 سال کبیسه وجود دارد
    // سال‌های کبیسه در هر چرخه 33 ساله: 1, 5, 9, 13, 17, 22, 26, 30

    const cycle = year % 33;
    const leapYearsInCycle = [1, 5, 9, 13, 17, 22, 26, 30];

    return leapYearsInCycle.includes(cycle);
  };

  // محاسبه تعداد روزهای یک ماه شمسی
  const getPersianMonthDays = (year: number, month: number): number => {
    if (month <= 6) {
      return 31; // فروردین تا شهریور: 31 روز
    } else if (month <= 11) {
      return 30; // مهر تا بهمن: 30 روز
    } else {
      // اسفند: 29 روز در سال عادی، 30 روز در سال کبیسه
      return isPersianLeapYear(year) ? 30 : 29;
    }
  };

  // محاسبه تعداد روزهای یک سال شمسی
  const getPersianYearDays = (year: number): number => {
    return isPersianLeapYear(year) ? 366 : 365;
  };

  // تابع محاسبه دقیق روزهای شمسی - نسخه اصلاح شده
  const calculatePersianDays = (startDate: string, endDate: string): number => {
    // تجزیه تاریخ‌ها
    const [startYear, startMonth, startDay] = startDate.split('/').map(Number);
    const [endYear, endMonth, endDay] = endDate.split('/').map(Number);

    // تابع کمکی برای تعداد روزهای هر ماه
    const getDaysInMonth = (year: number, month: number): number => {
      if (month <= 6) return 31; // فروردین تا شهریور
      if (month <= 11) return 30; // مهر تا بهمن
      return isPersianLeapYear(year) ? 30 : 29; // اسفند
    };

    // تابع کمکی برای تعداد روزهای سال
    const getDaysInYear = (year: number): number => {
      return isPersianLeapYear(year) ? 366 : 365;
    };

    let totalDays = 0;

    if (startYear === endYear) {
      // همان سال
      if (startMonth === endMonth) {
        // همان ماه - فقط تفاوت روزها
        return endDay - startDay;
      } else {
        // ماه‌های مختلف در همان سال
        // روزهای باقیمانده از ماه شروع (بدون شامل روز شروع)
        totalDays += getDaysInMonth(startYear, startMonth) - startDay;

        // ماه‌های کامل بین
        for (let m = startMonth + 1; m < endMonth; m++) {
          totalDays += getDaysInMonth(startYear, m);
        }

        // روزهای ماه پایان (شامل روز پایان)
        totalDays += endDay;

        return totalDays;
      }
    } else {
      // سال‌های مختلف
      // روزهای باقیمانده از ماه شروع (بدون شامل روز شروع)
      totalDays += getDaysInMonth(startYear, startMonth) - startDay;

      // ماه‌های باقیمانده از سال شروع
      for (let m = startMonth + 1; m <= 12; m++) {
        totalDays += getDaysInMonth(startYear, m);
      }

      // سال‌های کامل بین
      for (let y = startYear + 1; y < endYear; y++) {
        totalDays += getDaysInYear(y);
      }

      // ماه‌های کامل در سال پایان
      for (let m = 1; m < endMonth; m++) {
        totalDays += getDaysInMonth(endYear, m);
      }

      // روزهای ماه پایان (شامل روز پایان)
      totalDays += endDay;

      return totalDays;
    }
  };

  // محاسبه تعداد روزهای بین دو تاریخ شمسی - نسخه ساده و صحیح
  const calculateDaysBetween = (startDate: string, endDate: string): number => {
    try {
      if (!startDate || startDate === "نامشخص" || !endDate || endDate === "نامشخص") {
        console.log("Invalid dates for days calculation:", { startDate, endDate });
        return 0;
      }

      // پاک کردن فاصله‌های اضافی
      const cleanStartDate = startDate.trim();
      const cleanEndDate = endDate.trim();

      // تجزیه تاریخ‌ها
      const [startYear, startMonth, startDay] = cleanStartDate.split('/').map(Number);
      const [endYear, endMonth, endDay] = cleanEndDate.split('/').map(Number);

      // بررسی اعتبار تاریخ‌ها
      if (isNaN(startYear) || isNaN(startMonth) || isNaN(startDay) ||
          isNaN(endYear) || isNaN(endMonth) || isNaN(endDay)) {
        console.error("Invalid date format:", { startDate: cleanStartDate, endDate: cleanEndDate });
        return 0;
      }

      // استفاده از تابع محاسبه ساده
      const diffDays = calculatePersianDays(cleanStartDate, cleanEndDate);

      console.log("🔢 Days calculation (Simple & Correct):", {
        startDate: cleanStartDate,
        endDate: cleanEndDate,
        startComponents: { year: startYear, month: startMonth, day: startDay },
        endComponents: { year: endYear, month: endMonth, day: endDay },
        diffDays,
        isStartLeapYear: isPersianLeapYear(startYear),
        isEndLeapYear: isPersianLeapYear(endYear),
        note: diffDays < 0 ? "⚠️ منفی - ترتیب تاریخ‌ها اشتباه است" : "✅ مثبت"
      });

      return diffDays;
    } catch (error) {
      console.error("❌ Error calculating days between dates:", error, { startDate, endDate });
      return 0;
    }
  };

  // محاسبه تعداد روزهای تاخیر یا تعجیل
  const calculateDelayDays = (dueDate: string, paymentDate: string): number => {
    try {
      if (!dueDate || dueDate === "نامشخص" || !paymentDate) {
        console.log("Invalid dates for delay calculation:", { dueDate, paymentDate });
        return 0;
      }

      // محاسبه تاخیر/تعجیل: تاریخ پرداخت - تاریخ سررسید
      // مثبت = تاخیر، منفی = تعجیل
      const delayDays = calculateDaysBetween(dueDate, paymentDate);

      console.log("Delay calculation:", {
        dueDate,
        paymentDate,
        delayDays,
        status: delayDays > 0 ? 'تاخیر' : delayDays < 0 ? 'تعجیل' : 'به موقع'
      });

      return delayDays;
    } catch (error) {
      console.error("Error calculating delay days:", error, { dueDate, paymentDate });
      return 0;
    }
  };
  
  // محاسبه جریمه تاخیر
  const calculatePenalty = (
    principalAmount: number,
    penaltyRate: number,
    delayDays: number
  ): number => {
    if (delayDays <= 0) return 0;
    
    // نرخ جریمه سالانه به روزانه
    // تبدیل درصد به عدد اعشاری (تقسیم بر 100) و سپس تبدیل سالانه به روزانه (تقسیم بر 365)
    const dailyRate = (penaltyRate / 100) / 365;
    
    // محاسبه جریمه
    return Math.round(principalAmount * dailyRate * delayDays);
  };
  
  // محاسبه جریمه تاخیر با فرمول دقیق
  const calculateStandardPenalty = (
    principalAmount: number,
    penaltyRate: number,
    delayDays: number,
    facilityId: number,
    installmentNumber: number,
    currentPaymentId?: number, // شناسه پرداخت فعلی
    currentPaymentDate?: string // تاریخ پرداخت فعلی
  ): number => {
    // اگر تعجیل وجود داشته باشد (delayDays ≤ 0)، جریمه استاندارد = 0
    if (delayDays <= 0) return 0;
    
    // اگر مبلغ اصل صفر نیست، از روش معمولی استفاده می‌کنیم
    if (principalAmount > 0) {
      // فرمول دقیق برای محاسبه جریمه استاندارد:
      // (مبلغ پرداختی × نرخ جریمه) / 365 × تعداد روز تاخیر
      const standardPenalty = (principalAmount * penaltyRate / 100) / 365 * delayDays;
      return Math.round(standardPenalty);
    }
    
    // اگر مبلغ اصل صفر است، از روش جدید استفاده می‌کنیم
    // یافتن تسهیلات مربوطه
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility) {
      console.log("Facility not found for penalty:", facilityId);
      return 0;
    }
    
    // یافتن قسط مربوطه
    const installment = facility.installments?.find(i => i.installmentNumber === installmentNumber);
    if (!installment) {
      console.log("Installment not found for penalty:", installmentNumber);
      return 0;
    }
    
    // مبلغ اصل قسط را از اطلاعات تسهیلات بدست می‌آوریم
    const installmentPrincipal = installment.amount || 0;
    
    // اگر پرداخت فعلی اولین پرداخت برای این قسط است
    if (!currentPaymentId) {
      // محاسبه جریمه استاندارد کل تا تاریخ پرداخت فعلی
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided for penalty");
        return 0;
      }
      
      // محاسبه تعداد روزهای تاخیر برای پرداخت فعلی
      const currentDelayDays = calculateDelayDays(installment.dueDate, paymentDate);
      if (currentDelayDays <= 0) return 0; // اگر تاخیر نداشته باشد
      
      // محاسبه جریمه استاندارد کل
      const totalStandardPenalty = (installmentPrincipal * penaltyRate / 100) / 365 * currentDelayDays;
      
      console.log("First payment penalty calculation:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        currentDelayDays,
        totalStandardPenalty: Math.round(totalStandardPenalty)
      });
      
      return Math.round(totalStandardPenalty);
    }
    
    // یافتن تمام پرداخت‌های این قسط
    const allPayments = payments.filter(p => 
      p.facilityId === facilityId && 
      p.installmentNumber === installmentNumber
    );
    
    console.log("All payments for penalty calculation:", allPayments.length, 
      "Current payment ID:", currentPaymentId,
      "Payment IDs:", allPayments.map(p => p.id));
    
    // پیدا کردن پرداخت فعلی
    const currentPayment = allPayments.find(p => p.id === currentPaymentId);
    if (!currentPayment) {
      console.log("Current payment not found in payments list for penalty");
      
      // اگر پرداخت فعلی پیدا نشد، از تاریخ پرداخت ارسالی استفاده می‌کنیم
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided for penalty");
        return 0;
      }
      
      // محاسبه مجموع جریمه پرداخت شده در تمام پرداخت‌های قبلی
      const totalPaidPenalty = allPayments.reduce((sum, p) => sum + (p.penaltyAmount || 0), 0);
      
      // محاسبه تعداد روزهای تاخیر برای پرداخت فعلی
      const currentDelayDays = calculateDelayDays(installment.dueDate, paymentDate);
      if (currentDelayDays <= 0) return 0; // اگر تاخیر نداشته باشد
      
      // محاسبه جریمه استاندارد کل تا تاریخ پرداخت فعلی
      const totalStandardPenalty = (installmentPrincipal * penaltyRate / 100) / 365 * currentDelayDays;
      
      // اگر هنوز جریمه استاندارد باقی مانده است که باید پرداخت شود
      if (totalPaidPenalty < totalStandardPenalty) {
        const remainingStandardPenalty = Math.round(totalStandardPenalty) - totalPaidPenalty;
        
        console.log("Penalty calculation without current payment:", {
          facilityId,
          installmentNumber,
          installmentPrincipal,
          paymentDate,
          currentDelayDays,
          totalPaidPenalty,
          totalStandardPenalty: Math.round(totalStandardPenalty),
          remainingStandardPenalty
        });
        
        return Math.round(remainingStandardPenalty);
      }
      
      return 0;
    }
    
    // مرتب‌سازی پرداخت‌ها بر اساس تاریخ
    const sortedPayments = [...allPayments].sort((a, b) => {
      const dateA = persianDateToDays(a.paymentDate);
      const dateB = persianDateToDays(b.paymentDate);
      return dateA - dateB;
    });
    
    // پیدا کردن ایندکس پرداخت فعلی در لیست مرتب شده
    const currentPaymentIndex = sortedPayments.findIndex(p => p.id === currentPaymentId);
    if (currentPaymentIndex === -1) {
      console.log("Current payment not found in sorted payments for penalty");
      return 0;
    }
    
    // پرداخت‌های قبلی (قبل از پرداخت فعلی)
    const previousPayments = sortedPayments.slice(0, currentPaymentIndex);
    
    // محاسبه مجموع جریمه پرداخت شده در پرداخت‌های قبلی
    const totalPaidPenalty = previousPayments.reduce((sum, p) => sum + (p.penaltyAmount || 0), 0);
    
    // تاریخ پرداخت فعلی
    const paymentDate = currentPaymentDate || currentPayment.paymentDate;
    
    // محاسبه تعداد روزهای تاخیر برای پرداخت فعلی
    const currentDelayDays = calculateDelayDays(installment.dueDate, paymentDate);
    if (currentDelayDays <= 0) return 0; // اگر تاخیر نداشته باشد
    
    // محاسبه جریمه استاندارد کل تا تاریخ پرداخت فعلی
    const totalStandardPenalty = (installmentPrincipal * penaltyRate / 100) / 365 * currentDelayDays;
    
    // اگر هنوز جریمه استاندارد باقی مانده است که باید پرداخت شود
    if (totalPaidPenalty < totalStandardPenalty) {
      const remainingStandardPenalty = Math.round(totalStandardPenalty) - totalPaidPenalty;
      
      console.log("Penalty calculation for zero principal:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        currentDelayDays,
        totalPaidPenalty,
        totalStandardPenalty: Math.round(totalStandardPenalty),
        remainingStandardPenalty
      });
      
      return Math.round(remainingStandardPenalty);
    }
    
    // اگر بیشتر از جریمه استاندارد پرداخت شده، صفر برگردان
    return 0;
  };
  
  // محاسبه سود استاندارد
  const calculateStandardInterest = (
    principalAmount: number,
    interestRate: number,
    dayCount: number,
    facilityId: number,
    installmentNumber: number,
    currentPaymentId?: number, // شناسه پرداخت فعلی
    currentPaymentDate?: string // تاریخ پرداخت فعلی
  ): number => {
    if (dayCount <= 0) return 0;
    
    // اگر مبلغ اصل صفر نیست، از روش معمولی استفاده می‌کنیم
    if (principalAmount > 0) {
      // محاسبه معمول سود استاندارد برای حالتی که مبلغ اصل وجود دارد
      const standardInterest = (principalAmount * (interestRate / 100) / 365) * dayCount;
      return Math.round(standardInterest);
    }
    
    // اگر مبلغ اصل صفر است، از روش جدید استفاده می‌کنیم
    // یافتن تسهیلات مربوطه
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility) {
      console.log("Facility not found:", facilityId);
      return 0;
    }
    
    // یافتن قسط مربوطه
    const installment = facility.installments?.find(i => i.installmentNumber === installmentNumber);
    if (!installment) {
      console.log("Installment not found:", installmentNumber);
      return 0;
    }
    
    // مبلغ اصل قسط را از اطلاعات تسهیلات بدست می‌آوریم
    const installmentPrincipal = installment.amount || 0;
    
    // اگر پرداخت فعلی اولین پرداخت برای این قسط است
    if (!currentPaymentId) {
      // محاسبه سود استاندارد کل تا تاریخ پرداخت فعلی
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided");
        return 0;
      }
      
      const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", paymentDate);
      const totalStandardInterest = (installmentPrincipal * (interestRate / 100) / 365) * daysSinceReceived;
      
      console.log("First payment interest calculation:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        daysSinceReceived,
        totalStandardInterest: Math.round(totalStandardInterest)
      });
      
      return Math.round(totalStandardInterest);
    }
    
    // یافتن تمام پرداخت‌های این قسط
    const allPayments = payments.filter(p => 
      p.facilityId === facilityId && 
      p.installmentNumber === installmentNumber
    );
    
    console.log("All payments for this installment:", allPayments.length, 
      "Current payment ID:", currentPaymentId,
      "Payment IDs:", allPayments.map(p => p.id));
    
    // پیدا کردن پرداخت فعلی
    const currentPayment = allPayments.find(p => p.id === currentPaymentId);
    if (!currentPayment) {
      console.log("Current payment not found in payments list");
      
      // اگر پرداخت فعلی پیدا نشد، از تاریخ پرداخت ارسالی استفاده می‌کنیم
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided");
        return 0;
      }
      
      // محاسبه مجموع سود پرداخت شده در تمام پرداخت‌های قبلی
      const totalPaidInterest = allPayments.reduce((sum, p) => sum + (p.interestAmount || 0), 0);
      
      // محاسبه سود استاندارد کل تا تاریخ پرداخت فعلی
      const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", paymentDate);
      const totalStandardInterest = (installmentPrincipal * (interestRate / 100) / 365) * daysSinceReceived;
      
      // اگر هنوز سود استاندارد باقی مانده است که باید پرداخت شود
      if (totalPaidInterest < totalStandardInterest) {
        const remainingStandardInterest = Math.round(totalStandardInterest) - totalPaidInterest;
        
        console.log("Interest calculation without current payment:", {
          facilityId,
          installmentNumber,
          installmentPrincipal,
          paymentDate,
          daysSinceReceived,
          totalPaidInterest,
          totalStandardInterest: Math.round(totalStandardInterest),
          remainingStandardInterest
        });
        
        return Math.round(remainingStandardInterest);
      }
      
      return 0;
    }
    
    // مرتب‌سازی پرداخت‌ها بر اساس تاریخ
    const sortedPayments = [...allPayments].sort((a, b) => {
      const dateA = persianDateToDays(a.paymentDate);
      const dateB = persianDateToDays(b.paymentDate);
      return dateA - dateB;
    });
    
    // پیدا کردن ایندکس پرداخت فعلی در لیست مرتب شده
    const currentPaymentIndex = sortedPayments.findIndex(p => p.id === currentPaymentId);
    if (currentPaymentIndex === -1) {
      console.log("Current payment not found in sorted payments");
      return 0;
    }
    
    // پرداخت‌های قبلی (قبل از پرداخت فعلی)
    const previousPayments = sortedPayments.slice(0, currentPaymentIndex);
    
    // محاسبه مجموع سود پرداخت شده در پرداخت‌های قبلی
    const totalPaidInterest = previousPayments.reduce((sum, p) => sum + (p.interestAmount || 0), 0);
    
    // محاسبه سود استاندارد کل تا تاریخ پرداخت فعلی
    const paymentDate = currentPaymentDate || currentPayment.paymentDate;
    const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", paymentDate);
    const totalStandardInterest = (installmentPrincipal * (interestRate / 100) / 365) * daysSinceReceived;
    
    // اگر هنوز سود استاندارد باقی مانده است که باید پرداخت شود
    if (totalPaidInterest < totalStandardInterest) {
      const remainingStandardInterest = Math.round(totalStandardInterest) - totalPaidInterest;
      
      console.log("Interest calculation for zero principal:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        daysSinceReceived,
        totalPaidInterest,
        totalStandardInterest: Math.round(totalStandardInterest),
        remainingStandardInterest
      });
      
      return Math.round(remainingStandardInterest);
    }
    
    // اگر بیشتر از سود استاندارد پرداخت شده، صفر برگردان
    return 0;
  };

  // تست محاسبه روزها برای مثال داده شده
  const testDaysCalculation = () => {
    console.log("=== تست محاسبه روزها ===");

    // تست کیس‌های اصلاح شده - مقادیر صحیح
    const testCases = [
      {
        name: "Test Case 1",
        receivedDate: "1402/12/25",
        paymentDate: "1403/01/28",
        expected: 32,
        note: "1402 NOT leap year (Esfand 29), 1403 IS leap year"
      },
      {
        name: "Test Case 2",
        receivedDate: "1402/12/25",
        paymentDate: "1403/03/15",
        expected: 81,
        note: "Cross year calculation with leap year"
      },
      {
        name: "Test Case 3",
        receivedDate: "1402/12/25",
        paymentDate: "1403/08/06",
        expected: 226,
        note: "Long period calculation"
      },
      {
        name: "Test Case 4",
        receivedDate: "1402/12/25",
        paymentDate: "1404/04/09",
        expected: 472,
        note: "Multi-year calculation spanning leap year"
      }
    ];

    testCases.forEach(testCase => {
      const actualDays = calculateDaysBetween(testCase.receivedDate, testCase.paymentDate);
      const isCorrect = actualDays === testCase.expected;

      console.log(`🔥 ${testCase.name}:`, {
        receivedDate: testCase.receivedDate,
        paymentDate: testCase.paymentDate,
        actualDays,
        expectedDays: testCase.expected,
        isCorrect,
        difference: actualDays - testCase.expected,
        note: testCase.note,
        status: isCorrect ? "✅ PASS" : "❌ FAIL"
      });
    });

    // محاسبه دستی برای بررسی:
    // از 1402/12/25 تا 1402/12/30 = 6 روز (25, 26, 27, 28, 29, 30)
    // از 1403/01/01 تا 1403/01/28 = 28 روز (1, 2, 3, ..., 28)
    // مجموع = 6 + 28 = 34 روز (شامل هر دو روز)

    // تست دقیق با تبدیل به میلادی
    const startDateObj = persianToDate(1402, 12, 25);
    const endDateObj = persianToDate(1403, 1, 28);
    const diffTime = endDateObj.getTime() - startDateObj.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    console.log("🧮 Manual Calculation Check:", {
      startDate: "1402/12/25",
      endDate: "1403/01/28",
      startDateGregorian: startDateObj.toISOString().split('T')[0],
      endDateGregorian: endDateObj.toISOString().split('T')[0],
      diffTime,
      diffDays,
      settlementDaysIncludingBoth: diffDays + 1,
      note: "تفاوت میلادی + 1 برای شامل کردن هر دو روز"
    });

    // تست اضافی برای بررسی مشکل
    console.log("🔍 Debug Test:", {
      test1: calculateDaysBetween("1402/12/25", "1403/01/28"),
      test2: calculateDaysBetween("1403/01/28", "1402/12/25"),
      note: "بررسی ترتیب پارامترها"
    });

    // تست تاخیر/تعجیل
    const dueDate = "1403/01/15";
    const paymentDate2 = "1403/01/20";
    const delayDays = calculateDelayDays(dueDate, paymentDate2);

    console.log("Test - Delay Days:", {
      dueDate,
      paymentDate: paymentDate2,
      delayDays,
      expectedDelay: 5,
      status: delayDays > 0 ? 'تاخیر' : delayDays < 0 ? 'تعجیل' : 'به موقع'
    });

    // تست تعجیل
    const dueDate2 = "1403/02/15";
    const paymentDate3 = "1403/02/10";
    const advanceDays = calculateDelayDays(dueDate2, paymentDate3);

    console.log("Test - Advance Payment:", {
      dueDate: dueDate2,
      paymentDate: paymentDate3,
      advanceDays,
      expectedAdvance: -5,
      status: advanceDays > 0 ? 'تاخیر' : advanceDays < 0 ? 'تعجیل' : 'به موقع'
    });

    // تست سال‌های کبیسه - نسخه اصلاح شده
    console.log("🔍 Test - Leap Years (Corrected):", {
      1402: { isLeap: isPersianLeapYear(1402), expected: false, correct: isPersianLeapYear(1402) === false },
      1403: { isLeap: isPersianLeapYear(1403), expected: true, correct: isPersianLeapYear(1403) === true },
      1404: { isLeap: isPersianLeapYear(1404), expected: false, correct: isPersianLeapYear(1404) === false },
      1405: { isLeap: isPersianLeapYear(1405), expected: false, correct: isPersianLeapYear(1405) === false },
      1406: { isLeap: isPersianLeapYear(1406), expected: false, correct: isPersianLeapYear(1406) === false },
      1407: { isLeap: isPersianLeapYear(1407), expected: true, correct: isPersianLeapYear(1407) === true }
    });
  };
  
  // بارگذاری داده‌ها
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        console.log("Loading data...", { refreshTrigger });
        
        // بارگذاری داده‌ها
        const loadedFacilities = getFacilities();
        const loadedPayments = getPayments();
        
        console.log("Facilities:", loadedFacilities.length);
        console.log("Payments:", loadedPayments.length);
        
        // ذخیره داده‌ها در state
        setFacilities(loadedFacilities);
        setPayments(loadedPayments);
        
        // اطمینان از اینکه داده‌ها به درستی بارگذاری شده‌اند
        console.log("Data loaded successfully");
        
        // اجرای تست محاسبه روزها
        testDaysCalculation();
        
        setLoading(false);
        setError(null);
      } catch (err) {
        console.error("Error loading data:", err);
        setError("خطا در بارگذاری داده‌ها");
        setLoading(false);
      }
    };
    
    loadData();
  }, [refreshTrigger]); // اضافه کردن refreshTrigger به آرایه وابستگی‌ها

  // یافتن اطلاعات تسهیلات و قسط برای هر پرداخت
  const getFacilityInfo = (facilityId: number, installmentNumber: number) => {
    if (!facilities || facilities.length === 0) {
      return {
        bankName: "نامشخص",
        contractNumber: "نامشخص",
        interestRate: 0,
        penaltyRate: 0,
        dueDate: "نامشخص",
        receivedDate: "نامشخص"
      };
    }
    
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility) {
      console.warn(`Facility with ID ${facilityId} not found`);
      return {
        bankName: "نامشخص",
        contractNumber: "نامشخص",
        interestRate: 0,
        penaltyRate: 0,
        dueDate: "نامشخص",
        receivedDate: "نامشخص"
      };
    }
    
    const installment = facility.installments?.[installmentNumber - 1];
    
    return {
      bankName: facility.bankName || "نامشخص",
      contractNumber: facility.contractNumber || "نامشخص",
      interestRate: facility.interestRate || 0,
      penaltyRate: facility.penaltyRate || 0,
      dueDate: installment?.dueDate || "نامشخص",
      receivedDate: facility.receivedDate || "نامشخص" // تاریخ اخذ تسهیلات
    };
  };

  // محاسبه مانده در خط انحراف سود و جریمه برای هر قسط - روش ساده و مستقیم
  const calculateRemainingDeviations = () => {
    if (!payments || payments.length === 0 || !facilities || facilities.length === 0) {
      return [];
    }

    // اگر تسهیلات انتخاب شده، فقط پرداخت‌های آن تسهیلات را پردازش کن
    let paymentsToProcess = payments;
    if (selectedFacilityId && calculationStarted) {
      paymentsToProcess = payments.filter(p => p.facilityId === selectedFacilityId);
    }
    
    // گروه‌بندی پرداخت‌ها بر اساس تسهیلات و شماره قسط
    const paymentGroups: Record<string, Payment[]> = {};
    
    // مرتب‌سازی پرداخت‌ها بر اساس تاریخ (از قدیمی به جدید)
    const sortedPayments = [...paymentsToProcess].sort((a, b) => {
      const dateA = persianDateToDays(a.paymentDate);
      const dateB = persianDateToDays(b.paymentDate);
      return dateA - dateB;
    });
    
    // گروه‌بندی پرداخت‌ها
    sortedPayments.forEach(payment => {
      const key = `${payment.facilityId}-${payment.installmentNumber}`;
      if (!paymentGroups[key]) {
        paymentGroups[key] = [];
      }
      paymentGroups[key].push(payment);
    });
    
    // ساختار داده برای نگهداری مانده در خط انحراف برای هر گروه قسط
    const deviationBalances: Record<string, { interestBalance: number, penaltyBalance: number }> = {};
    
    // نتیجه نهایی
    const result: Payment[] = [];
    
    // پردازش هر پرداخت و محاسبه مانده در خط انحراف به صورت تجمعی
    for (const payment of sortedPayments) {
      const key = `${payment.facilityId}-${payment.installmentNumber}`;
      if (!deviationBalances[key]) {
        deviationBalances[key] = { interestBalance: 0, penaltyBalance: 0 };
      }
      const facility = facilities.find(f => f.id === payment.facilityId);
      if (!facility) {
        result.push({ ...payment, remainingInterestDeviation: 0, remainingPenaltyDeviation: 0 });
        continue;
      }
      // یافتن قسط
      // اصلاح: پیدا کردن قسط بر اساس ایندکس (شماره قسط - 1)
      const installment = facility.installments && facility.installments[payment.installmentNumber - 1];
      if (!installment) {
        result.push({ ...payment, remainingInterestDeviation: 0, remainingPenaltyDeviation: 0 });
        continue;
      }
      // محاسبه سود استاندارد
      let standardInterest = 0;
      let installmentPrincipal = 0;

      if (installment && installment.principalAmount) {
        installmentPrincipal = Number(installment.principalAmount) || 0;
      }

      // محاسبه سود استاندارد با فرمول صحیح
      if (payment.principalAmount && payment.principalAmount > 0) {
        // فرمول: سود استاندارد = (اصل پرداختی * نرخ سود / 100) / 365 * مدت زمان تسویه
        // مدت زمان تسویه شامل هر دو روز در تابع calculateDaysBetween محاسبه می‌شود
        const settlementDays = calculateDaysBetween(facility.receivedDate || "", payment.paymentDate);
        standardInterest = Math.round((payment.principalAmount * (facility.interestRate || 0) / 100) / 365 * settlementDays);
      } else {
        // اگر مبلغ اصل پرداختی صفر است، مانده انحراف قبلی را به عنوان سود استاندارد در نظر می‌گیریم
        // مانده قبلی همان سود استاندارد است (بدون تغییر علامت)
        const currentBalance = deviationBalances[key].interestBalance;
        standardInterest = Math.abs(currentBalance); // همیشه مقدار مطلق مانده قبلی
      }

      const interestDeviation = (payment.interestAmount || 0) - standardInterest;

      // برای پرداخت‌های بدون اصل، مانده را به‌روزرسانی می‌کنیم
      if (payment.principalAmount && payment.principalAmount > 0) {
        // پرداخت با اصل: انحراف را به مانده اضافه می‌کنیم
        deviationBalances[key].interestBalance += interestDeviation;
      } else {
        // پرداخت بدون اصل: مانده جدید = مانده قبلی + انحراف
        deviationBalances[key].interestBalance = deviationBalances[key].interestBalance + interestDeviation;
      }
      
      console.log(`Interest deviation for payment ${payment.id}:`, {
        facilityId: payment.facilityId,
        installmentNumber: payment.installmentNumber,
        paymentDate: payment.paymentDate,
        principalAmount: payment.principalAmount,
        interestAmount: payment.interestAmount,
        installmentPrincipal,
        interestRate: facility.interestRate,
        previousBalance: payment.principalAmount && payment.principalAmount > 0 ?
          deviationBalances[key].interestBalance - interestDeviation :
          deviationBalances[key].interestBalance - interestDeviation,
        standardInterest,
        interestDeviation,
        newBalance: deviationBalances[key].interestBalance,
        isZeroPrincipal: !payment.principalAmount || payment.principalAmount === 0,
        calculation: !payment.principalAmount || payment.principalAmount === 0 ?
          `مانده قبلی: ${deviationBalances[key].interestBalance - interestDeviation}, سود استاندارد: ${standardInterest}, سود پرداختی: ${payment.interestAmount}, انحراف: ${interestDeviation}` :
          'محاسبه معمولی'
      });
      
      // محاسبه جریمه استاندارد
      const delayDays = calculateDelayDays(installment.dueDate, payment.paymentDate);
      let standardPenalty = 0;

      // محاسبه جریمه استاندارد با فرمول صحیح
      if (payment.principalAmount && payment.principalAmount > 0) {
        // فرمول: جریمه استاندارد = (اصل پرداختی * نرخ جریمه / 100) / 365 * تاخیر
        // اگر تعجیل داشته باشد (delayDays <= 0) جریمه استاندارد صفر است
        if (delayDays > 0) {
          standardPenalty = Math.round((payment.principalAmount * (facility.penaltyRate || 0) / 100) / 365 * delayDays);
        } else {
          standardPenalty = 0; // تعجیل: جریمه استاندارد صفر
        }
      } else {
        // اگر مبلغ اصل پرداختی صفر است، مانده انحراف قبلی را به عنوان جریمه استاندارد در نظر می‌گیریم
        // مانده قبلی همان جریمه استاندارد است (بدون تغییر علامت)
        const currentBalance = deviationBalances[key].penaltyBalance;
        standardPenalty = Math.abs(currentBalance); // همیشه مقدار مطلق مانده قبلی
      }

      // محاسبه انحراف جریمه برای این پرداخت
      const penaltyDeviation = (payment.penaltyAmount || 0) - standardPenalty;

      // برای پرداخت‌های بدون اصل، مانده را به‌روزرسانی می‌کنیم
      if (payment.principalAmount && payment.principalAmount > 0) {
        // پرداخت با اصل: انحراف را به مانده اضافه می‌کنیم
        deviationBalances[key].penaltyBalance += penaltyDeviation;
      } else {
        // پرداخت بدون اصل: مانده جدید = مانده قبلی + انحراف
        deviationBalances[key].penaltyBalance = deviationBalances[key].penaltyBalance + penaltyDeviation;
      }
      
      console.log(`Penalty deviation for payment ${payment.id}:`, {
        facilityId: payment.facilityId,
        installmentNumber: payment.installmentNumber,
        paymentDate: payment.paymentDate,
        principalAmount: payment.principalAmount,
        penaltyAmount: payment.penaltyAmount,
        installmentPrincipal,
        penaltyRate: facility.penaltyRate,
        delayDays,
        standardPenalty,
        penaltyDeviation,
        balanceBefore: payment.principalAmount && payment.principalAmount > 0 ?
          deviationBalances[key].penaltyBalance - penaltyDeviation :
          deviationBalances[key].penaltyBalance - penaltyDeviation,
        balanceAfter: deviationBalances[key].penaltyBalance,
        isZeroPrincipal: !payment.principalAmount || payment.principalAmount === 0
      });
      
      // لاگ برای بررسی
      console.log(`Payment ${payment.id} deviations:`, {
        facilityId: payment.facilityId,
        installmentNumber: payment.installmentNumber,
        paymentDate: payment.paymentDate,
        principalAmount: payment.principalAmount,
        interestAmount: payment.interestAmount,
        standardInterest,
        interestDeviation,
        currentInterestBalance: deviationBalances[key].interestBalance,
        penaltyAmount: payment.penaltyAmount,
        standardPenalty,
        penaltyDeviation,
        currentPenaltyBalance: deviationBalances[key].penaltyBalance
      });
      
      // افزودن پرداخت با مانده در خط انحراف به نتیجه
      result.push({
        ...payment,
        remainingInterestDeviation: deviationBalances[key].interestBalance,
        remainingPenaltyDeviation: deviationBalances[key].penaltyBalance
      });
    }
    
    // مرتب‌سازی نتیجه بر اساس ترتیب اصلی پرداخت‌ها
    const paymentIdMap = new Map(paymentsToProcess.map((p, index) => [p.id, index]));

    return result.sort((a, b) => {
      const indexA = paymentIdMap.get(a.id) || 0;
      const indexB = paymentIdMap.get(b.id) || 0;
      return indexA - indexB;
    });
  };
  
  // پرداخت‌ها با محاسبه مانده در خط انحراف
  const paymentsWithDeviations = useMemo(() => {
    return calculateRemainingDeviations();
  }, [payments, facilities, selectedFacilityId, calculationStarted]);
  
  // فیلتر کردن پرداخت‌ها بر اساس تسهیلات انتخابی و جستجو
  const filteredPayments = useMemo(() => {
    if (!paymentsWithDeviations || paymentsWithDeviations.length === 0 || !facilities || facilities.length === 0) {
      return [];
    }

    // اگر محاسبات شروع نشده، هیچ پرداختی نمایش نده
    if (!calculationStarted || !selectedFacilityId) {
      return [];
    }

    // فیلتر بر اساس تسهیلات انتخابی
    let facilityPayments = paymentsWithDeviations.filter(payment =>
      payment.facilityId === selectedFacilityId
    );

    // اگر عبارت جستجو خالی باشد، همه پرداخت‌های تسهیلات انتخابی را نمایش بده
    if (!searchTerm.trim()) {
      return facilityPayments;
    }

    const term = searchTerm.trim().toLowerCase();

    return facilityPayments.filter(payment => {
      // یافتن تسهیلات مربوط به این پرداخت
      const facility = facilities.find(f => f.id === payment.facilityId);
      if (!facility) return false;

      // جستجو در نام بانک
      if (facility.bankName.toLowerCase().includes(term)) return true;

      // جستجو در شماره تسهیلات
      if (facility.contractNumber.toLowerCase().includes(term)) return true;

      // جستجو در شماره قسط
      if (payment.installmentNumber.toString().includes(term)) return true;

      // جستجو در تاریخ پرداخت
      if (payment.paymentDate.toLowerCase().includes(term)) return true;

      return false;
    });
  }, [paymentsWithDeviations, facilities, searchTerm, selectedFacilityId, calculationStarted]);
  
  // پاک کردن جستجو
  const clearSearch = () => {
    setSearchTerm("");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">کنترل تسهیلات - روش اول</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={exportToExcel}
            className="flex items-center gap-2"
            disabled={!calculationStarted || filteredPayments.length === 0}
          >
            <FileText className="h-4 w-4" />
            دریافت Excel
          </Button>
          <Button
            variant="outline"
            onClick={printAsPDF}
            className="flex items-center gap-2"
            disabled={!calculationStarted || filteredPayments.length === 0}
          >
            <Printer className="h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={onBack}>
            بازگشت
          </Button>
        </div>
      </div>

      {/* انتخاب تسهیلات */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            انتخاب تسهیلات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="facility-select">انتخاب تسهیلات:</Label>
                <Select value={selectedFacilityId?.toString() || ""} onValueChange={handleFacilitySelect}>
                  <SelectTrigger id="facility-select">
                    <SelectValue placeholder="تسهیلات مورد نظر را انتخاب کنید..." />
                  </SelectTrigger>
                  <SelectContent>
                    {facilities.map((facility) => (
                      <SelectItem key={facility.id} value={facility.id.toString()}>
                        <div className="flex flex-col space-y-1 text-right w-full">
                          <div className="font-medium">{facility.bankName} - {facility.contractNumber}</div>
                          <div className="text-sm text-gray-500">
                            مبلغ: {formatCurrency(facility.amount)} |
                            تعداد اقساط: {facility.installments?.length || 0}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end gap-2">
                <Button
                  onClick={startCalculations}
                  disabled={!selectedFacilityId || isCalculating}
                  className="flex items-center gap-2 flex-1"
                >
                  <Calculator className="h-4 w-4" />
                  {isCalculating ? "در حال محاسبه..." : "شروع محاسبات"}
                </Button>

                {selectedFacilityId && (
                  <Button
                    variant="outline"
                    onClick={clearFacilitySelection}
                    disabled={isCalculating}
                  >
                    پاک کردن
                  </Button>
                )}
              </div>
            </div>

            {/* نمایش اطلاعات تسهیلات انتخابی */}
            {selectedFacilityId && calculationStarted && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                {(() => {
                  const selectedFacility = facilities.find(f => f.id === selectedFacilityId);
                  if (!selectedFacility) return null;

                  return (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-blue-800">نام بانک:</span>
                        <div className="text-blue-600">{selectedFacility.bankName}</div>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">شماره تسهیلات:</span>
                        <div className="text-blue-600">{selectedFacility.contractNumber}</div>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">مبلغ وام:</span>
                        <div className="text-blue-600">{formatCurrency(selectedFacility.amount)}</div>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">تعداد اقساط:</span>
                        <div className="text-blue-600">{selectedFacility.installments?.length || 0}</div>
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      {/* جستجو - فقط زمانی که محاسبات شروع شده */}
      {calculationStarted && selectedFacilityId && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">جستجو در پرداخت‌های تسهیلات انتخابی</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-grow">
                <Input
                  placeholder="جستجو در شماره قسط یا تاریخ پرداخت"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <Button variant="outline" onClick={clearSearch} className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                پاک کردن جستجو
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      
      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">در حال بارگذاری...</div>
          </CardContent>
        </Card>
      ) : error ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-500">{error}</div>
          </CardContent>
        </Card>
      ) : (!payments || payments.length === 0) ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              هیچ پرداختی در سیستم ثبت نشده است. لطفاً ابتدا پرداخت‌ها را از بخش "مدیریت پرداخت‌ها" اضافه کنید.
            </div>
          </CardContent>
        </Card>
      ) : (!facilities || facilities.length === 0) ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              هیچ تسهیلاتی در سیستم ثبت نشده است. لطفاً ابتدا تسهیلات را از بخش "مدیریت تسهیلات" اضافه کنید.
            </div>
          </CardContent>
        </Card>
      ) : !selectedFacilityId ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-blue-600">
              <Building2 className="h-12 w-12 mx-auto mb-4 text-blue-400" />
              <h3 className="text-lg font-medium mb-2">انتخاب تسهیلات</h3>
              <p className="text-gray-500">
                لطفاً ابتدا یک تسهیلات از لیست بالا انتخاب کنید و سپس دکمه "شروع محاسبات" را فشار دهید.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : !calculationStarted ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-orange-600">
              <Calculator className="h-12 w-12 mx-auto mb-4 text-orange-400" />
              <h3 className="text-lg font-medium mb-2">آماده محاسبه</h3>
              <p className="text-gray-500">
                تسهیلات انتخاب شده است. برای شروع محاسبات دکمه "شروع محاسبات" را فشار دهید.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : isCalculating ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium mb-2">در حال محاسبه...</h3>
              <p className="text-gray-500">
                لطفاً صبر کنید، محاسبات در حال انجام است.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : filteredPayments.length === 0 ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              هیچ پرداختی برای تسهیلات انتخابی یافت نشد.
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>
              {(() => {
                const selectedFacility = facilities.find(f => f.id === selectedFacilityId);
                return selectedFacility
                  ? `لیست پرداخت‌ها - ${selectedFacility.bankName} (${selectedFacility.contractNumber})`
                  : "لیست پرداخت‌ها";
              })()}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table className="border-collapse border border-gray-300">
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right border border-gray-300">نام بانک</TableHead>
                    <TableHead className="text-right border border-gray-300">شماره تسهیلات</TableHead>
                    <TableHead className="text-right border border-gray-300">شماره قسط</TableHead>
                    <TableHead className="text-right border border-gray-300">تاریخ اخذ تسهیلات</TableHead>
                    <TableHead className="text-right border border-gray-300">تاریخ سررسید</TableHead>
                    <TableHead className="text-right border border-gray-300">تاریخ پرداخت</TableHead>
                    <TableHead className="text-right border border-gray-300">تاخیر/تعجیل</TableHead>
                    <TableHead className="text-right border border-gray-300">مدت زمان تسویه</TableHead>
                    <TableHead className="text-right border border-gray-300">نرخ سود</TableHead>
                    <TableHead className="text-right border border-gray-300">نرخ جریمه</TableHead>
                    <TableHead className="text-right border border-gray-300">اصل پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">سود پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">سود استاندارد</TableHead>
                    <TableHead className="text-right border border-gray-300">انحراف سود</TableHead>
                    <TableHead className="text-right border border-gray-300">جریمه پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">جریمه استاندارد</TableHead>
                    <TableHead className="text-right border border-gray-300">انحراف جریمه</TableHead>
                    <TableHead className="text-right border border-gray-300">مجموع پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">مانده انحراف سود</TableHead>
                    <TableHead className="text-right border border-gray-300">مانده انحراف جریمه</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {!filteredPayments || filteredPayments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={20} className="text-center">
                        هیچ پرداختی یافت نشد
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPayments.map((payment) => {
                      const { bankName, contractNumber, interestRate, penaltyRate, dueDate, receivedDate } = 
                        getFacilityInfo(payment.facilityId, payment.installmentNumber);
                      
                      // محاسبه تاخیر/تعجیل
                      const delayDays = calculateDelayDays(dueDate, payment.paymentDate);
                      
                      // محاسبه مدت زمان تسویه (شامل هر دو روز در تابع)
                      const settlementDays = calculateDaysBetween(receivedDate, payment.paymentDate);
                      
                      // برای تست محاسبه روزها
                      console.log("Settlement Days Calculation:", {
                        facilityId: payment.facilityId,
                        installmentNumber: payment.installmentNumber,
                        receivedDate,
                        paymentDate: payment.paymentDate,
                        rawDays: calculateDaysBetween(receivedDate, payment.paymentDate),
                        settlementDays,
                        note: "شامل روز اخذ و روز پرداخت"
                      });
                      
                      // محاسبه سود استاندارد با فرمول صحیح
                      let standardInterest = 0;

                      if (payment.principalAmount && payment.principalAmount > 0) {
                        // فرمول: سود استاندارد = (اصل پرداختی * نرخ سود / 100) / 365 * مدت زمان تسویه
                        standardInterest = Math.round((payment.principalAmount * (interestRate / 100) / 365) * settlementDays);
                      } else {
                        // اگر مبلغ اصل صفر است، از مانده در خط انحراف سود استفاده می‌کنیم
                        // مقدار مطلق مانده در خط انحراف را استفاده می‌کنیم
                        const remainingDeviation = payment.remainingInterestDeviation || 0;
                        standardInterest = Math.abs(remainingDeviation);
                      }
                      
                      console.log("Interest Calculation (Display):", {
                        principalAmount: payment.principalAmount,
                        interestRate,
                        receivedDate,
                        paymentDate: payment.paymentDate,
                        settlementDays,
                        formula: payment.principalAmount && payment.principalAmount > 0 ?
                          `(${payment.principalAmount} * ${interestRate} / 100) / 365 * ${settlementDays}` :
                          `مانده قبلی: ${payment.remainingInterestDeviation}`,
                        standardInterest,
                        remainingInterestDeviation: payment.remainingInterestDeviation
                      });
                      
                      // محاسبه انحراف سود
                      const interestDeviation = (payment.interestAmount || 0) - standardInterest;
                      
                      // محاسبه جریمه استاندارد با فرمول صحیح
                      let standardPenalty = 0;

                      if (payment.principalAmount && payment.principalAmount > 0) {
                        // فرمول: جریمه استاندارد = (اصل پرداختی * نرخ جریمه / 100) / 365 * تاخیر
                        // اگر تعجیل داشته باشد (delayDays <= 0) جریمه استاندارد صفر است
                        if (delayDays > 0) {
                          standardPenalty = Math.round((payment.principalAmount * (penaltyRate / 100) / 365) * delayDays);
                        } else {
                          standardPenalty = 0; // تعجیل: جریمه استاندارد صفر
                        }
                      } else {
                        // اگر مبلغ اصل صفر است، از مانده در خط انحراف جریمه استفاده می‌کنیم
                        // مقدار مطلق مانده در خط انحراف را استفاده می‌کنیم
                        const remainingDeviation = payment.remainingPenaltyDeviation || 0;
                        standardPenalty = Math.abs(remainingDeviation);
                      }
                      
                      // محاسبه دستی برای مقایسه با مقادیر مورد انتظار
                      // برای مثال با اصل 250,000,000 ریال، نرخ جریمه 6% و 10 روز تاخیر
                      const manualCalculation = Math.round(250000000 * ((6/100)/365) * 10);
                      
                      console.log("Penalty Calculation (Display):", {
                        principalAmount: payment.principalAmount,
                        penaltyRate,
                        delayDays,
                        dailyRate: (penaltyRate / 100) / 365,
                        formula: payment.principalAmount && payment.principalAmount > 0 ?
                          delayDays > 0 ?
                            `(${payment.principalAmount} * ${penaltyRate} / 100) / 365 * ${delayDays}` :
                            'تعجیل: جریمه صفر' :
                          `مانده قبلی: ${payment.remainingPenaltyDeviation}`,
                        standardPenalty,
                        manualCalculation,
                        isAdvancePayment: delayDays <= 0
                      });
                      
                      // محاسبه انحراف جریمه
                      const penaltyDeviation = (payment.penaltyAmount || 0) - standardPenalty;
                      
                      return (
                        <TableRow key={payment.id}>
                          <TableCell>{bankName}</TableCell>
                          <TableCell>{contractNumber}</TableCell>
                          <TableCell>{payment.installmentNumber}</TableCell>
                          <TableCell>{receivedDate}</TableCell>
                          <TableCell>{dueDate}</TableCell>
                          <TableCell>{payment.paymentDate}</TableCell>
                          <TableCell className={delayDays > 0 ? "text-red-500 font-bold" : delayDays < 0 ? "text-green-500 font-bold" : ""}>
                            {delayDays}
                          </TableCell>
                          <TableCell className="font-bold">
                            {settlementDays}
                          </TableCell>
                          <TableCell>{interestRate}%</TableCell>
                          <TableCell>{penaltyRate}%</TableCell>
                          <TableCell>{formatCurrency(payment.principalAmount)}</TableCell>
                          <TableCell>{formatCurrency(payment.interestAmount)}</TableCell>
                          <TableCell>{formatCurrency(standardInterest)}</TableCell>
                          <TableCell className={interestDeviation > 0 ? "text-red-500 font-bold" : interestDeviation < 0 ? "text-green-500 font-bold" : ""}>
                            {formatCurrency(interestDeviation)}
                          </TableCell>
                          <TableCell>{formatCurrency(payment.penaltyAmount)}</TableCell>
                          <TableCell>{formatCurrency(standardPenalty)}</TableCell>
                          <TableCell className={penaltyDeviation > 0 ? "text-red-500 font-bold" : penaltyDeviation < 0 ? "text-green-500 font-bold" : ""}>
                            {formatCurrency(penaltyDeviation)}
                          </TableCell>
                          <TableCell>{formatCurrency(payment.totalAmount)}</TableCell>
                          <TableCell className={payment.remainingInterestDeviation < 0 ? "text-red-500 font-bold" : "text-blue-500 font-bold"}>
                            {formatCurrency(payment.remainingInterestDeviation || 0)}
                          </TableCell>
                          <TableCell className={payment.remainingPenaltyDeviation < 0 ? "text-red-500 font-bold" : "text-blue-500 font-bold"}>
                            {formatCurrency(payment.remainingPenaltyDeviation || 0)}
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ControlMethod1;