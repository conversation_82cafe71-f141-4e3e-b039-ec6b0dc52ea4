import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { getFacilities, getPayments } from "@/utils/storage";
import { Facility, Payment } from "@/types/types";
import { formatCurrency } from "@/utils/formatters";
import { persianToDate } from "@/utils/jalali";
import { FileText, Printer, Search } from "lucide-react";
import * as XLSX from 'xlsx';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ControlMethod1Props {
  onBack: () => void;
  refreshTrigger?: number; // پارامتر جدید برای تریگر به‌روزرسانی
}

const ControlMethod1 = ({ onBack, refreshTrigger = 0 }: ControlMethod1Props) => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // جستجو
  const [searchTerm, setSearchTerm] = useState<string>("");
  
  // صادر کردن به اکسل
  const exportToExcel = () => {
    if (!filteredPayments || filteredPayments.length === 0) return;
    // گروه‌بندی پرداخت‌ها بر اساس تسهیلات و شماره قسط
    const paymentGroups: Record<string, typeof filteredPayments> = {};
    filteredPayments.forEach(payment => {
      const key = `${payment.facilityId}-${payment.installmentNumber}`;
      if (!paymentGroups[key]) paymentGroups[key] = [];
      paymentGroups[key].push(payment);
    });
    // تبدیل داده‌های پرداخت به فرمت مناسب برای اکسل
    const excelData = filteredPayments.map(payment => {
      const { bankName, contractNumber, interestRate, penaltyRate, dueDate, receivedDate } = getFacilityInfo(payment.facilityId, payment.installmentNumber);
      // محاسبه تاخیر/تعجیل
      const delayDays = calculateDelayDays(dueDate, payment.paymentDate);
      // محاسبه سود استاندارد فقط برای بازه بین پرداخت قبلی و فعلی
      const key = `${payment.facilityId}-${payment.installmentNumber}`;
      const groupPayments = paymentGroups[key] || [];
      const currentIndex = groupPayments.findIndex(p => p.id === payment.id);
      let prevPaymentDate = receivedDate;
      if (currentIndex > 0) prevPaymentDate = groupPayments[currentIndex - 1].paymentDate;
      const days = calculateDaysBetween(prevPaymentDate, payment.paymentDate);
      let installmentPrincipal = 0;
      if (groupPayments.length > 0) {
        const facility = facilities.find(f => f.id === payment.facilityId);
        const installment = facility && facility.installments && facility.installments[payment.installmentNumber - 1];
        if (installment && installment.principalAmount) {
          installmentPrincipal = Number(installment.principalAmount) || 0;
        }
      }
      const standardInterest = Math.round((installmentPrincipal * (interestRate || 0) / 100) / 365 * days);
      // محاسبه انحراف سود
      const interestDeviation = (payment.interestAmount || 0) - standardInterest;
      // محاسبه جریمه استاندارد (در صورت نیاز همین منطق را می‌توان برای penalty هم تعمیم داد)
      const standardPenalty = 0; // فعلاً بدون تغییر
      const penaltyDeviation = (payment.penaltyAmount || 0) - standardPenalty;
      return {
        'نام بانک': bankName,
        'شماره تسهیلات': contractNumber,
        'شماره قسط': payment.installmentNumber,
        'تاریخ اخذ تسهیلات': receivedDate,
        'تاریخ سررسید': dueDate,
        'تاریخ پرداخت': payment.paymentDate,
        'تاخیر/تعجیل': delayDays,
        'مدت زمان تسویه': days,
        'نرخ سود': interestRate,
        'نرخ جریمه': penaltyRate,
        'اصل پرداختی': payment.principalAmount || 0,
        'سود پرداختی': payment.interestAmount || 0,
        'سود استاندارد': standardInterest,
        'انحراف سود': interestDeviation,
        'جریمه پرداختی': payment.penaltyAmount || 0,
        'جریمه استاندارد': standardPenalty,
        'انحراف جریمه': penaltyDeviation,
        'مجموع پرداختی': payment.totalAmount || 0
      };
    });
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "کنترل تسهیلات");
    const maxWidth = 20;
    const cols = Object.keys(excelData[0]).map(() => ({ wch: maxWidth }));
    worksheet['!cols'] = cols;
    XLSX.writeFile(workbook, "کنترل-تسهیلات-روش-اول.xlsx");
  };
  
  // چاپ به صورت PDF
  const printAsPDF = () => {
    window.print();
  };
  
  // تبدیل تاریخ شمسی به تعداد روز
  const persianDateToDays = (date: string): number => {
    try {
      if (!date || date === "نامشخص") return 0;
      
      const [year, month, day] = date.split('/').map(Number);
      // استفاده از تابع persianToDate برای تبدیل صحیح تاریخ شمسی به میلادی
      const dateObj = persianToDate(year, month, day);
      return Math.floor(dateObj.getTime() / (24 * 60 * 60 * 1000));
    } catch (error) {
      console.error("Error converting date:", error, date);
      return 0;
    }
  };
  
  // محاسبه تعداد روزهای بین دو تاریخ شمسی
  const calculateDaysBetween = (startDate: string, endDate: string): number => {
    try {
      if (!startDate || startDate === "نامشخص" || !endDate || endDate === "نامشخص") {
        console.log("Invalid dates for days calculation:", { startDate, endDate });
        return 0;
      }
      
      // تبدیل تاریخ‌های شمسی به اجزای سال، ماه و روز
      const [startYearStr, startMonthStr, startDayStr] = startDate.split('/');
      const [endYearStr, endMonthStr, endDayStr] = endDate.split('/');
      
      // تبدیل به اعداد
      const startYear = parseInt(startYearStr);
      const startMonth = parseInt(startMonthStr);
      const startDay = parseInt(startDayStr);
      
      const endYear = parseInt(endYearStr);
      const endMonth = parseInt(endMonthStr);
      const endDay = parseInt(endDayStr);
      
      // بررسی اعتبار تاریخ‌ها
      if (isNaN(startYear) || isNaN(startMonth) || isNaN(startDay) || 
          isNaN(endYear) || isNaN(endMonth) || isNaN(endDay)) {
        console.error("Invalid date format:", { startDate, endDate });
        return 0;
      }
      
      // محاسبه دقیق روزها برای مثال 1402/12/25 تا 1403/01/30
      
      // اگر سال یکسان باشد
      if (startYear === endYear) {
        // اگر ماه یکسان باشد
        if (startMonth === endMonth) {
          return endDay - startDay;
        }
        
        // اگر ماه‌ها متفاوت باشند
        let days = 0;
        
        // روزهای باقیمانده از ماه اول
        const daysInMonth = startMonth <= 6 ? 31 : (startMonth <= 11 ? 30 : 29);
        days += (daysInMonth - startDay);
        
        // روزهای ماه‌های کامل بین
        for (let m = startMonth + 1; m < endMonth; m++) {
          days += m <= 6 ? 31 : (m <= 11 ? 30 : 29);
        }
        
        // روزهای ماه آخر
        days += endDay;
        
        return days;
      }
      
      // اگر سال‌ها متفاوت باشند
      let days = 0;
      
      // روزهای باقیمانده از سال اول
      // روزهای باقیمانده از ماه اول
      const daysInStartMonth = startMonth <= 6 ? 31 : (startMonth <= 11 ? 30 : 29);
      days += (daysInStartMonth - startDay);
      
      // روزهای ماه‌های کامل در سال اول
      for (let m = startMonth + 1; m <= 12; m++) {
        days += m <= 6 ? 31 : (m <= 11 ? 30 : 29);
      }
      
      // روزهای سال‌های کامل بین
      for (let y = startYear + 1; y < endYear; y++) {
        days += 365; // در اینجا کبیسه را در نظر نمی‌گیریم برای سادگی
      }
      
      // روزهای ماه‌های کامل در سال آخر
      for (let m = 1; m < endMonth; m++) {
        days += m <= 6 ? 31 : (m <= 11 ? 30 : 29);
      }
      
      // روزهای ماه آخر
      days += endDay;
      
      return days;
    } catch (error) {
      console.error("Error calculating days between dates:", error, { startDate, endDate });
      return 0;
    }
  };

  // محاسبه تعداد روزهای تاخیر یا تعجیل
  const calculateDelayDays = (dueDate: string, paymentDate: string): number => {
    try {
      if (!dueDate || dueDate === "نامشخص" || !paymentDate) {
        console.log("Invalid dates for delay calculation:", { dueDate, paymentDate });
        return 0;
      }
      
      const dueDays = persianDateToDays(dueDate);
      const paymentDays = persianDateToDays(paymentDate);
      
      if (dueDays === 0 || paymentDays === 0) {
        console.log("Zero days calculated for:", { dueDate, paymentDate, dueDays, paymentDays });
        return 0;
      }
      
      return paymentDays - dueDays; // مثبت = تاخیر، منفی = تعجیل
    } catch (error) {
      console.error("Error calculating delay days:", error, { dueDate, paymentDate });
      return 0;
    }
  };
  
  // محاسبه جریمه تاخیر
  const calculatePenalty = (
    principalAmount: number,
    penaltyRate: number,
    delayDays: number
  ): number => {
    if (delayDays <= 0) return 0;
    
    // نرخ جریمه سالانه به روزانه
    // تبدیل درصد به عدد اعشاری (تقسیم بر 100) و سپس تبدیل سالانه به روزانه (تقسیم بر 365)
    const dailyRate = (penaltyRate / 100) / 365;
    
    // محاسبه جریمه
    return Math.round(principalAmount * dailyRate * delayDays);
  };
  
  // محاسبه جریمه تاخیر با فرمول دقیق
  const calculateStandardPenalty = (
    principalAmount: number,
    penaltyRate: number,
    delayDays: number,
    facilityId: number,
    installmentNumber: number,
    currentPaymentId?: number, // شناسه پرداخت فعلی
    currentPaymentDate?: string // تاریخ پرداخت فعلی
  ): number => {
    // اگر تعجیل وجود داشته باشد (delayDays ≤ 0)، جریمه استاندارد = 0
    if (delayDays <= 0) return 0;
    
    // اگر مبلغ اصل صفر نیست، از روش معمولی استفاده می‌کنیم
    if (principalAmount > 0) {
      // فرمول دقیق برای محاسبه جریمه استاندارد:
      // (مبلغ پرداختی × نرخ جریمه) / 365 × تعداد روز تاخیر
      const standardPenalty = (principalAmount * penaltyRate / 100) / 365 * delayDays;
      return Math.round(standardPenalty);
    }
    
    // اگر مبلغ اصل صفر است، از روش جدید استفاده می‌کنیم
    // یافتن تسهیلات مربوطه
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility) {
      console.log("Facility not found for penalty:", facilityId);
      return 0;
    }
    
    // یافتن قسط مربوطه
    const installment = facility.installments?.find(i => i.installmentNumber === installmentNumber);
    if (!installment) {
      console.log("Installment not found for penalty:", installmentNumber);
      return 0;
    }
    
    // مبلغ اصل قسط را از اطلاعات تسهیلات بدست می‌آوریم
    const installmentPrincipal = installment.amount || 0;
    
    // اگر پرداخت فعلی اولین پرداخت برای این قسط است
    if (!currentPaymentId) {
      // محاسبه جریمه استاندارد کل تا تاریخ پرداخت فعلی
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided for penalty");
        return 0;
      }
      
      // محاسبه تعداد روزهای تاخیر برای پرداخت فعلی
      const currentDelayDays = calculateDelayDays(installment.dueDate, paymentDate);
      if (currentDelayDays <= 0) return 0; // اگر تاخیر نداشته باشد
      
      // محاسبه جریمه استاندارد کل
      const totalStandardPenalty = (installmentPrincipal * penaltyRate / 100) / 365 * currentDelayDays;
      
      console.log("First payment penalty calculation:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        currentDelayDays,
        totalStandardPenalty: Math.round(totalStandardPenalty)
      });
      
      return Math.round(totalStandardPenalty);
    }
    
    // یافتن تمام پرداخت‌های این قسط
    const allPayments = payments.filter(p => 
      p.facilityId === facilityId && 
      p.installmentNumber === installmentNumber
    );
    
    console.log("All payments for penalty calculation:", allPayments.length, 
      "Current payment ID:", currentPaymentId,
      "Payment IDs:", allPayments.map(p => p.id));
    
    // پیدا کردن پرداخت فعلی
    const currentPayment = allPayments.find(p => p.id === currentPaymentId);
    if (!currentPayment) {
      console.log("Current payment not found in payments list for penalty");
      
      // اگر پرداخت فعلی پیدا نشد، از تاریخ پرداخت ارسالی استفاده می‌کنیم
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided for penalty");
        return 0;
      }
      
      // محاسبه مجموع جریمه پرداخت شده در تمام پرداخت‌های قبلی
      const totalPaidPenalty = allPayments.reduce((sum, p) => sum + (p.penaltyAmount || 0), 0);
      
      // محاسبه تعداد روزهای تاخیر برای پرداخت فعلی
      const currentDelayDays = calculateDelayDays(installment.dueDate, paymentDate);
      if (currentDelayDays <= 0) return 0; // اگر تاخیر نداشته باشد
      
      // محاسبه جریمه استاندارد کل تا تاریخ پرداخت فعلی
      const totalStandardPenalty = (installmentPrincipal * penaltyRate / 100) / 365 * currentDelayDays;
      
      // اگر هنوز جریمه استاندارد باقی مانده است که باید پرداخت شود
      if (totalPaidPenalty < totalStandardPenalty) {
        const remainingStandardPenalty = Math.round(totalStandardPenalty) - totalPaidPenalty;
        
        console.log("Penalty calculation without current payment:", {
          facilityId,
          installmentNumber,
          installmentPrincipal,
          paymentDate,
          currentDelayDays,
          totalPaidPenalty,
          totalStandardPenalty: Math.round(totalStandardPenalty),
          remainingStandardPenalty
        });
        
        return Math.round(remainingStandardPenalty);
      }
      
      return 0;
    }
    
    // مرتب‌سازی پرداخت‌ها بر اساس تاریخ
    const sortedPayments = [...allPayments].sort((a, b) => {
      const dateA = persianDateToDays(a.paymentDate);
      const dateB = persianDateToDays(b.paymentDate);
      return dateA - dateB;
    });
    
    // پیدا کردن ایندکس پرداخت فعلی در لیست مرتب شده
    const currentPaymentIndex = sortedPayments.findIndex(p => p.id === currentPaymentId);
    if (currentPaymentIndex === -1) {
      console.log("Current payment not found in sorted payments for penalty");
      return 0;
    }
    
    // پرداخت‌های قبلی (قبل از پرداخت فعلی)
    const previousPayments = sortedPayments.slice(0, currentPaymentIndex);
    
    // محاسبه مجموع جریمه پرداخت شده در پرداخت‌های قبلی
    const totalPaidPenalty = previousPayments.reduce((sum, p) => sum + (p.penaltyAmount || 0), 0);
    
    // تاریخ پرداخت فعلی
    const paymentDate = currentPaymentDate || currentPayment.paymentDate;
    
    // محاسبه تعداد روزهای تاخیر برای پرداخت فعلی
    const currentDelayDays = calculateDelayDays(installment.dueDate, paymentDate);
    if (currentDelayDays <= 0) return 0; // اگر تاخیر نداشته باشد
    
    // محاسبه جریمه استاندارد کل تا تاریخ پرداخت فعلی
    const totalStandardPenalty = (installmentPrincipal * penaltyRate / 100) / 365 * currentDelayDays;
    
    // اگر هنوز جریمه استاندارد باقی مانده است که باید پرداخت شود
    if (totalPaidPenalty < totalStandardPenalty) {
      const remainingStandardPenalty = Math.round(totalStandardPenalty) - totalPaidPenalty;
      
      console.log("Penalty calculation for zero principal:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        currentDelayDays,
        totalPaidPenalty,
        totalStandardPenalty: Math.round(totalStandardPenalty),
        remainingStandardPenalty
      });
      
      return Math.round(remainingStandardPenalty);
    }
    
    // اگر بیشتر از جریمه استاندارد پرداخت شده، صفر برگردان
    return 0;
  };
  
  // محاسبه سود استاندارد
  const calculateStandardInterest = (
    principalAmount: number,
    interestRate: number,
    dayCount: number,
    facilityId: number,
    installmentNumber: number,
    currentPaymentId?: number, // شناسه پرداخت فعلی
    currentPaymentDate?: string // تاریخ پرداخت فعلی
  ): number => {
    if (dayCount <= 0) return 0;
    
    // اگر مبلغ اصل صفر نیست، از روش معمولی استفاده می‌کنیم
    if (principalAmount > 0) {
      // محاسبه معمول سود استاندارد برای حالتی که مبلغ اصل وجود دارد
      const standardInterest = (principalAmount * (interestRate / 100) / 365) * dayCount;
      return Math.round(standardInterest);
    }
    
    // اگر مبلغ اصل صفر است، از روش جدید استفاده می‌کنیم
    // یافتن تسهیلات مربوطه
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility) {
      console.log("Facility not found:", facilityId);
      return 0;
    }
    
    // یافتن قسط مربوطه
    const installment = facility.installments?.find(i => i.installmentNumber === installmentNumber);
    if (!installment) {
      console.log("Installment not found:", installmentNumber);
      return 0;
    }
    
    // مبلغ اصل قسط را از اطلاعات تسهیلات بدست می‌آوریم
    const installmentPrincipal = installment.amount || 0;
    
    // اگر پرداخت فعلی اولین پرداخت برای این قسط است
    if (!currentPaymentId) {
      // محاسبه سود استاندارد کل تا تاریخ پرداخت فعلی
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided");
        return 0;
      }
      
      const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", paymentDate);
      const totalStandardInterest = (installmentPrincipal * (interestRate / 100) / 365) * daysSinceReceived;
      
      console.log("First payment interest calculation:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        daysSinceReceived,
        totalStandardInterest: Math.round(totalStandardInterest)
      });
      
      return Math.round(totalStandardInterest);
    }
    
    // یافتن تمام پرداخت‌های این قسط
    const allPayments = payments.filter(p => 
      p.facilityId === facilityId && 
      p.installmentNumber === installmentNumber
    );
    
    console.log("All payments for this installment:", allPayments.length, 
      "Current payment ID:", currentPaymentId,
      "Payment IDs:", allPayments.map(p => p.id));
    
    // پیدا کردن پرداخت فعلی
    const currentPayment = allPayments.find(p => p.id === currentPaymentId);
    if (!currentPayment) {
      console.log("Current payment not found in payments list");
      
      // اگر پرداخت فعلی پیدا نشد، از تاریخ پرداخت ارسالی استفاده می‌کنیم
      const paymentDate = currentPaymentDate || "";
      if (!paymentDate) {
        console.log("No payment date provided");
        return 0;
      }
      
      // محاسبه مجموع سود پرداخت شده در تمام پرداخت‌های قبلی
      const totalPaidInterest = allPayments.reduce((sum, p) => sum + (p.interestAmount || 0), 0);
      
      // محاسبه سود استاندارد کل تا تاریخ پرداخت فعلی
      const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", paymentDate);
      const totalStandardInterest = (installmentPrincipal * (interestRate / 100) / 365) * daysSinceReceived;
      
      // اگر هنوز سود استاندارد باقی مانده است که باید پرداخت شود
      if (totalPaidInterest < totalStandardInterest) {
        const remainingStandardInterest = Math.round(totalStandardInterest) - totalPaidInterest;
        
        console.log("Interest calculation without current payment:", {
          facilityId,
          installmentNumber,
          installmentPrincipal,
          paymentDate,
          daysSinceReceived,
          totalPaidInterest,
          totalStandardInterest: Math.round(totalStandardInterest),
          remainingStandardInterest
        });
        
        return Math.round(remainingStandardInterest);
      }
      
      return 0;
    }
    
    // مرتب‌سازی پرداخت‌ها بر اساس تاریخ
    const sortedPayments = [...allPayments].sort((a, b) => {
      const dateA = persianDateToDays(a.paymentDate);
      const dateB = persianDateToDays(b.paymentDate);
      return dateA - dateB;
    });
    
    // پیدا کردن ایندکس پرداخت فعلی در لیست مرتب شده
    const currentPaymentIndex = sortedPayments.findIndex(p => p.id === currentPaymentId);
    if (currentPaymentIndex === -1) {
      console.log("Current payment not found in sorted payments");
      return 0;
    }
    
    // پرداخت‌های قبلی (قبل از پرداخت فعلی)
    const previousPayments = sortedPayments.slice(0, currentPaymentIndex);
    
    // محاسبه مجموع سود پرداخت شده در پرداخت‌های قبلی
    const totalPaidInterest = previousPayments.reduce((sum, p) => sum + (p.interestAmount || 0), 0);
    
    // محاسبه سود استاندارد کل تا تاریخ پرداخت فعلی
    const paymentDate = currentPaymentDate || currentPayment.paymentDate;
    const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", paymentDate);
    const totalStandardInterest = (installmentPrincipal * (interestRate / 100) / 365) * daysSinceReceived;
    
    // اگر هنوز سود استاندارد باقی مانده است که باید پرداخت شود
    if (totalPaidInterest < totalStandardInterest) {
      const remainingStandardInterest = Math.round(totalStandardInterest) - totalPaidInterest;
      
      console.log("Interest calculation for zero principal:", {
        facilityId,
        installmentNumber,
        installmentPrincipal,
        paymentDate,
        daysSinceReceived,
        totalPaidInterest,
        totalStandardInterest: Math.round(totalStandardInterest),
        remainingStandardInterest
      });
      
      return Math.round(remainingStandardInterest);
    }
    
    // اگر بیشتر از سود استاندارد پرداخت شده، صفر برگردان
    return 0;
  };

  // تست محاسبه روزها برای مثال داده شده
  const testDaysCalculation = () => {
    // تست مثال اصلی
    const receivedDate1 = "1402/12/20";
    const paymentDate1 = "1403/01/30";
    
    // محاسبه با تابع جدید
    const daysDifference1 = calculateDaysBetween(receivedDate1, paymentDate1);
    
    console.log("Test Example 1 - Days Calculation (New Method):", {
      receivedDate: receivedDate1,
      paymentDate: paymentDate1,
      daysDifference: daysDifference1,
      expectedDays: 40 // انتظار داریم 40 روز باشد
    });
    
    // تست مثال دوم (مثال شما)
    const receivedDate2 = "1402/12/25";
    const paymentDate2 = "1403/01/30";
    
    // محاسبه با تابع جدید
    const daysDifference2 = calculateDaysBetween(receivedDate2, paymentDate2);
    
    console.log("Test Example 2 - Days Calculation (New Method):", {
      receivedDate: receivedDate2,
      paymentDate: paymentDate2,
      daysDifference: daysDifference2,
      expectedDays: 35 // انتظار داریم حدود 35 روز باشد
    });
    
    // تست با ماه‌های یکسان
    const receivedDate3 = "1402/01/10";
    const paymentDate3 = "1402/01/20";
    
    // محاسبه با تابع جدید
    const daysDifference3 = calculateDaysBetween(receivedDate3, paymentDate3);
    
    console.log("Test Example 3 - Same Month:", {
      receivedDate: receivedDate3,
      paymentDate: paymentDate3,
      daysDifference: daysDifference3,
      expectedDays: 10 // انتظار داریم 10 روز باشد
    });
    
    // تست با سال‌های متفاوت
    const receivedDate4 = "1401/12/25";
    const paymentDate4 = "1403/01/30";
    
    // محاسبه با تابع جدید
    const daysDifference4 = calculateDaysBetween(receivedDate4, paymentDate4);
    
    console.log("Test Example 4 - Different Years:", {
      receivedDate: receivedDate4,
      paymentDate: paymentDate4,
      daysDifference: daysDifference4
    });
  };
  
  // بارگذاری داده‌ها
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        console.log("Loading data...", { refreshTrigger });
        
        // بارگذاری داده‌ها
        const loadedFacilities = getFacilities();
        const loadedPayments = getPayments();
        
        console.log("Facilities:", loadedFacilities.length);
        console.log("Payments:", loadedPayments.length);
        
        // ذخیره داده‌ها در state
        setFacilities(loadedFacilities);
        setPayments(loadedPayments);
        
        // اطمینان از اینکه داده‌ها به درستی بارگذاری شده‌اند
        console.log("Data loaded successfully");
        
        // اجرای تست محاسبه روزها
        testDaysCalculation();
        
        setLoading(false);
        setError(null);
      } catch (err) {
        console.error("Error loading data:", err);
        setError("خطا در بارگذاری داده‌ها");
        setLoading(false);
      }
    };
    
    loadData();
  }, [refreshTrigger]); // اضافه کردن refreshTrigger به آرایه وابستگی‌ها

  // یافتن اطلاعات تسهیلات و قسط برای هر پرداخت
  const getFacilityInfo = (facilityId: number, installmentNumber: number) => {
    if (!facilities || facilities.length === 0) {
      return {
        bankName: "نامشخص",
        contractNumber: "نامشخص",
        interestRate: 0,
        penaltyRate: 0,
        dueDate: "نامشخص",
        receivedDate: "نامشخص"
      };
    }
    
    const facility = facilities.find(f => f.id === facilityId);
    if (!facility) {
      console.warn(`Facility with ID ${facilityId} not found`);
      return {
        bankName: "نامشخص",
        contractNumber: "نامشخص",
        interestRate: 0,
        penaltyRate: 0,
        dueDate: "نامشخص",
        receivedDate: "نامشخص"
      };
    }
    
    const installment = facility.installments?.[installmentNumber - 1];
    
    return {
      bankName: facility.bankName || "نامشخص",
      contractNumber: facility.contractNumber || "نامشخص",
      interestRate: facility.interestRate || 0,
      penaltyRate: facility.penaltyRate || 0,
      dueDate: installment?.dueDate || "نامشخص",
      receivedDate: facility.receivedDate || "نامشخص" // تاریخ اخذ تسهیلات
    };
  };

  // محاسبه مانده در خط انحراف سود و جریمه برای هر قسط - روش ساده و مستقیم
  const calculateRemainingDeviations = () => {
    if (!payments || payments.length === 0 || !facilities || facilities.length === 0) {
      return [];
    }
    
    // گروه‌بندی پرداخت‌ها بر اساس تسهیلات و شماره قسط
    const paymentGroups: Record<string, Payment[]> = {};
    
    // مرتب‌سازی پرداخت‌ها بر اساس تاریخ (از قدیمی به جدید)
    const sortedPayments = [...payments].sort((a, b) => {
      const dateA = persianDateToDays(a.paymentDate);
      const dateB = persianDateToDays(b.paymentDate);
      return dateA - dateB;
    });
    
    // گروه‌بندی پرداخت‌ها
    sortedPayments.forEach(payment => {
      const key = `${payment.facilityId}-${payment.installmentNumber}`;
      if (!paymentGroups[key]) {
        paymentGroups[key] = [];
      }
      paymentGroups[key].push(payment);
    });
    
    // ساختار داده برای نگهداری مانده در خط انحراف برای هر گروه قسط
    const deviationBalances: Record<string, { interestBalance: number, penaltyBalance: number }> = {};
    
    // نتیجه نهایی
    const result: Payment[] = [];
    
    // پردازش هر پرداخت و محاسبه مانده در خط انحراف به صورت تجمعی
    for (const payment of sortedPayments) {
      const key = `${payment.facilityId}-${payment.installmentNumber}`;
      if (!deviationBalances[key]) {
        deviationBalances[key] = { interestBalance: 0, penaltyBalance: 0 };
      }
      const facility = facilities.find(f => f.id === payment.facilityId);
      if (!facility) {
        result.push({ ...payment, remainingInterestDeviation: 0, remainingPenaltyDeviation: 0 });
        continue;
      }
      // یافتن قسط
      // اصلاح: پیدا کردن قسط بر اساس ایندکس (شماره قسط - 1)
      const installment = facility.installments && facility.installments[payment.installmentNumber - 1];
      if (!installment) {
        result.push({ ...payment, remainingInterestDeviation: 0, remainingPenaltyDeviation: 0 });
        continue;
      }
      // محاسبه سود استاندارد
      let standardInterest = 0;
      let installmentPrincipal = 0;

      if (installment && installment.principalAmount) {
        installmentPrincipal = Number(installment.principalAmount) || 0;
      }

      // محاسبه سود استاندارد با استفاده از توابع موجود
      if (payment.principalAmount && payment.principalAmount > 0) {
        // اگر مبلغ اصل پرداختی وجود دارد، از آن استفاده می‌کنیم
        const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", payment.paymentDate);
        standardInterest = Math.round((payment.principalAmount * (facility.interestRate || 0) / 100) / 365 * daysSinceReceived);
      } else {
        // اگر مبلغ اصل پرداختی صفر است، از تابع محاسبه پیشرفته استفاده می‌کنیم
        const daysSinceReceived = calculateDaysBetween(facility.receivedDate || "", payment.paymentDate);
        standardInterest = calculateStandardInterest(
          payment.principalAmount || 0,
          facility.interestRate || 0,
          daysSinceReceived,
          payment.facilityId,
          payment.installmentNumber,
          payment.id,
          payment.paymentDate
        );
      }

      const interestDeviation = (payment.interestAmount || 0) - standardInterest;
      deviationBalances[key].interestBalance += interestDeviation;
      
      console.log(`Interest deviation for payment ${payment.id}:`, {
        facilityId: payment.facilityId,
        installmentNumber: payment.installmentNumber,
        paymentDate: payment.paymentDate,
        principalAmount: payment.principalAmount,
        interestAmount: payment.interestAmount,
        installmentPrincipal,
        interestRate: facility.interestRate,
        standardInterest,
        interestDeviation,
        balanceBefore: deviationBalances[key].interestBalance - interestDeviation,
        balanceAfter: deviationBalances[key].interestBalance
      });
      
      // محاسبه جریمه استاندارد
      const delayDays = calculateDelayDays(installment.dueDate, payment.paymentDate);
      let standardPenalty = 0;

      // محاسبه جریمه استاندارد با استفاده از توابع موجود
      if (payment.principalAmount && payment.principalAmount > 0) {
        // اگر مبلغ اصل پرداختی وجود دارد، از آن استفاده می‌کنیم
        if (delayDays > 0) {
          standardPenalty = Math.round((payment.principalAmount * (facility.penaltyRate || 0) / 100) / 365 * delayDays);
        }
      } else {
        // اگر مبلغ اصل پرداختی صفر است، از تابع محاسبه پیشرفته استفاده می‌کنیم
        standardPenalty = calculateStandardPenalty(
          payment.principalAmount || 0,
          facility.penaltyRate || 0,
          delayDays,
          payment.facilityId,
          payment.installmentNumber,
          payment.id,
          payment.paymentDate
        );
      }

      // محاسبه انحراف جریمه برای این پرداخت
      const penaltyDeviation = (payment.penaltyAmount || 0) - standardPenalty;

      // به‌روزرسانی مانده در خط انحراف جریمه
      deviationBalances[key].penaltyBalance += penaltyDeviation;
      
      console.log(`Penalty deviation for payment ${payment.id}:`, {
        facilityId: payment.facilityId,
        installmentNumber: payment.installmentNumber,
        paymentDate: payment.paymentDate,
        principalAmount: payment.principalAmount,
        penaltyAmount: payment.penaltyAmount,
        installmentPrincipal,
        penaltyRate: facility.penaltyRate,
        delayDays,
        standardPenalty,
        penaltyDeviation,
        balanceBefore: deviationBalances[key].penaltyBalance - penaltyDeviation,
        balanceAfter: deviationBalances[key].penaltyBalance
      });
      
      // لاگ برای بررسی
      console.log(`Payment ${payment.id} deviations:`, {
        facilityId: payment.facilityId,
        installmentNumber: payment.installmentNumber,
        paymentDate: payment.paymentDate,
        principalAmount: payment.principalAmount,
        interestAmount: payment.interestAmount,
        standardInterest,
        interestDeviation,
        currentInterestBalance: deviationBalances[key].interestBalance,
        penaltyAmount: payment.penaltyAmount,
        standardPenalty,
        penaltyDeviation,
        currentPenaltyBalance: deviationBalances[key].penaltyBalance
      });
      
      // افزودن پرداخت با مانده در خط انحراف به نتیجه
      result.push({
        ...payment,
        remainingInterestDeviation: deviationBalances[key].interestBalance,
        remainingPenaltyDeviation: deviationBalances[key].penaltyBalance
      });
    }
    
    // مرتب‌سازی نتیجه بر اساس ترتیب اصلی پرداخت‌ها
    const paymentIdMap = new Map(payments.map((p, index) => [p.id, index]));
    
    return result.sort((a, b) => {
      const indexA = paymentIdMap.get(a.id) || 0;
      const indexB = paymentIdMap.get(b.id) || 0;
      return indexA - indexB;
    });
  };
  
  // پرداخت‌ها با محاسبه مانده در خط انحراف
  const paymentsWithDeviations = useMemo(() => {
    if (payments && payments.length > 0 && facilities && facilities.length > 0) {
      // استفاده از تابع محاسبه واقعی مانده انحراف
      return calculatePaymentsWithDeviations(payments, facilities);
    }

    return [];
  }, [payments, facilities]);
  
  // فیلتر کردن پرداخت‌ها بر اساس جستجو
  const filteredPayments = useMemo(() => {
    if (!paymentsWithDeviations || paymentsWithDeviations.length === 0 || !facilities || facilities.length === 0) {
      return [];
    }
    
    if (!searchTerm.trim()) {
      return paymentsWithDeviations; // اگر عبارت جستجو خالی باشد، همه پرداخت‌ها را نمایش بده
    }
    
    const term = searchTerm.trim().toLowerCase();
    
    return paymentsWithDeviations.filter(payment => {
      // یافتن تسهیلات مربوط به این پرداخت
      const facility = facilities.find(f => f.id === payment.facilityId);
      if (!facility) return false;
      
      // جستجو در نام بانک
      if (facility.bankName.toLowerCase().includes(term)) return true;
      
      // جستجو در شماره تسهیلات
      if (facility.contractNumber.toLowerCase().includes(term)) return true;
      
      // جستجو در شماره قسط
      if (payment.installmentNumber.toString().includes(term)) return true;
      
      // جستجو در تاریخ پرداخت
      if (payment.paymentDate.toLowerCase().includes(term)) return true;
      
      return false;
    });
  }, [paymentsWithDeviations, facilities, searchTerm]);
  
  // پاک کردن جستجو
  const clearSearch = () => {
    setSearchTerm("");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">کنترل تسهیلات - روش اول</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportToExcel} className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            دریافت Excel
          </Button>
          <Button variant="outline" onClick={printAsPDF} className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={onBack}>
            بازگشت
          </Button>
        </div>
      </div>
      
      {/* جستجو */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">جستجو</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-grow">
              <Input
                placeholder="جستجو در بانک، شماره تسهیلات، شماره قسط یا تاریخ پرداخت"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <Button variant="outline" onClick={clearSearch} className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              پاک کردن جستجو
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">در حال بارگذاری...</div>
          </CardContent>
        </Card>
      ) : error ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-500">{error}</div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>لیست پرداخت‌ها</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table className="border-collapse border border-gray-300">
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right border border-gray-300">نام بانک</TableHead>
                    <TableHead className="text-right border border-gray-300">شماره تسهیلات</TableHead>
                    <TableHead className="text-right border border-gray-300">شماره قسط</TableHead>
                    <TableHead className="text-right border border-gray-300">تاریخ اخذ تسهیلات</TableHead>
                    <TableHead className="text-right border border-gray-300">تاریخ سررسید</TableHead>
                    <TableHead className="text-right border border-gray-300">تاریخ پرداخت</TableHead>
                    <TableHead className="text-right border border-gray-300">تاخیر/تعجیل</TableHead>
                    <TableHead className="text-right border border-gray-300">مدت زمان تسویه</TableHead>
                    <TableHead className="text-right border border-gray-300">نرخ سود</TableHead>
                    <TableHead className="text-right border border-gray-300">نرخ جریمه</TableHead>
                    <TableHead className="text-right border border-gray-300">اصل پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">سود پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">سود استاندارد</TableHead>
                    <TableHead className="text-right border border-gray-300">انحراف سود</TableHead>
                    <TableHead className="text-right border border-gray-300">جریمه پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">جریمه استاندارد</TableHead>
                    <TableHead className="text-right border border-gray-300">انحراف جریمه</TableHead>
                    <TableHead className="text-right border border-gray-300">مجموع پرداختی</TableHead>
                    <TableHead className="text-right border border-gray-300">مانده انحراف سود</TableHead>
                    <TableHead className="text-right border border-gray-300">مانده انحراف جریمه</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {!filteredPayments || filteredPayments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={20} className="text-center">
                        هیچ پرداختی یافت نشد
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPayments.map((payment) => {
                      const { bankName, contractNumber, interestRate, penaltyRate, dueDate, receivedDate } = 
                        getFacilityInfo(payment.facilityId, payment.installmentNumber);
                      
                      // محاسبه تاخیر/تعجیل
                      const delayDays = calculateDelayDays(dueDate, payment.paymentDate);
                      
                      // محاسبه تعداد روز از تاریخ اخذ تسهیلات تا تاریخ پرداخت با تابع جدید
                      const daysSinceReceived = calculateDaysBetween(receivedDate, payment.paymentDate);
                      
                      // برای تست محاسبه روزها
                      console.log("Days Calculation:", {
                        facilityId: payment.facilityId,
                        installmentNumber: payment.installmentNumber,
                        receivedDate,
                        paymentDate: payment.paymentDate,
                        daysSinceReceived
                      });
                      
                      // محاسبه سود استاندارد
                      let standardInterest = 0;
                      
                      if (payment.principalAmount && payment.principalAmount > 0) {
                        // اگر مبلغ اصل وجود دارد، از فرمول معمولی استفاده می‌کنیم
                        standardInterest = Math.round((payment.principalAmount * (interestRate / 100) / 365) * daysSinceReceived);
                      } else {
                        // اگر مبلغ اصل صفر است، از مانده در خط انحراف سود استفاده می‌کنیم
                        // مقدار مطلق مانده در خط انحراف را استفاده می‌کنیم (فقط اگر منفی باشد)
                        const remainingDeviation = payment.remainingInterestDeviation || 0;
                        standardInterest = remainingDeviation < 0 ? Math.abs(remainingDeviation) : 0;
                      }
                      
                      console.log("Interest Calculation:", {
                        principalAmount: payment.principalAmount,
                        interestRate,
                        receivedDate,
                        paymentDate: payment.paymentDate,
                        daysSinceReceived,
                        standardInterest,
                        remainingInterestDeviation: payment.remainingInterestDeviation
                      });
                      
                      // محاسبه انحراف سود
                      const interestDeviation = (payment.interestAmount || 0) - standardInterest;
                      
                      // محاسبه جریمه استاندارد
                      let standardPenalty = 0;
                      
                      if (payment.principalAmount && payment.principalAmount > 0) {
                        // اگر مبلغ اصل وجود دارد، از فرمول معمولی استفاده می‌کنیم
                        if (delayDays > 0) {
                          standardPenalty = Math.round((payment.principalAmount * (penaltyRate / 100) / 365) * delayDays);
                        }
                      } else {
                        // اگر مبلغ اصل صفر است، از مانده در خط انحراف جریمه استفاده می‌کنیم
                        // مقدار مطلق مانده در خط انحراف را استفاده می‌کنیم (فقط اگر منفی باشد)
                        const remainingDeviation = payment.remainingPenaltyDeviation || 0;
                        standardPenalty = remainingDeviation < 0 ? Math.abs(remainingDeviation) : 0;
                      }
                      
                      // محاسبه دستی برای مقایسه با مقادیر مورد انتظار
                      // برای مثال با اصل 250,000,000 ریال، نرخ جریمه 6% و 10 روز تاخیر
                      const manualCalculation = Math.round(250000000 * ((6/100)/365) * 10);
                      
                      console.log("Penalty Calculation:", {
                        principalAmount: payment.principalAmount,
                        penaltyRate,
                        delayDays,
                        dailyRate: (penaltyRate / 100) / 365,
                        standardPenalty,
                        manualCalculation,
                        expectedValue: 537600 // مقدار مورد انتظار از خروجی
                      });
                      
                      // محاسبه انحراف جریمه
                      const penaltyDeviation = (payment.penaltyAmount || 0) - standardPenalty;
                      
                      return (
                        <TableRow key={payment.id}>
                          <TableCell>{bankName}</TableCell>
                          <TableCell>{contractNumber}</TableCell>
                          <TableCell>{payment.installmentNumber}</TableCell>
                          <TableCell>{receivedDate}</TableCell>
                          <TableCell>{dueDate}</TableCell>
                          <TableCell>{payment.paymentDate}</TableCell>
                          <TableCell className={delayDays > 0 ? "text-red-500 font-bold" : delayDays < 0 ? "text-green-500 font-bold" : ""}>
                            {delayDays}
                          </TableCell>
                          <TableCell className="font-bold">
                            {daysSinceReceived}
                          </TableCell>
                          <TableCell>{interestRate}%</TableCell>
                          <TableCell>{penaltyRate}%</TableCell>
                          <TableCell>{formatCurrency(payment.principalAmount)}</TableCell>
                          <TableCell>{formatCurrency(payment.interestAmount)}</TableCell>
                          <TableCell>{formatCurrency(standardInterest)}</TableCell>
                          <TableCell className={interestDeviation > 0 ? "text-red-500 font-bold" : interestDeviation < 0 ? "text-green-500 font-bold" : ""}>
                            {formatCurrency(interestDeviation)}
                          </TableCell>
                          <TableCell>{formatCurrency(payment.penaltyAmount)}</TableCell>
                          <TableCell>{formatCurrency(standardPenalty)}</TableCell>
                          <TableCell className={penaltyDeviation > 0 ? "text-red-500 font-bold" : penaltyDeviation < 0 ? "text-green-500 font-bold" : ""}>
                            {formatCurrency(penaltyDeviation)}
                          </TableCell>
                          <TableCell>{formatCurrency(payment.totalAmount)}</TableCell>
                          <TableCell className={payment.remainingInterestDeviation < 0 ? "text-red-500 font-bold" : "text-blue-500 font-bold"}>
                            {formatCurrency(payment.remainingInterestDeviation || 0)}
                          </TableCell>
                          <TableCell className={payment.remainingPenaltyDeviation < 0 ? "text-red-500 font-bold" : "text-blue-500 font-bold"}>
                            {formatCurrency(payment.remainingPenaltyDeviation || 0)}
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ControlMethod1;