import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface SidebarMenuProps {
  onNavigate: (view: string) => void;
}

const items = [
  { id: 'dashboard', label: 'داشبورد' },
  { id: 'facility-form', label: 'ثبت تسهیلات' },
  { id: 'payment-form', label: 'ثبت پرداخت' },
  { id: 'facility-list', label: 'لیست تسهیلات' },
  { id: 'payment-list', label: 'لیست پرداخت ها' },
  { id: 'installment-list', label: 'لیست اقساط' },
  { 
    id: 'facility-control', 
    label: 'کنترل تسهیلات',
    children: [
      { id: 'control-method-1', label: 'کنترل روش اول' },
      { id: 'control-method-2', label: 'کنترل روش دوم' },
      { id: 'control-method-3', label: 'کنترل روش سوم' },
    ]
  },
];

const SidebarMenu = ({ onNavigate }: SidebarMenuProps) => {
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});

  const toggleMenu = (id: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const renderMenuItem = (item: any) => {
    // اگر زیرمنو داشته باشد
    if (item.children) {
      return (
        <div key={item.id} className="space-y-1">
          <Button
            variant="ghost"
            className="w-full justify-between text-sidebar-foreground"
            onClick={() => toggleMenu(item.id)}
          >
            <span>{item.label}</span>
            <span className="ml-2">{expandedMenus[item.id] ? '▼' : '◀'}</span>
          </Button>
          
          {expandedMenus[item.id] && (
            <div className="pr-4 space-y-1">
              {item.children.map((child: any) => (
                <Button
                  key={child.id}
                  variant="ghost"
                  className="w-full justify-start text-sidebar-foreground text-sm"
                  onClick={() => onNavigate(child.id)}
                >
                  {child.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    // منوی معمولی
    return (
      <Button
        key={item.id}
        variant="ghost"
        className="w-full justify-start text-sidebar-foreground"
        onClick={() => onNavigate(item.id)}
      >
        {item.label}
      </Button>
    );
  };

  return (
    <aside className="w-48 bg-sidebar text-sidebar-foreground shadow h-screen p-4 space-y-2 fixed right-0 top-0">
      {items.map(renderMenuItem)}
      
      <div className="mt-4">
        <label className="text-sm text-sidebar-foreground">سبک نمایش</label>
        <select
          className="mt-2 w-full border-sidebar-border border rounded p-1 bg-sidebar text-sidebar-foreground"
          onChange={(e) => {
            document.documentElement.classList.remove('theme1','theme2','theme3','theme4','theme5');
            document.documentElement.classList.add(e.target.value);
          }}
        >
          <option value="theme1">رنگ 1</option>
          <option value="theme2">رنگ 2</option>
          <option value="theme3">رنگ 3</option>
          <option value="theme4">رنگ 4</option>
          <option value="theme5">رنگ 5</option>
        </select>
      </div>
    </aside>
  );
};

export default SidebarMenu;
