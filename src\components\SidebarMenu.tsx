import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Home,
  Building2,
  CreditCard,
  List,
  Receipt,
  Calculator,
  ChevronDown,
  ChevronRight,
  Palette,
  Settings,
  BarChart3,
  FileText,
  TrendingUp
} from 'lucide-react';

interface SidebarMenuProps {
  onNavigate: (view: string) => void;
}

const items = [
  { id: 'dashboard', label: 'داشبورد', icon: Home },
  { id: 'facility-form', label: 'ثبت تسهیلات', icon: Building2 },
  { id: 'payment-form', label: 'ثبت پرداخت', icon: CreditCard },
  { id: 'facility-list', label: 'لیست تسهیلات', icon: List },
  { id: 'payment-list', label: 'لیست پرداخت ها', icon: Receipt },
  { id: 'installment-list', label: 'لیست اقساط', icon: FileText },
  {
    id: 'facility-control',
    label: 'کنترل تسهیلات',
    icon: Calculator,
    children: [
      { id: 'control-method-1', label: 'کنترل روش اول', icon: BarChart3 },
      { id: 'control-method-2', label: 'کنترل روش دوم', icon: TrendingUp },
      { id: 'control-method-3', label: 'کنترل روش سوم', icon: Settings },
    ]
  },
];

const SidebarMenu = ({ onNavigate }: SidebarMenuProps) => {
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});

  const toggleMenu = (id: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const renderMenuItem = (item: any) => {
    const IconComponent = item.icon;

    // اگر زیرمنو داشته باشد
    if (item.children) {
      return (
        <div key={item.id} className="space-y-2">
          <Button
            variant="ghost"
            className="w-full justify-between text-slate-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-200 rounded-xl p-3 group"
            onClick={() => toggleMenu(item.id)}
          >
            <div className="flex items-center gap-3">
              <IconComponent className="h-5 w-5 group-hover:text-blue-600 transition-colors" />
              <span className="font-medium">{item.label}</span>
            </div>
            {expandedMenus[item.id] ?
              <ChevronDown className="h-4 w-4 transition-transform duration-200" /> :
              <ChevronRight className="h-4 w-4 transition-transform duration-200" />
            }
          </Button>

          {expandedMenus[item.id] && (
            <div className="pr-6 space-y-1 border-r-2 border-blue-100 mr-3">
              {item.children.map((child: any) => {
                const ChildIcon = child.icon;
                return (
                  <Button
                    key={child.id}
                    variant="ghost"
                    className="w-full justify-start text-slate-600 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 text-sm transition-all duration-200 rounded-lg p-2 group"
                    onClick={() => onNavigate(child.id)}
                  >
                    <div className="flex items-center gap-2">
                      <ChildIcon className="h-4 w-4 group-hover:text-indigo-600 transition-colors" />
                      <span>{child.label}</span>
                    </div>
                  </Button>
                );
              })}
            </div>
          )}
        </div>
      );
    }

    // منوی معمولی
    return (
      <Button
        key={item.id}
        variant="ghost"
        className="w-full justify-start text-slate-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-200 rounded-xl p-3 group"
        onClick={() => onNavigate(item.id)}
      >
        <div className="flex items-center gap-3">
          <IconComponent className="h-5 w-5 group-hover:text-blue-600 transition-colors" />
          <span className="font-medium">{item.label}</span>
        </div>
      </Button>
    );
  };

  return (
    <aside className="w-48 bg-white/95 backdrop-blur-sm border-l border-slate-200/60 shadow-2xl h-screen fixed right-0 top-0 z-50 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-slate-200/60 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-lg shadow-lg">
            <Calculator className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="font-bold text-slate-800 text-sm">سیستم کنترل</h2>
            <p className="text-xs text-slate-500">تسهیلات بانکی</p>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <div className="p-4 space-y-3 flex-1 overflow-y-auto overflow-x-hidden">
        {items.map(renderMenuItem)}
      </div>

      {/* Theme Selector */}
      <div className="p-4 border-t border-slate-200/60 bg-gradient-to-r from-purple-50 to-pink-50">
        <div className="flex items-center gap-2 mb-3">
          <Palette className="h-4 w-4 text-purple-600" />
          <label className="text-sm font-medium text-slate-700">سبک نمایش</label>
        </div>
        <select
          className="w-full border-2 border-slate-200 rounded-lg p-2 bg-white text-slate-700 text-sm hover:border-purple-300 focus:border-purple-500 transition-all duration-200 shadow-sm"
          onChange={(e) => {
            document.documentElement.classList.remove('theme1','theme2','theme3','theme4','theme5');
            document.documentElement.classList.add(e.target.value);
          }}
        >
          <option value="theme1">🎨 رنگ آبی</option>
          <option value="theme2">🌸 رنگ صورتی</option>
          <option value="theme3">🌿 رنگ سبز</option>
          <option value="theme4">🔥 رنگ نارنجی</option>
          <option value="theme5">💜 رنگ بنفش</option>
        </select>
      </div>
    </aside>
  );
};

export default SidebarMenu;
