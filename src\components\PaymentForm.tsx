import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import PersianDatePicker from "@/components/common/PersianDatePicker";
import { persianToDate } from "@/utils/jalali";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import MoneyInput from "@/components/ui/money-input";
import { formatMoney } from "@/utils/formatMoney";
import { Installment } from "@/types/facility";

interface PaymentFormProps {
  onBack: () => void;
}

interface Facility {
  id: number;
  facilityType: string;
  amount: string;
  bankName: string;
  facilityNumber: string;
  installmentCount: string;
  installmentAmount: string;
  installments?: Installment[];
}

const PaymentForm = ({ onBack }: PaymentFormProps) => {
  const { toast } = useToast();
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [formData, setFormData] = useState({
    facilityId: "",
    installmentNumber: "",
    paymentDate: "",
    principalAmount: "",
    interestAmount: "",
    penaltyAmount: "",
    totalAmount: "",
    notes: "",
  });

  useEffect(() => {
    try {
      const savedFacilities = JSON.parse(localStorage.getItem('facilities') || '[]');
      setFacilities(savedFacilities);
    } catch {
      setFacilities([]);
    }
  }, []);

  useEffect(() => {
    const principal = parseFloat(formData.principalAmount) || 0;
    const interest = parseFloat(formData.interestAmount) || 0;
    const penalty = parseFloat(formData.penaltyAmount) || 0;
    const total = principal + interest + penalty;
    setFormData(prev => ({ ...prev, totalAmount: total.toString() }));
  }, [formData.principalAmount, formData.interestAmount, formData.penaltyAmount]);

  const selectedFacility = facilities.find(f => f.id.toString() === formData.facilityId);
  const availableInstallments = selectedFacility?.installments || [];
  const selectedInstallment = availableInstallments.find(
    inst => inst.id.toString() === formData.installmentNumber
  );


  // Filter out any invalid facilities or installments
  const validFacilities = facilities.filter(facility => 
    facility && 
    facility.id !== null && 
    facility.id !== undefined && 
    facility.id.toString().trim() !== ""
  );

  const validInstallments = availableInstallments.filter(installment => 
    installment && 
    installment.id !== null && 
    installment.id !== undefined && 
    installment.id.toString().trim() !== ""
  );

  const calculateSettlementDays = () => {
    if (!selectedInstallment?.dueDate || !formData.paymentDate) return null;
    
    const [dy, dm, dd] = selectedInstallment.dueDate.split("/").map(Number);
    const [py, pm, pd] = formData.paymentDate.split("/").map(Number);
    const dueDate = persianToDate(dy, dm, dd);
    const paymentDate = persianToDate(py, pm, pd);
    
    const diffTime = paymentDate.getTime() - dueDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  const settlementDays = calculateSettlementDays();

  const handleInstallmentChange = (installmentId: string) => {
    setFormData(prev => ({ ...prev, installmentNumber: installmentId }));
    
    if (selectedFacility) {
      const installment = availableInstallments.find(
        inst => inst.id.toString() === installmentId
      );
      
      if (installment) {
        setFormData(prev => ({
          ...prev,
          principalAmount: String(installment.principalAmount || ""),
          interestAmount: String(installment.interestAmount || ""),
        }));
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const requiredFields = ['facilityId', 'installmentNumber', 'paymentDate', 'totalAmount'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);
    
    if (missingFields.length > 0) {
      toast({
        title: "خطا",
        description: "لطفا تمام فیلدهای ضروری را پر کنید",
        variant: "destructive",
      });
      return;
    }

    // بررسی فرمت تاریخ پرداخت
    const datePattern = /^\d{4}\/\d{2}\/\d{2}$/;
    if (!datePattern.test(formData.paymentDate)) {
      toast({
        title: "خطا",
        description: "تاریخ پرداخت باید در قالب yyyy/mm/dd باشد",
        variant: "destructive",
      });
      return;
    }

    if (parseFloat(formData.totalAmount) <= 0) {
      toast({
        title: "خطا",
        description: "مبلغ پرداختی باید بیشتر از صفر باشد",
        variant: "destructive",
      });
      return;
    }

    const payments = JSON.parse(localStorage.getItem('payments') || '[]');
    const newPayment = {
      id: Date.now(),
      ...formData,
      settlementDays: settlementDays,
      facilityInfo: selectedFacility,
      installmentInfo: selectedInstallment,
      createdAt: new Date(),
    };
    payments.push(newPayment);
    localStorage.setItem('payments', JSON.stringify(payments));

    toast({
      title: "موفقیت",
      description: "پرداخت با موفقیت ثبت شد",
    });

    onBack();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-800">ثبت پرداخت قسط</h2>
        <Button variant="outline" onClick={onBack}>
          بازگشت
        </Button>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>اطلاعات پرداخت</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Facility Selection */}
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="facilityId">انتخاب تسهیلات *</Label>
                {validFacilities.length > 0 ? (
                  <Select 
                    value={formData.facilityId || ""} 
                    onValueChange={(value) => setFormData({...formData, facilityId: value, installmentNumber: ""})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="انتخاب کنید" />
                    </SelectTrigger>
                    <SelectContent>
                      {validFacilities.map((facility) => {
                        const key = `facility_${facility.id}`;
                        const value = facility.id.toString();
                        return (
                          <SelectItem key={key} value={value}>
                            {`${facility.facilityType} - ${facility.bankName} - ${facility.facilityNumber}`}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-3 border rounded-md bg-gray-50 text-gray-500">
                    هیچ تسهیلاتی یافت نشد
                  </div>
                )}
              </div>

              {/* Selected Facility Info */}
              {selectedFacility && (
                <div className="md:col-span-2 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">اطلاعات تسهیلات انتخاب شده:</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">نوع: </span>
                      <span className="font-semibold">{selectedFacility.facilityType}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">بانک: </span>
                      <span className="font-semibold">{selectedFacility.bankName}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">شماره: </span>
                      <span className="font-semibold">{selectedFacility.facilityNumber}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">مبلغ قسط: </span>
                      <span className="font-semibold">{formatMoney(selectedFacility.installmentAmount)} ریال</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Installment Number Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="installmentNumber">شماره قسط *</Label>
                {selectedFacility && validInstallments.length > 0 ? (
                  <Select 
                    value={formData.installmentNumber || ""} 
                    onValueChange={handleInstallmentChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="انتخاب کنید" />
                    </SelectTrigger>
                    <SelectContent>
                      {validInstallments.map((installment) => {
                        const key = `installment_${installment.id}`;
                        const value = installment.id.toString();
                        return (
                          <SelectItem key={key} value={value}>
                            {`قسط ${installment.id}`}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-3 border rounded-md bg-gray-50 text-gray-500">
                    {!selectedFacility ? "ابتدا تسهیلات را انتخاب کنید" : "هیچ قسطی یافت نشد"}
                  </div>
                )}
              </div>

              {/* Selected Installment Info */}
              {selectedInstallment && (
                <div className="space-y-2">
                  <Label>اطلاعات قسط انتخاب شده</Label>
                  <div className="p-3 bg-green-50 rounded-lg text-sm space-y-1">
                    <div>
                      <span className="text-gray-600">تاریخ سررسید: </span>
                      <span className="font-semibold">{selectedInstallment.dueDate || 'تعریف نشده'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">مبلغ اصل: </span>
                      <span className="font-semibold">{formatMoney(selectedInstallment.principalAmount)} ریال</span>
                    </div>
                    <div>
                      <span className="text-gray-600">مبلغ سود: </span>
                      <span className="font-semibold">{formatMoney(selectedInstallment.interestAmount)} ریال</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Payment Date */}
              <PersianDatePicker
                label="تاریخ پرداخت"
                value={formData.paymentDate}
                onChange={(value) => setFormData({ ...formData, paymentDate: value })}
                required
              />

              {/* Settlement Days */}
              {settlementDays !== null && selectedInstallment?.dueDate && formData.paymentDate && (
                <div className="space-y-2">
                  <Label>مدت روز تسویه</Label>
                  <div className={`p-3 rounded-lg text-sm font-semibold ${
                    settlementDays <= 0 ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                  }`}>
                    {settlementDays <= 0 
                      ? `${Math.abs(settlementDays)} روز زودتر` 
                      : `${settlementDays} روز دیرتر`
                    }
                  </div>
                </div>
              )}

              {/* Principal Amount */}
              <div className="space-y-2">
                <Label htmlFor="principalAmount">مبلغ اصل (ریال)</Label>
                <MoneyInput
                  id="principalAmount"
                  value={formData.principalAmount}
                  onChange={(value) => setFormData({...formData, principalAmount: value})}
                  placeholder="مثال: 10,000,000"
                />
              </div>

              {/* Interest Amount */}
              <div className="space-y-2">
                <Label htmlFor="interestAmount">مبلغ سود (ریال)</Label>
                <MoneyInput
                  id="interestAmount"
                  value={formData.interestAmount}
                  onChange={(value) => setFormData({...formData, interestAmount: value})}
                  placeholder="مثال: 4,000,000"
                />
              </div>

              {/* Penalty Amount */}
              <div className="space-y-2">
                <Label htmlFor="penaltyAmount">مبلغ جریمه (ریال)</Label>
                <MoneyInput
                  id="penaltyAmount"
                  value={formData.penaltyAmount}
                  onChange={(value) => setFormData({...formData, penaltyAmount: value})}
                  placeholder="مثال: 1,000,000"
                />
              </div>

              {/* Total Amount (Auto-calculated) */}
              <div className="space-y-2">
                <Label htmlFor="totalAmount">مجموع مبلغ پرداختی (ریال) *</Label>
                <MoneyInput
                  id="totalAmount"
                  value={formData.totalAmount}
                  onChange={(value) => setFormData({...formData, totalAmount: value})}
                  className="bg-gray-50 font-semibold"
                />
                <p className="text-xs text-gray-500">
                  {formatMoney(formData.totalAmount)} ریال
                </p>
              </div>

              {/* Notes */}
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="notes">یادداشت</Label>
                <Input
                  id="notes"
                  placeholder="توضیحات اضافی"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 space-x-reverse">
              <Button type="button" variant="outline" onClick={onBack}>
                لغو
              </Button>
              <Button type="submit" className="bg-green-600 hover:bg-green-700">
                ثبت پرداخت
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentForm;
