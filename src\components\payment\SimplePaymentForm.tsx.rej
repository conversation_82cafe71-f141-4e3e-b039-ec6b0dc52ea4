diff a/src/components/payment/SimplePaymentForm.tsx b/src/components/payment/SimplePaymentForm.tsx	(rejected hunks)
@@ -1,234 +1,354 @@
-
 import { useState, useEffect, useMemo } from "react";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Button } from "@/components/ui/button";
 import { useToast } from "@/hooks/use-toast";
-import SimpleSelect from "@/components/common/SimpleSelect";
+import SearchableSelect from "@/components/common/SearchableSelect";
 import SimpleInput from "@/components/common/SimpleInput";
 import CurrencyInput from "@/components/common/CurrencyInput";
-import { getFacilities, addPayment } from "@/utils/storage";
+import { Input } from "@/components/ui/input";
+import { getFacilities, addPayment, updatePayment } from "@/utils/storage";
 import { formatCurrency } from "@/utils/formatters";
-import { Facility, PaymentFormData } from "@/types/types";
+import { Facility, PaymentFormData, Payment } from "@/types/types";
+import { toEnglishDigits } from "@/utils/formatters";
 
 interface SimplePaymentFormProps {
   onBack: () => void;
   onSuccess?: () => void;
+  payment?: Payment;
 }
 
-const SimplePaymentForm = ({ onBack, onSuccess }: SimplePaymentFormProps) => {
+const SimplePaymentForm = ({
+  onBack,
+  onSuccess,
+  payment,
+}: SimplePaymentFormProps) => {
   const { toast } = useToast();
   const [facilities, setFacilities] = useState<Facility[]>([]);
-  const [formData, setFormData] = useState<PaymentFormData>({
-    facilityId: "",
-    installmentNumber: "",
-    paymentDate: "",
-    principalAmount: "",
-    interestAmount: "",
-    penaltyAmount: "",
-    notes: ""
-  });
+  const [formData, setFormData] = useState<PaymentFormData>(() => ({
+    facilityId: payment ? `${payment.facilityId}|` : "",
+    installmentNumber: payment?.installmentNumber?.toString() || "",
+    paymentDate: payment?.paymentDate || "",
+    principalAmount: payment?.principalAmount?.toString() || "",
+    interestAmount: payment?.interestAmount?.toString() || "",
+    penaltyAmount: payment?.penaltyAmount?.toString() || "",
+    notes: payment?.notes || "",
+  }));
 
   useEffect(() => {
     setFacilities(getFacilities());
   }, []);
 
-  const facilityOptions = useMemo(() => 
-    facilities.map(f => `${f.id}|${f.type} - ${f.bankName} - ${f.contractNumber}`),
-    [facilities]
+  // پس از بارگذاری لیست تسهیلات، عنوان انتخاب شده را تکمیل کن
+  useEffect(() => {
+    if (payment && facilities.length > 0 && formData.facilityId.endsWith('|')) {
+      const facility = facilities.find((f) => f.id === payment.facilityId);
+      if (facility) {
+        setFormData((prev) => ({
+          ...prev,
+          facilityId: `${facility.id}|${facility.bankName} - ${facility.contractNumber}`,
+        }));
+      }
+    }
+  }, [facilities, payment]);
+
+  const facilityOptions = useMemo(
+    () => facilities.map((f) => `${f.id}|${f.bankName} - ${f.contractNumber}`),
+    [facilities],
   );
 
   const selectedFacility = useMemo(() => {
     if (!formData.facilityId) return null;
-    const facilityId = parseInt(formData.facilityId.split('|')[0]);
-    return facilities.find(f => f.id === facilityId) || null;
+    const facilityId = parseInt(formData.facilityId.split("|")[0]);
+    return facilities.find((f) => f.id === facilityId) || null;
   }, [formData.facilityId, facilities]);
 
   const installmentOptions = useMemo(() => {
     if (!selectedFacility) return [];
-    return Array.from({ length: selectedFacility.installmentCount }, (_, i) => `${i + 1}`);
+    return Array.from(
+      { length: selectedFacility.installmentCount },
+      (_, i) => `${i + 1}`,
+    );
   }, [selectedFacility]);
 
   const updateField = (field: keyof PaymentFormData, value: string) => {
-    setFormData(prev => ({ ...prev, [field]: value }));
+    setFormData((prev) => ({ ...prev, [field]: value }));
   };
 
   const totalAmount = useMemo(() => {
     const principal = parseInt(formData.principalAmount) || 0;
     const interest = parseInt(formData.interestAmount) || 0;
     const penalty = parseInt(formData.penaltyAmount) || 0;
     return principal + interest + penalty;
-  }, [formData.principalAmount, formData.interestAmount, formData.penaltyAmount]);
+  }, [
+    formData.principalAmount,
+    formData.interestAmount,
+    formData.penaltyAmount,
+  ]);
+
+  const parseDate = (value: string): Date | null => {
+    const [y, m, d] = toEnglishDigits(value).split("/").map(Number);
+    if (!y || !m || !d) return null;
+    return new Date(y, m - 1, d);
+  };
+
+  const paymentDiff = useMemo(() => {
+    if (!formData.paymentDate || !selectedFacility || !formData.installmentNumber)
+      return null;
+    const installmentIdx = parseInt(formData.installmentNumber) - 1;
+    const installment = selectedFacility.installments?.[installmentIdx];
+    if (!installment) return null;
+    const payDate = parseDate(formData.paymentDate);
+    const dueDate = parseDate(installment.dueDate);
+    if (!payDate || !dueDate) return null;
+    const diff = Math.floor(
+      (payDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24),
+    );
+    return diff;
+  }, [formData.paymentDate, formData.installmentNumber, selectedFacility]);
 
   const handleSubmit = (e: React.FormEvent) => {
     e.preventDefault();
-    
-    if (!formData.facilityId || !formData.installmentNumber || !formData.paymentDate) {
+
+    if (
+      !formData.facilityId ||
+      !formData.installmentNumber ||
+      !formData.paymentDate
+    ) {
       toast({
         title: "خطا",
         description: "لطفاً تمام فیلدهای ضروری را پر کنید",
         variant: "destructive",
       });
       return;
     }
 
+    if (!/^\d{4}\/\d{2}\/\d{2}$/.test(formData.paymentDate)) {
+      toast({
+        title: "خطا",
+        description: "تاریخ پرداخت باید در قالب YYYY/MM/DD باشد",
+        variant: "destructive",
+      });
+      return;
+    }
+
     if (totalAmount <= 0) {
       toast({
         title: "خطا",
         description: "مبلغ پرداختی باید بیشتر از صفر باشد",
         variant: "destructive",
       });
       return;
     }
 
     try {
-      const facilityId = parseInt(formData.facilityId.split('|')[0]);
-      
-      addPayment({
-        facilityId,
-        installmentNumber: parseInt(formData.installmentNumber),
-        paymentDate: formData.paymentDate,
-        principalAmount: parseInt(formData.principalAmount) || 0,
-        interestAmount: parseInt(formData.interestAmount) || 0,
-        penaltyAmount: parseInt(formData.penaltyAmount) || 0,
-        totalAmount,
-        notes: formData.notes
-      });
+      const facilityId = parseInt(formData.facilityId.split("|")[0]);
 
-      toast({
-        title: "موفقیت",
-        description: "پرداخت با موفقیت ثبت شد",
-      });
+      if (payment) {
+        updatePayment(payment.id, {
+          facilityId,
+          installmentNumber: parseInt(formData.installmentNumber),
+          paymentDate: formData.paymentDate,
+          principalAmount: parseInt(formData.principalAmount) || 0,
+          interestAmount: parseInt(formData.interestAmount) || 0,
+          penaltyAmount: parseInt(formData.penaltyAmount) || 0,
+          totalAmount,
+          notes: formData.notes,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "پرداخت با موفقیت ویرایش شد",
+        });
+      } else {
+        addPayment({
+          facilityId,
+          installmentNumber: parseInt(formData.installmentNumber),
+          paymentDate: formData.paymentDate,
+          principalAmount: parseInt(formData.principalAmount) || 0,
+          interestAmount: parseInt(formData.interestAmount) || 0,
+          penaltyAmount: parseInt(formData.penaltyAmount) || 0,
+          totalAmount,
+          notes: formData.notes,
+        });
+
+        toast({
+          title: "موفقیت",
+          description: "پرداخت با موفقیت ثبت شد",
+        });
+      }
 
       if (onSuccess) onSuccess();
       else onBack();
     } catch (error) {
       toast({
         title: "خطا",
         description: "خطا در ثبت پرداخت",
         variant: "destructive",
       });
     }
   };
 
   return (
     <div className="space-y-6">
       <div className="flex items-center justify-between">
-        <h2 className="text-2xl font-bold">ثبت پرداخت</h2>
+        <h2 className="text-2xl font-bold">
+          {payment ? "ویرایش پرداخت" : "ثبت پرداخت"}
+        </h2>
         <Button variant="outline" onClick={onBack}>
           بازگشت
         </Button>
       </div>
 
       <Card>
         <CardHeader>
           <CardTitle>اطلاعات پرداخت</CardTitle>
         </CardHeader>
         <CardContent>
           <form onSubmit={handleSubmit} className="space-y-6">
-            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
-              <SimpleSelect
+            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
+              <SearchableSelect
                 label="انتخاب تسهیلات"
                 value={formData.facilityId}
                 onValueChange={(value) => {
-                  updateField('facilityId', value);
-                  updateField('installmentNumber', '');
+                  updateField("facilityId", value);
+                  updateField("installmentNumber", "");
                 }}
                 options={facilityOptions}
-                placeholder={facilities.length === 0 ? "هیچ تسهیلاتی وجود ندارد" : "انتخاب کنید"}
+                placeholder={
+                  facilities.length === 0
+                    ? "هیچ تسهیلاتی وجود ندارد"
+                    : "انتخاب کنید"
+                }
                 required
               />
 
-              <SimpleSelect
+              <SearchableSelect
                 label="شماره قسط"
                 value={formData.installmentNumber}
-                onValueChange={(value) => updateField('installmentNumber', value)}
+                onValueChange={(value) =>
+                  updateField("installmentNumber", value)
+                }
                 options={installmentOptions}
-                placeholder={!selectedFacility ? "ابتدا تسهیلات را انتخاب کنید" : "انتخاب کنید"}
+                placeholder={
+                  !selectedFacility
+                    ? "ابتدا تسهیلات را انتخاب کنید"
+                    : "انتخاب کنید"
+                }
                 required
               />
 
-              <SimpleInput
-                label="تاریخ پرداخت"
-                value={formData.paymentDate}
-                onChange={(value) => updateField('paymentDate', value)}
-                type="date"
-                required
-              />
+              <div className="space-y-2">
+                <label className="text-sm font-medium">تاریخ پرداخت *</label>
+                <Input
+                  value={formData.paymentDate}
+                  onChange={(e) => updateField("paymentDate", e.target.value)}
+                  placeholder="YYYY/MM/DD"
+                  pattern="\d{4}/\d{2}/\d{2}"
+                  required
+                />
+                {paymentDiff !== null && (
+                  <p
+                    className={`text-sm mt-1 ${
+                      paymentDiff > 0
+                        ? 'text-red-600'
+                        : paymentDiff < 0
+                        ? 'text-green-600'
+                        : 'text-gray-500'
+                    }`}
+                  >
+                    {paymentDiff > 0
+                      ? `${paymentDiff} روز دیرتر`
+                      : paymentDiff < 0
+                      ? `${Math.abs(paymentDiff)} روز زودتر`
+                      : 'در تاریخ مقرر'}
+                  </p>
+                )}
+              </div>
 
               <div></div>
 
               <CurrencyInput
                 label="مبلغ اصل (ریال)"
                 value={formData.principalAmount}
-                onChange={(value) => updateField('principalAmount', value)}
+                onChange={(value) => updateField("principalAmount", value)}
               />
 
               <CurrencyInput
                 label="مبلغ سود (ریال)"
                 value={formData.interestAmount}
-                onChange={(value) => updateField('interestAmount', value)}
+                onChange={(value) => updateField("interestAmount", value)}
               />
 
               <CurrencyInput
                 label="مبلغ جریمه (ریال)"
                 value={formData.penaltyAmount}
-                onChange={(value) => updateField('penaltyAmount', value)}
+                onChange={(value) => updateField("penaltyAmount", value)}
               />
 
               <div className="space-y-2">
                 <label className="text-sm font-medium">مجموع مبلغ</label>
                 <div className="p-3 bg-gray-50 rounded-md text-lg font-semibold">
                   {formatCurrency(totalAmount)} ریال
                 </div>
               </div>
 
               <div className="md:col-span-2">
                 <SimpleInput
                   label="یادداشت"
                   value={formData.notes}
-                  onChange={(value) => updateField('notes', value)}
+                  onChange={(value) => updateField("notes", value)}
                   placeholder="توضیحات اضافی"
                 />
               </div>
             </div>
 
             {selectedFacility && (
               <div className="p-4 bg-blue-50 rounded-lg">
-                <h4 className="font-semibold mb-2">اطلاعات تسهیلات انتخاب شده:</h4>
+                <h4 className="font-semibold mb-2">
+                  اطلاعات تسهیلات انتخاب شده:
+                </h4>
                 <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                   <div>
                     <span className="text-gray-600">نوع: </span>
-                    <span className="font-semibold">{selectedFacility.type}</span>
+                    <span className="font-semibold">
+                      {selectedFacility.type}
+                    </span>
                   </div>
                   <div>
                     <span className="text-gray-600">بانک: </span>
-                    <span className="font-semibold">{selectedFacility.bankName}</span>
+                    <span className="font-semibold">
+                      {selectedFacility.bankName}
+                    </span>
                   </div>
                   <div>
                     <span className="text-gray-600">مبلغ کل: </span>
-                    <span className="font-semibold">{formatCurrency(selectedFacility.amount)} ریال</span>
+                    <span className="font-semibold">
+                      {formatCurrency(selectedFacility.amount)} ریال
+                    </span>
                   </div>
                   <div>
                     <span className="text-gray-600">نرخ سود: </span>
-                    <span className="font-semibold">{selectedFacility.interestRate}%</span>
+                    <span className="font-semibold">
+                      {selectedFacility.interestRate}%
+                    </span>
                   </div>
                 </div>
               </div>
             )}
 
             <div className="flex justify-end space-x-2 space-x-reverse">
               <Button type="button" variant="outline" onClick={onBack}>
                 لغو
               </Button>
               <Button type="submit">
-                ثبت پرداخت
+                {payment ? "ذخیره تغییرات" : "ثبت پرداخت"}
               </Button>
             </div>
           </form>
         </CardContent>
       </Card>
     </div>
   );
 };
 
 export default SimplePaymentForm;
