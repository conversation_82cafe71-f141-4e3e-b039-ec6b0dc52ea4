diff a/src/types/types.ts b/src/types/types.ts	(rejected hunks)
@@ -1,52 +1,54 @@
 
 // تعریف انواع داده‌ها
+import { Installment } from './facility';
+
 export interface Facility {
   id: number;
   type: string;
   amount: number;
   bankName: string;
   contractNumber: string;
   installmentCount: number;
   interestRate: number;
   penaltyRate: number;
   installmentAmount: number;
   receivedDate: string;
   firstInstallmentDate: string;
+  installments?: Installment[];
   createdAt: string;
 }
 
 export interface Payment {
   id: number;
   facilityId: number;
   installmentNumber: number;
   paymentDate: string;
   principalAmount: number;
   interestAmount: number;
   penaltyAmount: number;
   totalAmount: number;
   notes?: string;
   createdAt: string;
 }
 
 export interface FacilityFormData {
   type: string;
   amount: string;
   bankName: string;
   contractNumber: string;
   installmentCount: string;
   interestRate: string;
   penaltyRate: string;
-  installmentAmount: string;
   receivedDate: string;
   firstInstallmentDate: string;
 }
 
 export interface PaymentFormData {
   facilityId: string;
   installmentNumber: string;
   paymentDate: string;
   principalAmount: string;
   interestAmount: string;
   penaltyAmount: string;
   notes: string;
 }
